FROM node:20-alpine

WORKDIR /app

# 複製必要檔案
COPY package.json package-lock.json tsconfig.json ./
COPY prisma ./prisma
COPY src/ ./src
COPY scripts/wait-for-it.sh scripts/entrypoint.sh ./

RUN npm install

# 產生 Prisma Client
RUN npx prisma generate

# 確保 wait-for-it 可執行
# windows 中, 就算系統儲存檔案格式轉換成LF,在build時chmod還是會有問題, 所以後續執行一律加上sh
RUN chmod +x wait-for-it.sh entrypoint.sh

# 開發環境：啟動前先等 DB → migrate → seed → dev server
ENTRYPOINT ["sh","entrypoint.sh"]
