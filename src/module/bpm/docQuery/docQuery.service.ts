import { SignStatus } from "@prisma/client";
import { DocQueryRepository } from "./docQuery.repository";
import { DocFileRepository } from "@/module/bpm/files/file.repository";
import { DocListQuery, DocWithFileAndTask, DocWithTaskSign } from "../domain/schema/query.schema";
import { TaskSign } from "../domain/TaskSign";

export class DocQueryService {
    constructor(
        private readonly docQueryRepo: DocQueryRepository,
        private readonly fileRepo: DocFileRepository
    ) { }

    async listQuery(filter: DocListQuery): Promise<DocWithTaskSign[]> {
        return await this.docQueryRepo.listDocByQuery(filter)
    }

    async listMySignDoc(
        userId: string,
        filter: DocListQuery
    ): Promise<DocWithTaskSign[]> {
        filter.signerId = userId
        filter.filterDraftSign = true
        return this.docQueryRepo.listDocByQuery(filter)
    }

    async listTodoDoc(userId: string, filter: DocListQuery): Promise<DocWithTaskSign[]> {
        filter.signerId = userId
        filter.signStatus = filter.signStatus ?? [SignStatus.ready] as SignStatus[]
        return this.docQueryRepo.listDocByQuery(filter)
    }

    async listMyApplyDoc(userId: string, filter: DocListQuery): Promise<DocWithTaskSign[]> {
        filter.authorId = userId
        return this.docQueryRepo.listDocByQuery(filter)
    }

    async listMyAllDoc(userId: string, filter: DocListQuery): Promise<DocWithTaskSign[]> {
        filter.authorId = userId
        filter.signerId = userId
        return this.docQueryRepo.listMyAllDoc(filter)
    }

    async getDocById(docId: string): Promise<DocWithFileAndTask> {
        const files = await this.fileRepo.list({ docId, taskId: null })
        const doc = await this.docQueryRepo.findDocById(docId)
        const result={
            ...doc,
            files,
        }
        return result
    }
    // history
    async listMyHistoryDoc(docId: string) {
        return this.docQueryRepo.listHistoryByDocId(docId)
    }
    //signTask
    async listTaskSign(docId: string): Promise<TaskSign[]> {
        return this.docQueryRepo.listTaskSignByDocId(docId)
    }
}