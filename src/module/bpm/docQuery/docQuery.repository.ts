import { <PERSON><PERSON><PERSON>, Prisma, PrismaClient, SignAction, SignStatus, SignTaskMode } from "@prisma/client"
import { DocStatus } from "@prisma/client";
import { CatchRepositoryError } from "@/utils/repoDecorator";
import { DocHistoryMapper } from "../domain/mapper/history.mapper";
import { DocListQuery, DocWithTaskSign } from "../domain/schema/query.schema";
import { DocMapper } from "../domain/mapper/doc.mapper";
import { DocHistory } from "../domain/DocHistory";
import { TaskSign } from "../domain/TaskSign";
import { TaskSignMapper } from "../domain/mapper/taskSign.mapper";

export class DocQueryRepository {
    constructor(private readonly prisma: PrismaClient) { }
    @CatchRepositoryError()
    async findDocById(docId: string, tx?: Prisma.TransactionClient): Promise<DocWithTaskSign> {
        const db = tx ?? this.prisma
        const result = await db.doc.findUniqueOrThrow({
            where: { id: docId },
            include: {
                task_signs: {
                    orderBy: [{ position: 'asc' }, { created_at: 'asc' }]
                }
            },
        })
        return DocMapper.toDocPropsWithTaskSign(result)
    }
    @CatchRepositoryError()
    async listDocByQuery(
        query: DocListQuery,
        tx?: Prisma.TransactionClient):Promise<DocWithTaskSign[]> {
        const db = tx ?? this.prisma
        const { id, authorId, signerId, name, docStatus, signStatus, signActions, filterDraftSign } = query
        const result = await db.doc.findMany({
            where: {
                ...(id && { id }),
                ...(name && { name }),
                ...(docStatus && { status: { in: docStatus as DocStatus[] } }),
                ...(authorId && { author_id: authorId }),
                task_signs: {
                    some: {
                        ...(signerId && { operator_id: signerId }),
                        ...(filterDraftSign && { position: { not: 0 } }),
                        status: { in: signStatus as SignStatus[] || [SignStatus.ready, SignStatus.signed] },
                        action: { in: signActions as SignAction[] || Object.keys(SignAction) }
                    },
                }
            },
            include: {
                task_signs: {
                    orderBy: [{ position: 'asc' as const }, { created_at: 'asc' as const }]
                }
            },
            orderBy: [{ created_at: 'desc' }]
        })
        return result.map(r => DocMapper.toDocPropsWithTaskSign(r))
    }
    @CatchRepositoryError()
    async listMyAllDoc(query: DocListQuery, tx?: Prisma.TransactionClient):Promise<DocWithTaskSign[]>{
        const db = tx ?? this.prisma
        const { authorId, signerId, name, docStatus, signModes, signStatus, signActions: signAction } = query
        const result = await db.doc.findMany({
            where: {
                OR: [
                    { author_id: authorId },
                    {
                        task_signs: {
                            some: {
                                operator_id: signerId,
                                mode: { in: signModes as SignTaskMode[] || Object.keys(SignTaskMode) },
                                status: { in: signStatus as SignStatus[] || [SignStatus.ready, SignStatus.signed] },
                                action: { in: signAction as SignAction[] || Object.keys(SignAction) }
                            }
                        }
                    }
                ],
                ...(name && { name }),
                ...(docStatus && { status: { in: docStatus as DocStatus[] } }),
            },
            include: {
                task_signs: {
                    orderBy: [{ position: 'asc' as const }, { created_at: 'asc' as const }]
                }
            },
            orderBy: [{ created_at: 'desc' }]
        })
        return result.map(r => DocMapper.toDocPropsWithTaskSign(r))
    }
    @CatchRepositoryError()
    async listTodoDoc(userId: string, tx?: Prisma.TransactionClient):Promise<DocWithTaskSign[]>{
        const db = tx ?? this.prisma
        const result = await db.doc.findMany({
            where: { author_id: userId, status: DocStatus.in_progress },
            include: {
                task_signs: {
                    where: {
                        operator_id: userId,
                        status: SignStatus.ready,
                    },
                    orderBy: [{ position: 'asc' }, { created_at: 'asc' }] 
                }
            },
            orderBy: [{ created_at: 'desc' }]
        })
        return result.map(r => DocMapper.toDocPropsWithTaskSign(r))
    }
    @CatchRepositoryError()
    async listHistoryByDocId(docId: string, tx?: Prisma.TransactionClient):Promise<DocHistory[]> {
        const db = tx ?? this.prisma
        const result = await db.doc_history.findMany({
            where: {
                doc_id: docId,
                NOT: { action: HistoryAction.undo }
            },
            orderBy: [{ timestamp: 'desc' }]
        })
        return result.map(r => DocHistoryMapper.toDomain(r))
    }
    @CatchRepositoryError()
    async listTaskSignByDocId(docId: string, tx?: Prisma.TransactionClient):Promise<TaskSign[]> {
        const db = tx ?? this.prisma
        const result = await db.task_sign.findMany({
            where: { doc_id: docId },
            orderBy: [{ position: 'asc' }, { created_at: 'asc' }]
        })
        return result.map(e=>TaskSignMapper.toDomain(e))
    }
}