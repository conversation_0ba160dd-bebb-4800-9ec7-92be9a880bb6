import { jwtAuthMiddleware } from "@/middleware/auth";
import { openApiMiddleware } from "@/middleware/openapi";
import { Hono } from "hono";

import { DocQueryService } from "./docQuery.service";
import { validator } from "hono-openapi/zod";

import { createListResponseSchema, successBase } from "@/module/common/schema";
import {  DocListQuerySchema, DocWithFileAndTaskSchema, DocWithTaskSignSchema } from "../domain/schema/query.schema";
import { DocHistoryPropsSchema, TaskSignPropsSchema } from "../domain/schema/domain.schema";

export function createDocQueryRouter(
  docQueryService: DocQueryService,
) {
  const router = new Hono()
  router.use('*', jwtAuthMiddleware)
  const spec = openApiMiddleware("Doc", ['JwtKeyAuth']);

  /**
   * GET doc?query                    list docs
   * GET user-doc/:userId/my-sign     list my sign docs
   * GET user-doc/:userId/my-todo     list my todo docs
   * GET user-doc/:userId/my-apply    list my apply docs
   * GET doc/:docId                   get doc
   * GET doc/:docId/history           list doc history
   * GET doc/:docId/task-sign         list doc sign graph
   */
  router.get('/doc',
    spec(createListResponseSchema(DocWithFileAndTaskSchema), 'List of docs'),
    validator('query', DocListQuerySchema),
    async (c) => {
      const { authorId, signerId, name, filterDraftSign } = c.req.query();
      const { docStatus, signStatus, signActions } = c.req.queries();
      const filter = { authorId, signerId, name, filterDraftSign, docStatus, signStatus, signActions }
      const validated = DocListQuerySchema.parse(filter)
      const docs = await docQueryService.listQuery(validated);
      return c.json({
        success: true,
        total: docs.length,
        data: docs,
      });
    });

  router.get('/user-doc/:userId/my-sign',
    spec(createListResponseSchema(DocWithTaskSignSchema), 'List my sign docs'),
    validator('query', DocListQuerySchema.pick({
      name: true,
      docStatus: true,
      signModes:true,
      signStatus: true,
      signActions: true
    })),
    async (c) => {
      const userId = c.req.param('userId');
      const { name } = c.req.query();
      const { docStatus, signStatus, signAction } = c.req.queries();
      const filter = { name, docStatus, signStatus, signAction }
      const validated = DocListQuerySchema.parse(filter)
      const docs = await docQueryService.listMySignDoc(userId, validated);
      return c.json({
        success: true,
        total: docs.length,
        data: docs,
      });
    });

  router.get('/user-doc/:userId/my-todo',
    spec(createListResponseSchema(DocWithTaskSignSchema), 'List my todo docs'),
    validator('query', DocListQuerySchema.pick({
      name: true,
      docStatus: true,
      signModes: true,
      signStatus: true,
      signActions: true
    })),
    async (c) => {
      const userId = c.req.param('userId');
      const { name } = c.req.query();
      const { docStatus, signStatus, signAction } = c.req.queries();
      const filter = { name, docStatus, signStatus, signAction }
      const validated = DocListQuerySchema.parse(filter)
      const docs = await docQueryService.listTodoDoc(userId, validated);
      return c.json({
        success: true,
        total: docs.length,
        data: docs,
      });
    });

  router.get('/user-doc/:userId/my-apply',
    spec(createListResponseSchema(DocWithTaskSignSchema), 'List my apply docs'),
    validator('query', DocListQuerySchema.pick({
      name: true,
      docStatus: true
    })),
    async (c) => {
      const userId = c.req.param('userId');
      const { name } = c.req.query();
      const { docStatus } = c.req.queries();
      const filter = { name, docStatus }
      const validated = DocListQuerySchema.parse(filter)
      const docs = await docQueryService.listMyApplyDoc(userId, validated);
      return c.json({
        success: true,
        total: docs.length,
        data: docs,
      });
    });

  router.get('/user-doc/:userId/my-all',
    spec(createListResponseSchema(DocWithTaskSignSchema), 'List my apply docs'),
    validator('query', DocListQuerySchema.pick({
      name: true,
      docStatus: true,
      signModes:true,
      signStatus: true,
      signActions: true
    })),
    async (c) => {
      const userId = c.req.param('userId');
      const { name } = c.req.query();
      const { docStatus, signStatus, signAction } = c.req.queries();
      const filter = { name, docStatus, signStatus, signAction }
      const validated = DocListQuerySchema.parse(filter)
      const docs = await docQueryService.listMyAllDoc(userId, validated);
      return c.json({
        success: true,
        total: docs.length,
        data: docs,
      });
    });



  router.get('/doc/:docId',
    spec(successBase(DocWithFileAndTaskSchema), 'Get doc by id'),
    async (c) => {
      const docId = c.req.param('docId');
      const doc = await docQueryService.getDocById(docId);
      return c.json({
        success: true,
        data: doc
      });
    });

  router.get('/doc/:docId/history',
    spec(createListResponseSchema(DocHistoryPropsSchema), 'List doc history'),
    async (c) => {
      const docId = c.req.param('docId');
      const history = await docQueryService.listMyHistoryDoc(docId);
      return c.json({
        success: true,
        data: history.map(d=>d.getProps()),
        total: history.length,
      });
    });

  router.get('/doc/:docId/task-sign',
    spec(createListResponseSchema(TaskSignPropsSchema), 'List doc sign graph'),
    async (c) => {
      const docId = c.req.param('docId');
      const signs = await docQueryService.listTaskSign(docId);
      return c.json({
        success: true,
        total: signs.length,
        data: signs.map(d=>d.getProps()),
      });
    });
  return router
}
