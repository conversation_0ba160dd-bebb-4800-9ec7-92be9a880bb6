import { DocSpecRepository } from "./docSpec.repository";
import { genDocSpecId } from "@/utils/genId";
import { DocSpecListQuery } from "../domain/schema/query.schema";
import { DocSpec } from "../domain/DocSpec";
import { DocSpecProps } from "../domain/schema/domain.schema";

export class DocSpecService {
    constructor(
        private readonly docSpecRepo: DocSpecRepository,
    ) { }

    async find(id: string): Promise<DocSpec> {
        const spec = await this.docSpecRepo.find(id)
        return spec
    }

    async list(query: DocSpecListQuery): Promise<DocSpec[]> {
        const list = await this.docSpecRepo.list(query)
        return list
    }

    async create(data: Omit<DocSpecProps, 'id'>): Promise<DocSpec> {
        const props = { ...data, id: genDocSpecId() } as DocSpecProps
        const domain = await this.docSpecRepo.create(props)
        return domain
    }

    async update(id: string, data: Partial<DocSpecProps>): Promise<DocSpec> {
        const props = await this.docSpecRepo.update(id, data)
        return props
    }

    async delete(id: string): Promise<void> {
        await this.docSpecRepo.delete(id)
    }
}