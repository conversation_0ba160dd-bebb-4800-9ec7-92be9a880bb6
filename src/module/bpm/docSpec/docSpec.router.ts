import { jwtAuthMiddleware } from "@/middleware/auth";
import { openApiMiddleware } from "@/middleware/openapi";
import { Context, Hono } from "hono";
import { validator } from "hono-openapi/zod";
import { createListResponseSchema, successBase, SuccessResponseSchema } from "@/module/common/schema";
import { DocSpecService } from "./docSpec.service";
import { z } from "@hono/zod-openapi";
import { DocSpecPkSchema, DocSpecPropsSchema } from "../domain/schema/domain.schema";
import { DocSpecListQuerySchema } from "../domain/schema/query.schema";

export function createDocSpecRouter(
    docSpecService: DocSpecService,
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const spec = openApiMiddleware("DocSpec", ['JwtKeyAuth']);
    /*
    * GET /                         'list spec'
    * POST /                        'Create spec'
    * GET /:specId                  'Get spec by id'
    * PATCH /:specId                'Edit spec'
    * DELETE /:specId               'Delete spec'
    * POST /:specId/create-draft    'Create Draft'
    */
    // spec
    router.get('/',
        spec(createListResponseSchema(DocSpecPropsSchema), 'list doc spec'),
        validator('query', DocSpecListQuerySchema),
        async (c: Context) => {
            const filter = c.req.query()
            const validated = DocSpecListQuerySchema.parse(filter)
            const resp = await docSpecService.list(validated);
            return c.json({ success: true, total: resp.length, data: resp.map(domain=>domain.getProps()) });
        }
    );

    router.get('/:specId',
        spec(successBase(DocSpecPropsSchema), 'Get doc spec by id'),
        validator('param',  z.object({ specId: DocSpecPkSchema })),
        async (c: Context) => {
            const { specId } = c.req.param();
            const resp = await docSpecService.find(specId);
            return c.json({ success: true, data: resp.getProps() });
        }
    );

    router.post('/',
        spec(successBase(DocSpecPropsSchema), 'Create doc spec'),
        validator('json', DocSpecPropsSchema),
        async (c: Context) => {
            const data = await c.req.json();
            const resp = await docSpecService.create(data);
            return c.json({ success: true, data: resp.getProps() });
        }
    );
    router.put('/:specId',
        spec(successBase(DocSpecPropsSchema), 'Edit doc spec'),
        validator('param',  z.object({ specId: DocSpecPkSchema })),
        validator('json', DocSpecPropsSchema),
        async (c: Context) => {
            const { specId } = c.req.param();
            const data = await c.req.json();
            const resp = await docSpecService.update(specId, data);
            return c.json({ success: true, data: resp.getProps() });
        }
    );
    router.delete('/:specId',
        spec(SuccessResponseSchema, 'Delete doc spec'),
        validator('param',  z.object({ specId: DocSpecPkSchema })),
        async (c: Context) => {
            const { specId } = c.req.param();
            await docSpecService.delete(specId);
            return c.json({ success: true });
        }
    );

    return router;
}
