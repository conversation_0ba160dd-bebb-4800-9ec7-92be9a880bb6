import { DocSpecService } from '../docSpec.service'
import { DocSpecRepository } from '../docSpec.repository'
import { DocSpec } from '../../domain/DocSpec'
import { getPrisma } from '@/infra/db'
import { deepcopyBaseDocSpecProps } from '../../domain/__tests__/domain.mock'
jest.mock('../docSpec.repository')
jest.mock('../../docDraft/docDraft.repository')
jest.mock('../../common/common.repository')
jest.mock('@/infra/db')
jest.mock('nanoid', () => ({
    nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('DocSpecService', () => {
    let service: DocSpecService
    let docSpecRepo: jest.Mocked<DocSpecRepository>
    let mockPrisma: any

    beforeEach(() => {
        docSpecRepo = jest.mocked(new DocSpecRepository(getPrisma()))
        service = new DocSpecService(docSpecRepo)

        mockPrisma = {
            $transaction: jest.fn((callback) => callback(mockPrisma))
        }
        ;(getPrisma as jest.Mock).mockReturnValue(mockPrisma)
    })

    afterEach(() => {
        jest.clearAllMocks()
    })

    describe('find', () => {
        const mockDocSpec = new DocSpec(deepcopyBaseDocSpecProps())

        it('應該可以找到指定的文件規格', async () => {
            docSpecRepo.find.mockResolvedValue(mockDocSpec)

            const result = await service.find('spec-1')

            expect(docSpecRepo.find).toHaveBeenCalledWith('spec-1')
            expect(result).toEqual(mockDocSpec)
        })
    })

    describe('create', () => {
        const mockDocSpec = new DocSpec(deepcopyBaseDocSpecProps())

        it('應該可以創建新的文件規格', async () => {
            docSpecRepo.create.mockResolvedValue(mockDocSpec)
            const {id,...els}=deepcopyBaseDocSpecProps()
            const result = await service.create(els)

            expect(result).toEqual(mockDocSpec)
        })
    })

    describe('update', () => {
        const mockUpdatedDocSpec = new DocSpec(deepcopyBaseDocSpecProps())

        it('應該可以更新文件規格', async () => {
            docSpecRepo.update.mockResolvedValue(mockUpdatedDocSpec)

            const updateData = {
                label: '更新後的請假單',
                description: '更新後的說明'
            }

            const result = await service.update('spec-1', updateData)

            expect(docSpecRepo.update).toHaveBeenCalledWith('spec-1', updateData)
            expect(result).toEqual(mockUpdatedDocSpec)
        })
    })

    describe('delete', () => {
        it('應該可以刪除文件規格', async () => {
            docSpecRepo.delete.mockResolvedValue(undefined)

            await service.delete('spec-1')

            expect(docSpecRepo.delete).toHaveBeenCalledWith('spec-1')
        })
    })

    describe('list', () => {
        const mockDocSpecs = [
            new DocSpec({
                ...deepcopyBaseDocSpecProps(),
                id:'spec-1',
                label: '請假單',
            }),
            new DocSpec({
                ...deepcopyBaseDocSpecProps(),
                id: 'spec-2',
                label: '加班單',
            })
        ]

        it('應該可以列出所有文件規格', async () => {
            docSpecRepo.list.mockResolvedValue(mockDocSpecs)

            const result = await service.list({})

            expect(docSpecRepo.list).toHaveBeenCalledWith({})
            expect(result).toEqual(mockDocSpecs)
        })
    })
})