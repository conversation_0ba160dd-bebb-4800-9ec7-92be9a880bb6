import { <PERSON><PERSON><PERSON>, PrismaClient } from "@prisma/client";
import { CatchRepositoryError } from "@/utils/repoDecorator";
import { DocSpecMapper } from "../domain/mapper/docSpec.mapper";
import { DocSpecListQuery } from "../domain/schema/query.schema";
import { DocSpec } from "../domain/DocSpec";
import { DocSpecProps } from "../domain/schema/domain.schema";

export class DocSpecRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async list(query: DocSpecListQuery, tx?: Prisma.TransactionClient): Promise<DocSpec[]> {
        const db = tx ?? this.prisma
        const where = {
            ...(query.isActive !== undefined && { is_active: query.isActive }),
            ...(query.label && { label: { contains: query.label } }),
            ...(query.priority !== undefined && { priority: query.priority }),
            ...(query.category && { category: { contains: query.category } }),
            ...(query.id && { id: query.id })
        };
        const list = await db.doc_spec.findMany({
            where,
        });
        return list.map(spec => DocSpecMapper.toDomain(spec));
    }

    @CatchRepositoryError()
    async find(id: string, tx?: Prisma.TransactionClient): Promise<DocSpec> {
        const db = tx ?? this.prisma

        const result = await db.doc_spec.findUniqueOrThrow({
            where: { id },
        });
        return DocSpecMapper.toDomain(result);
    }

    @CatchRepositoryError()
    async create(props: DocSpecProps, tx?: Prisma.TransactionClient): Promise<DocSpec> {
        const db = tx ?? this.prisma
        const result = await db.doc_spec.create({
            data: DocSpecMapper.toPersistence(props)
        })
        return DocSpecMapper.toDomain(result)
    }

    @CatchRepositoryError()
    async update(id: string, data: Partial<Omit<DocSpecProps, 'id'>>, tx?: Prisma.TransactionClient): Promise<DocSpec> {
        const db = tx ?? this.prisma
        const result = await db.doc_spec.update({
            where: { id },
            data: DocSpecMapper.toPersistenceForUpdate(data)
        });
        return DocSpecMapper.toDomain(result);
    }

    @CatchRepositoryError()
    async delete(id: string, tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma
        await db.doc_spec.delete({
            where: { id }
        });
    }
}
