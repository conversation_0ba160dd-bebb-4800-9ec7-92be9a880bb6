import { OrganizationType } from "@prisma/client";
import { z } from "zod";
import { extendZodWithOpenApi } from 'zod-openapi';

extendZodWithOpenApi(z);
/**
 * DocSpec.Flow
 */
export enum FlowPartialMode {
    person = "person",
    org = "org",
    role = "role"
}
export const FlowPartialPersonSchema = z.object({
    mode: z.literal(FlowPartialMode.person),
    employeeId: z.string().openapi({ example: "FC0056" }),
})
export type FlowPartialPerson = z.infer<typeof FlowPartialPersonSchema>

export const FlowPartialOrgSchema = z.object({
    mode: z.literal(FlowPartialMode.org),
    orgId: z.string().openapi({ example: "dep-123" }),
    isManager: z.preprocess(
        (val) => {
            if (typeof val === 'boolean') return val;

            if (val === 'true') return true;
            if (val === 'false') return false;
            return val;
        },
        z.boolean(),
    ).openapi({ example: true }),
})
export type FlowPartialOrg = z.infer<typeof FlowPartialOrgSchema>

export const FlowPartialRoleSchema = z.object({
    mode: z.literal(FlowPartialMode.role),
    type: z.nativeEnum(OrganizationType).openapi({ example: OrganizationType.department }),
})
export type FlowPartialRole = z.infer<typeof FlowPartialRoleSchema>

export const SpecFlowNodeSchema = z.union([
    FlowPartialPersonSchema,
    FlowPartialOrgSchema,
    FlowPartialRoleSchema
])
export type FlowNumber = (FlowPartialPerson | FlowPartialOrg | FlowPartialRole) & { position: number }

export const SpecFlowSchema = z.array(
    z.union([
        SpecFlowNodeSchema,
        z.array(SpecFlowNodeSchema)
    ])
).openapi({
    example: [
        { mode: FlowPartialMode.person, employeeId: 'FC0056' },
        [
            { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
            { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: false }
        ],
        { mode: FlowPartialMode.role, type: 'department'/*部門主管*/ },
    ]
})
export type SpecFlow = z.infer<typeof SpecFlowSchema>


/**
 * Use Stamps
 */
export const StampsFlowSchema = z.array(FlowPartialOrgSchema).describe("一、二、三級用印(optional)")
    .openapi({
        example: [
            { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
            { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
            { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: true }
        ]
    })
export type StampsFlow = z.infer<typeof StampsFlowSchema>