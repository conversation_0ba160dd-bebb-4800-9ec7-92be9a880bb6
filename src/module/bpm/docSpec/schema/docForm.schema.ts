import { z } from "zod";
import { extendZod<PERSON>ith<PERSON>penApi } from 'zod-openapi';
extendZodWithOpenApi(z);
/**
 * DocSpec.Form
 */
export const SpecFormNodeSchema = z.object({
    key: z.string().openapi({ example: "budgetType" }),
    label: z.string().openapi({ example: "預算類別" }),
    conditional: z.string().openapi({ example: "required" }),
    inputType: z.string().openapi({ example: "number" }),
    validate: z.preprocess(
        (val) => {
            if (typeof val === 'boolean') return val;
            if (val === 'true') return true;
            if (val === 'false') return false;
            return val;
        },
        z.boolean(),
    ).openapi({ example: true }),
    hideLabel: z.preprocess(
        (val) => {
            if (typeof val === 'boolean') return val;
            if (val === 'true') return true;
            if (val === 'false') return false;
            return val;
        },
        z.boolean(),
    ).openapi({ example: true }),
    values: z.array(z.object({
        key: z.string().openapi({ example: "tools" }),
        label: z.string().openapi({ example: "工具" }),
    })).openapi({ example: [] }).optional()
})
export const SpecFormSchema = z.array(SpecFormNodeSchema).openapi({
    example: [
        { key: "budgetType", label: "預算類別", conditional: "required", inputType: "text", validate: true, hideLabel: false },
        { key: "budget", label: "預算金額", conditional: "required", inputType: "number", validate: true, hideLabel: false },
        { key: "reason", label: "申請原因", conditional: "required", inputType: "text", validate: true, hideLabel: false },
    ]
})
export type SpecForm = z.infer<typeof SpecFormSchema>

/**
 * DocDraft
 */
export const DocDraftFormSchema = z.object({
    topic: z.string().optional().openapi({ example: "test" }).describe("標題(optional)"),
    formData: z.record(z.any()).openapi({
        example:
        {
            "budgetType": "tools",
            "budget": 10000,
            "reason": "buy"
        }
    })
})
export type DraftFormData = z.infer<typeof DocDraftFormSchema>