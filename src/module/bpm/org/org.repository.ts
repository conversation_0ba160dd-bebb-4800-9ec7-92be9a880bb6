import { EmployeeStatus, Prisma, PrismaClient } from "@prisma/client";
import { CatchRepositoryError } from "@/utils/repoDecorator";
import { EmployeeDto, OrgDto } from "./org.schema";

export class OrgRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async listOrg(): Promise<OrgDto[]> {
        const orgs = await this.prisma.organization.findMany()
        const result = orgs.map(org => ({ id: org.id, name: org.name }))
        return result
    }

    @CatchRepositoryError()
    async listAssigemntByOrg(orgId: string, tx?: Prisma.TransactionClient): Promise<EmployeeDto[]> {
        const db = tx ?? this.prisma

        const allOrgs = await db.organization.findMany({
            select: { id: true, parent_id: true },
        });
        const orgIds = this.getDescendantOrgIds(orgId, allOrgs);
        const assigments = await db.employee_assignment.findMany({
            where: {
                org_id: { in: orgIds },
                employee: {
                    status: EmployeeStatus.active
                }
            },
            include: {
                employee: true,
                organization: true,
            },
            orderBy: [{ employee_id: 'asc' }],
            distinct: ['employee_id'],
        });
        return assigments.map((a) => this.toEmployeeDto(a.employee));
    }

    private toEmployeeDto(employee: any): EmployeeDto {
        return {
            id: employee.id,
            name: `${employee.first_name}${employee.last_name}`,
            title: `${employee.english_name}${employee.title === null ? '' : ` ${employee.english_name}`}`,
        }
    }

    // 遞迴查找所有下層 orgId
    private getDescendantOrgIds(orgId: string, orgs: { id: string; parent_id: string | null }[]): string[] {
        const result: string[] = [];
        const dfs = (id: string) => {
            result.push(id);
            for (const child of orgs.filter(o => o.parent_id === id)) {
                dfs(child.id);
            }
        };
        dfs(orgId);
        return result;
    }
}
