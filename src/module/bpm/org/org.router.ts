import { Hono } from "hono";
import { jwtAuthMiddleware } from "@/middleware/auth";
import { openApiMiddleware } from "@/middleware/openapi";
import { validator } from "hono-openapi/zod";
import { z } from "@hono/zod-openapi";
import { createListResponseSchema } from "../../common/schema";
import { EmployeeDtoSchema, OrgDtoSchema } from "./org.schema";
import { OrgRepository } from "./org.repository";

export function createOrgRouter(
    orgRepo: OrgRepository
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const spec = openApiMiddleware("DocOperator", ['JwtKeyAuth']);

    router.get('/org',
        spec(createListResponseSchema(OrgDtoSchema), 'list operators by org id'),
        async (c) => {
            const list = await orgRepo.listOrg()
            return c.json({ success: true, total: list.length, data: list })
        })

    router.get('/org/:orgId/employee',
        spec(createListResponseSchema(EmployeeDtoSchema), 'list operators by org id'),
        validator('param', z.object({ orgId: z.string() })),
        async (c) => {
            const { orgId } = c.req.param()
            const list = await orgRepo.listAssigemntByOrg(orgId)
            return c.json({ success: true, total: list.length, data: list })
        })
    return router
}