import { z } from "@hono/zod-openapi";
import { EmpPkSchema, EmpTitleSchema } from "../domain/schema/domain.schema";

export const EmployeeDtoSchema = z.object({
    id: EmpPkSchema,
    name: EmpTitleSchema,
    title: z.string().openapi({ example: 'Mike Executive' })
});
export type EmployeeDto = z.infer<typeof EmployeeDtoSchema>

export const OrgDtoSchema = z.object({
    id: z.string().openapi({ example: 'p_001' }),
    name: z.string().openapi({ example: '資訊部' })
});
export type OrgDto = z.infer<typeof OrgDtoSchema>