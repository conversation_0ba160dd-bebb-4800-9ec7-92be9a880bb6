import { CatchRepositoryError } from "@/utils/repoDecorator"
import { DocStatus, OrganizationType, Prisma, PrismaClient } from "@prisma/client"
import { DocDraft } from "../domain/DocDraft"
import { TaskSign } from "../domain/TaskSign"
import { TaskSignMapper } from "../domain/mapper/taskSign.mapper"
import { DocHistory } from "../domain/DocHistory"
import { DocHistoryMapper } from "../domain/mapper/history.mapper"
import { RepositoryError } from "@/errors/repo.error"
import { DocSpecMapper } from "../domain/mapper/docSpec.mapper"
import { DocSpec } from "../domain/DocSpec"
import { DocDraftMapper } from "../domain/mapper/docDraft.mapper"

export class DocDraftRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async listDraft(userId: string, tx?: Prisma.TransactionClient): Promise<DocDraft[]> {
        const db = tx ?? this.prisma
        const list = await db.doc.findMany({
            where: {
                author_id: userId,
                current_position: 0,
                status: 'draft'
            }
        })
        return list.map(r => DocDraftMapper.toDomain(r))
    }

    @CatchRepositoryError()
    async create(data: DocDraft, tx?: Prisma.TransactionClient): Promise<DocDraft> {
        const db = tx ?? this.prisma
        const result = await db.doc.create({
            data: DocDraftMapper.toPersistence(data.props)
        })

        return DocDraftMapper.toDomain(result)
    }

    @CatchRepositoryError()
    async find(id: string, tx?: Prisma.TransactionClient): Promise<DocDraft> {
        const db = tx ?? this.prisma

        const result = await db.doc.findUniqueOrThrow({
            where: { id, current_position: 0, status: DocStatus.draft },
        });
        return DocDraftMapper.toDomain(result);
    }

    @CatchRepositoryError()
    async update(id: string, data: DocDraft, tx?: Prisma.TransactionClient): Promise<DocDraft> {
        const db = tx ?? this.prisma
        const result = await db.doc.update({
            where: { id },
            data: DocDraftMapper.toPersistence(data.props)
        });
        return DocDraftMapper.toDomain(result);
    }

    @CatchRepositoryError()
    async delete(id: string, tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma
        await db.doc.delete({
            where: { id, current_position: 0, status: 'draft' }
        });
    }

    @CatchRepositoryError()
    async bulkCreateSigns(tasks: TaskSign[], tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma
        await db.task_sign.createMany({
            data: tasks.map(t => TaskSignMapper.toCreateMany(t.props)),
        });
    }

    @CatchRepositoryError()
    async bulkCreatHistory(histories: DocHistory[], tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma;
        await db.doc_history.createMany({
            data: histories.map(t => DocHistoryMapper.toCreateMany(t.props)),
        });
    }

    @CatchRepositoryError()
    async findSpecById(id: string, tx?: Prisma.TransactionClient): Promise<DocSpec> {
        const db = tx ?? this.prisma
        const result = await db.doc_spec.findUniqueOrThrow({
            where: { id },
        });
        return DocSpecMapper.toDomain(result);
    }

    @CatchRepositoryError()
    async getNextDocDailyCounter( // optimistic locking with retries
        YYYYMMDD: string,
        tx?: Prisma.TransactionClient,
        maxRetries = 5
    ): Promise<number> {
        const db = tx ?? this.prisma;
        for (let attempt = 0; attempt < maxRetries; attempt++) {

            // Step 1: 先查資料
            const record = await db.doc_daily_counter.findUnique({
                where: { id: YYYYMMDD },
            });

            // Step 2: 如果不存在，先嘗試 create（第一次進來的人）
            if (!record) {
                try {
                    const created = await db.doc_daily_counter.create({
                        data: {
                            id: YYYYMMDD,
                            count: 1,
                        },
                    });
                    return created.count;
                } catch (e) {
                    // 有人比你早 create，一樣往下 retry
                    continue;
                }
            }

            // Step 3: 嘗試更新（樂觀鎖：version match）
            const updated = await db.doc_daily_counter.updateMany({
                where: {
                    id: YYYYMMDD,
                    count: record.count, // 當作 version 使用
                },
                data: {
                    count: record.count + 1,
                },
            });

            // 成功只會更新 1 筆
            if (updated.count === 1) {
                return record.count + 1;
            }
            // 否則表示 version 衝突，retry
        }
        throw new RepositoryError('Retry failed', 'getNextDocDailyCounter failed after max retries')
    }
}
