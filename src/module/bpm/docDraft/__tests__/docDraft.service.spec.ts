import { deepcopyBaseDocProps } from '../../domain/__tests__/domain.mock'
import { DocDraftService } from '../docDraft.service'
import { DocDraftRepository } from '../docDraft.repository'
import { DocFileRepository } from '@/module/bpm/files/file.repository'
import { CommonRepository } from '../../common/common.repository'
import { TaskRepository } from '../../docAction/repository/task.repository'
import { DocDraft } from '../../domain/DocDraft'
import { PermissionError } from '@/errors/app.error'

import { getPrisma } from '@/infra/db'

jest.mock('@/infra/db')
jest.mock('nanoid', () => ({
    nanoid: () => 'this-is-a-fake-nano-id',
}))

describe('DocDraftService', () => {
    let service: DocDraftService
    let docDraftRepo: jest.Mocked<DocDraftRepository>
    let commonRepo: jest.Mocked<CommonRepository>
    let fileRepo: jest.Mocked<DocFileRepository>
    let taskRepo: jest.Mocked<TaskRepository>
    let mockPrisma: any

    beforeEach(() => {
        mockPrisma = {
            $transaction: jest.fn((callback) => callback(mockPrisma))
        }
        ;(getPrisma as jest.Mock).mockReturnValue(mockPrisma)
        docDraftRepo = {
            find: jest.fn(),
            listDraft: jest.fn(),
            update: jest.fn(),
            bulkCreateSigns: jest.fn(),
            bulkCreatHistory: jest.fn(),
            findFlowPartialRoleToEmployeeId: jest.fn(),
            findFlowPartialOrgToEmployeeId: jest.fn()
        } as any

        commonRepo = {
            userIdToTitle: jest.fn()
        } as any

        fileRepo = {
            list: jest.fn()
        } as any

        taskRepo = {
            findFirstOrThrow: jest.fn(),
            bulkUpdateTaskSign: jest.fn()
        } as any

        service = new DocDraftService(
            docDraftRepo,
            commonRepo,
            fileRepo
        )
    })

    describe('getDraftById', () => {
        const mockDocProps = { ...deepcopyBaseDocProps() }
        it('should return draft when user is author', async () => {
            docDraftRepo.find.mockResolvedValue(new DocDraft(mockDocProps))
            fileRepo.list.mockResolvedValue([])

            const result = await service.getDraftById(mockDocProps.authorId, mockDocProps.id)

            expect(docDraftRepo.find).toHaveBeenCalledWith(mockDocProps.id)
            expect(fileRepo.list).toHaveBeenCalledWith({ docId: mockDocProps.id, taskId: null })
            expect(result).toBeDefined()
            expect(result.id).toBe(mockDocProps.id)
        })

        it('should throw PermissionError when user is not author', async () => {
            docDraftRepo.find.mockResolvedValue(new DocDraft(mockDocProps))

            await expect(service.getDraftById('other-user', mockDocProps.id))
                .rejects
                .toThrow(PermissionError)

            expect(docDraftRepo.find).toHaveBeenCalledWith(mockDocProps.id)
        })
    })
})