import { jwtAuthMiddleware } from "@/middleware/auth";
import { openApiMiddleware } from "@/middleware/openapi";
import { Context, Hono } from "hono";
import { DocDraftService } from "./docDraft.service";
import { validator } from "hono-openapi/zod";
import { createListResponseSchema, successBase, SuccessResponseSchema } from "@/module/common/schema";
import { DocDraftFormSchema } from "../docSpec/schema/docForm.schema";
import { SpecFlowSchema, StampsFlowSchema } from "../docSpec/schema/docFlow.schema";
import { CreateDraftSchema, DocWithFileSchema } from "../domain/schema/query.schema";
import { z } from "@hono/zod-openapi";
import { extendZodWithOpenApi } from "@hono/zod-openapi";
import { DocPropsSchema, DocSpecPkSchema } from "../domain/schema/domain.schema";
import { DocActionService } from "../docAction/docAction.service";

extendZodWithOpenApi(z); // ⬅️ 必須呼叫一次！
export function createDocDraftRouter(
    docDraftService: DocDraftService,
    docActionService:DocActionService
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const spec = openApiMiddleware("DocDraft", ['JwtKeyAuth']);
    /**
     * GET doc-draft     list draft
     * POST doc-draft     create draft (add flow_draft)
     * GET doc-draft/:docId     get draft
     * DELETE doc-draft/:docId     delete draft
     * PATCH doc-draft/:docId/submit     submit draft (new signtask)
     * PATCH doc-draft/:docId/flow     edit draft flow
     */
    router.get('/',
        spec(createListResponseSchema(DocPropsSchema), 'list draft'),
        async (c: Context) => {
            const user = c.get('user');
            const docs = await docDraftService.listDraft(user.id);
            return c.json({ success: true, total: docs.length, data: docs.map(doc=>doc.props) });
        }
    );

    // create draft
    router.post('/create-draft/:specId',
        spec(SuccessResponseSchema, 'Create doc draft'),
        validator('param', z.object({ specId: DocSpecPkSchema })),
        validator('json', CreateDraftSchema),
        async (c: Context) => {
            const { specId } = c.req.param();
            const body = await c.req.json();
            const submit = CreateDraftSchema.parse(body)
            const user = c.get('user');
            const resp = await docDraftService.createDraft(user.id, specId, submit);
            return c.json({ success: true, data: resp.props });
        }
    );

    router.get('/:docId',
        spec(successBase(DocWithFileSchema), 'Get doc draft by id'),
        validator('param', z.object({ docId: z.string() })),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const doc = await docDraftService.getDraftById(user.id, docId);
            return c.json({ success: true, data: doc });
        }
    );

    router.patch('/:docId/form',
        spec(successBase(DocPropsSchema), 'Update doc draft data by id'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocDraftFormSchema),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const formData = await c.req.json();
            const doc = await docDraftService.updateFormData(user.id, docId, formData);
            return c.json({ success: true, data: doc.props });
        }
    );

    router.patch('/:docId/flow',
        spec(successBase(DocPropsSchema),
            'Update doc draft flow by id, \
        會簽(add)也加入到這 ` { mode: "person", employeeId: "FC0056" } 組裝flow`'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', SpecFlowSchema),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const flowDraft = await c.req.json();
            const doc = await docDraftService.updateFlowData(user.id, docId, flowDraft);
            return c.json({ success: true, data: doc.props });
        }
    );

    router.delete('/:docId',
        spec(SuccessResponseSchema, 'Delete doc draft by id'),
        validator('param', z.object({ docId: z.string() })),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const doc = await docDraftService.deleteDraft(user.id, docId);
            return c.json({ success: true });
        }
    );

    router.post('/:docId/submit',
        spec(successBase(DocPropsSchema), 'Submit doc draft by id'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', z.object({ stamps: StampsFlowSchema })),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const { stamps } = await c.req.json();
            const doc = await docActionService.submit(user.id, docId, stamps);
            return c.json({ success: true, data: doc.props });
        }
    );

    return router;
}
