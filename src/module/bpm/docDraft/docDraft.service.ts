import { DraftFormData } from "../docSpec/schema/docForm.schema";
import { DocDraftRepository } from "./docDraft.repository";
import { PermissionError } from "@/errors/app.error";
import { getPrisma } from "@/infra/db";
import { CommonRepository } from "../common/common.repository";
import { Prisma } from "@prisma/client";
import { DocFileRepository } from "@/module/bpm/files/file.repository";
import { DocDraft } from "../domain/DocDraft";
import { CreateDraft, DocWithFile } from "../domain/schema/query.schema";
import { DocMapper } from "../domain/mapper/doc.mapper";
import { SpecFlow } from "../docSpec/schema/docFlow.schema";

export class DocDraftService {
    constructor(
        private readonly docDraftRepo: DocDraftRepository,
        private readonly commonRepo: CommonRepository,
        private readonly fileRepo: DocFileRepository,
    ) { }

    async getDraftById(userId: string, docId: string): Promise<DocWithFile> {
        const doc = await this.docDraftRepo.find(docId)
        const files = await this.fileRepo.list({ docId, taskId: null })
        if (userId !== doc.props.authorId) throw new PermissionError('PermissionError', 'Only author can view.')
        return DocMapper.toResponseWithFiles(doc, files)
    }

    async listDraft(userId: string): Promise<DocDraft[]> {
        const result = await this.docDraftRepo.listDraft(userId)
        return result
    }

    async updateFormData(userId: string, docId: string, formData: DraftFormData): Promise<DocDraft> {
        const draft = await this.docDraftRepo.find(docId)
        if (userId !== draft.props.authorId) throw new PermissionError('PermissionError', 'Only author can edit.')
        draft.updateFormData(formData)
        const result = await this.docDraftRepo.update(draft.props.id, draft)
        return result
    }

    async updateFlowData(userId: string, docId: string, flowDraft: SpecFlow): Promise<DocDraft> {
        const draft = await this.docDraftRepo.find(docId)
        if (userId !== draft.props.authorId) throw new PermissionError('PermissionError', 'Only author can edit.')
        draft.updateFlowData(flowDraft)
        const result = await this.docDraftRepo.update(draft.props.id, draft)
        return result
    }

    async deleteDraft(userId: string, docId: string): Promise<void> {
        const draft = await this.docDraftRepo.find(docId)
        if (userId !== draft.props.authorId) throw new PermissionError('PermissionError', 'Only author can delete.')
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            draft.delete()
            await this.fileRepo.bulkDelete({ docId }, tx)
            await this.docDraftRepo.delete(draft.props.id, tx)
            // draft.commit()// 目前邏輯不用紀錄
        })
    }
    
    async createDraft(userId: string, specId: string, submit: CreateDraft): Promise<DocDraft> {
        const author = await this.commonRepo.userIdToTitle(userId)
        const spec = await this.docDraftRepo.findSpecById(specId)
        const prisma = getPrisma()
        const resp = await prisma.$transaction(async (tx) => {
            const draftId = await this.genDocId(submit.day, tx)
            const draft = DocDraft.specFactory(
                {
                    id: draftId,
                    name: submit.name,
                    authorId: author.id,
                    authorTitle: author.title,
                    formData: submit.data,
                    topic: submit.topic
                },
                spec.props
            )
            const docProps = await this.docDraftRepo.create(draft, tx)
            return docProps
        })
        return resp
    }

    private async genDocId(YYYYMMDD: string, tx: Prisma.TransactionClient): Promise<string> {
        const nextCounter = await this.docDraftRepo.getNextDocDailyCounter(YYYYMMDD, tx)
        if (nextCounter >= 1000) {
            throw new PermissionError('PermissionError', 'Exceeds the total application volume of documents in one day.')
        }
        const padded = String(nextCounter).padStart(3, '0');
        return `d${YYYYMMDD}${padded}`
    }
}