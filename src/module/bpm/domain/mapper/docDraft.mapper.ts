import { Prisma } from "@prisma/client"
import { Spec<PERSON><PERSON>, StampsFlow } from "../../docSpec/schema/docFlow.schema"
import { DocDraft } from "../DocDraft"
import { DocProps } from "../schema/domain.schema"

export class Doc<PERSON>raftMapper {//copy by <PERSON><PERSON><PERSON><PERSON><PERSON>per
    static toDomain(raw: any): DocDraft {
        return new DocDraft({
            id: raw.id,
            name: raw.name,
            status: raw.status,
            authorId: raw.author_id,
            authorTitle: raw.author_title,

            topic: raw.topic,
            specId: raw.spec_id,
            priority: raw.priority,
            category: raw.category,

            flowData: raw.flow_data as SpecFlow,
            formData: raw.form_data as Record<string, any>,
            stamps:raw.stamps as StampsFlow,

            currentPosition: raw.current_position !== null ? raw.current_position : null,

            createdAt: new Date(raw.created_at),
            updatedAt: new Date(raw.updated_at),
            submittedAt: raw.submitted_at?new Date(raw.submitted_at) : null,
            completedAt: raw.completed_at ? new Date(raw.completed_at) : null,
        })
    }

    static toPersistence(props: DocProps): Prisma.docCreateInput {
        const {
            authorId,
            authorTitle,
            specId,
            flowData,
            formData,
            currentPosition,
            createdAt,
            updatedAt,
            completedAt,
            submittedAt,
            ...elseProps
        } = props
        return {
            ...elseProps,
            author_id: authorId,
            author_title: authorTitle,
            spec_id: specId,
            form_data: formData,
            flow_data: flowData,
            current_position: currentPosition,
            created_at: createdAt,
            updated_at: updatedAt,
            submitted_at: submittedAt,
            completed_at: completedAt
        }
    }
}