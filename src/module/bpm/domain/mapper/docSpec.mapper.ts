import { Prisma } from "@prisma/client"
import { DocSpec } from "../DocSpec"
import { DocSpecProps } from "../schema/domain.schema"
/**
 * toPersistence(props): db, 正規應該建立create/update 物件
 * toDomain(db): domain
 */
export class DocSpecMapper {
    static toDomain(raw: any): DocSpec {
        return new DocSpec({
            id: raw.id,
            label: raw.label,
            priority: raw.priority,
            category: raw.category,
            isActive: raw.is_active,
            docForm: raw.doc_form,
            docFlow: raw.doc_flow
        })
    }

    static toPersistenceForUpdate(props: Partial<Omit<DocSpecProps, 'id'>>): Partial<Prisma.doc_specUpdateInput> {
        const { isActive, docForm, docFlow, ...elseProps } =props
        return {
            ...elseProps,
            is_active: isActive,
            doc_form: docForm,
            doc_flow: docFlow,
        }
    }

    static toPersistence(props: DocSpecProps): Prisma.doc_specCreateInput {
        const { isActive, docForm, docFlow, ...elseProps } = props
        return {
            ...elseProps,
            is_active: isActive,
            doc_form: docForm,
            doc_flow: docFlow,
        }
    }
}