import { <PERSON>risma, SignStatus } from "@prisma/client";
import { SpecFlow, StampsFlow } from "../../docSpec/schema/docFlow.schema";
import { DocWithFile, DocWithTaskSign } from "../schema/query.schema";
import { DocFile } from "@/module/bpm/files/file.schema";
import { Doc } from "../Doc";
import { DocProps } from "../schema/domain.schema";
import { DocDraft } from "../DocDraft";

export class DocMapper {//copy by <PERSON><PERSON>raftMapper
    static toDomain(raw: any): Doc {
        return new Doc({
            id: raw.id,
            name: raw.name,
            status: raw.status,
            authorId: raw.author_id,
            authorTitle: raw.author_title,

            topic: raw.topic,
            specId: raw.spec_id,
            priority: raw.priority,
            category: raw.category,

            flowData: raw.flow_data as SpecFlow,
            formData: raw.form_data as Record<string, any>,
            stamps:raw.stamps as StampsFlow,

            currentPosition: raw.current_position !== null ? raw.current_position : null,

            createdAt: new Date(raw.created_at),
            updatedAt: new Date(raw.updated_at),
            submittedAt: raw.submitted_at?new Date(raw.submitted_at) : null,
            completedAt: raw.completed_at ? new Date(raw.completed_at) : null,
        })
    }

    static toPersistence(props: DocProps): Prisma.docCreateInput {
        const {
            authorId,
            authorTitle,
            specId,
            flowData,
            formData,
            currentPosition,
            createdAt,
            updatedAt,
            completedAt,
            submittedAt,
            ...elseProps
        } = props
        return {
            ...elseProps,
            author_id: authorId,
            author_title: authorTitle,
            spec_id: specId,
            form_data: formData,
            flow_data: flowData,
            current_position: currentPosition,
            created_at: createdAt,
            updated_at: updatedAt,
            submitted_at: submittedAt,
            completed_at: completedAt
        }
    }

    static toDocPropsWithTaskSign(raw: any): DocWithTaskSign {
        return {
            id: raw.id,
            name: raw.name,
            status: raw.status,
            authorId: raw.author_id,
            authorTitle: raw.author_title,

            topic: raw.topic,
            specId: raw.spec_id,
            priority: raw.priority,
            category: raw.category,
            flowData: raw.flow_data,
            formData: raw.form_data,
            stamps: raw.stamps,
            currentPosition: raw.current_position,

            createdAt: raw.created_at,
            updatedAt: raw.updated_at,
            submittedAt:raw.submitted_at,
            completedAt: raw.completed_at,
            
            tasks: raw.task_signs.map((taskSign: any) => ({
                id: taskSign.id,
                docId: taskSign.doc_id,
                operatorId: taskSign.operator_id,
                operatorTitle: taskSign.operator_title,
                mode: taskSign.mode,
                status: taskSign.status,
                action: taskSign.action,
                position: taskSign.position,
                parentId: taskSign.parent_id,
                createdAt: taskSign.created_at,
                updatedAt: taskSign.updated_at,
                completedAt: taskSign.completed_at,
            })),
            currentTasks: raw.task_signs.
                filter((taskSign: any) => taskSign.status === SignStatus.ready).
                map((taskSign: any) => ({
                    id: taskSign.id,
                    docId: taskSign.doc_id,
                    operatorId: taskSign.operator_id,
                    operatorTitle: taskSign.operator_title,
                    mode: taskSign.mode,
                    status: taskSign.status,
                    action: taskSign.action,
                    position: taskSign.position,
                    parentId: taskSign.parent_id,
                    createdAt: taskSign.created_at,
                    updatedAt: taskSign.updated_at,
                    completedAt: taskSign.completed_at,
                })),
        }
    }

    static toResponseWithFiles(docDomain: Doc|DocDraft, files: DocFile[]): DocWithFile {
        const docProps=docDomain.getProps()
        return {
            id: docProps.id,
            name: docProps.name,
            status: docProps.status,
            authorId: docProps.authorId,
            authorTitle: docProps.authorTitle,

            topic: docProps.topic, // add this line to return topic information
            specId: docProps.specId,
            priority: docProps.priority,
            category: docProps.category,
            flowData: docProps.flowData,
            formData: docProps.formData,
            stamps: docProps.stamps,
            currentPosition: docProps.currentPosition,
            createdAt: docProps.createdAt,
            updatedAt: docProps.updatedAt,
            submittedAt: docProps.submittedAt,
            completedAt: docProps.completedAt,
            files: files.map(file => {
                return {
                    id: file.id,
                    docId: file.docId,
                    taskId: file.taskId,
                    fileName: file.fileName,
                    fileType: file.fileType,
                    fileSize: file.fileSize,
                    uploaderId: file.uploaderId,
                    uploaderTitle: file.uploaderTitle,
                    uploadedAt: file.uploadedAt,
                }
            })
        }
    }
}