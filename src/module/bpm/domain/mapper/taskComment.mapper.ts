import { Prisma } from "@prisma/client";
import { TaskCommentProps } from "../schema/domain.schema";

export class TaskCommentMapper {
    static toPersistence(props: TaskCommentProps):Prisma.task_commentCreateInput {
        return {
            task_id: props.taskId,
            doc: { connect: { id: props.docId } },
            operator_id: props.operatorId,
            operator_title: props.operatorTitle,
            comment: props.comment,
            created_at: props.createdAt,
        }
    }
}