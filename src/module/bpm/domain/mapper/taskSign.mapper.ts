import { Prisma } from "@prisma/client";
import { TaskSignProps } from "../schema/domain.schema";
import { TaskSign } from "../TaskSign";

export class TaskSignMapper {
    static toDomain(task: any): TaskSign {
        return new TaskSign({
            taskId: task.task_id,
            docId: task.doc_id,
            operatorId: task.operator_id,
            operatorTitle: task.operator_title,
            mode: task.mode,
            status: task.status,
            action: task.action,
            position: task.position,
            createdAt: task.created_at,
            updatedAt: task.updated_at,
            completedAt: task.completed_at,
        } as TaskSignProps)
    }

    static toPersistence(props: TaskSignProps): Prisma.task_signCreateInput {
        return {
            task_id: props.taskId,
            doc: { connect: { id: props.docId } },
            operator_id: props.operatorId,
            operator_title: props.operatorTitle,
            mode: props.mode,
            position: props.position,
            action: props.action,
            status: props.status,
            completed_at: props.completedAt,
            created_at: props.createdAt,
            updated_at: props.updatedAt
        };
    }

    static toCreateMany(raw: TaskSignProps): Prisma.task_signCreateManyInput {
        return {
            task_id: raw.taskId,
            doc_id: raw.docId,
            operator_id: raw.operatorId,
            operator_title: raw.operatorTitle,
            mode: raw.mode,
            position: raw.position,
            action: raw.action,
            status: raw.status,
            completed_at: raw.completedAt,
            created_at: raw.createdAt,
            updated_at: raw.updatedAt
        };
    }
}