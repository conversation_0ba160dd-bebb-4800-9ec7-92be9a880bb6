import { Prisma } from "@prisma/client";
import { DocHistoryProps } from "../schema/domain.schema";
import { DocHistory } from "../DocHistory";

export class DocHistoryMapper {

    static toDomain(result: any): DocHistory {
        return new DocHistory({
            id: result.id,
            operatorId: result.operator_id,
            operatorTitle: result.operator_title,
            message: result.message,
            action: result.action,
            timestamp: result.timestamp,
            isSuccess: result.is_success
        } as DocHistoryProps)
    }
    
    static toPersistence(props: DocHistoryProps): Prisma.doc_historyCreateInput {
        return {
            id: props.id,
            doc: { connect: { id: props.docId } },
            operator_id: props.operatorId,
            operator_title: props.operatorTitle,
            message: props.message,
            action: props.action,
            timestamp: props.timestamp,
            is_success: props.isSuccess,
            error: props.error
        }
    }

    static toCreateMany(props: DocHistoryProps): Prisma.doc_historyCreateManyInput {
        return {
            id: props.id,
            doc_id: props.docId, // ✅ 要用明確的外鍵
            operator_id: props.operatorId,
            operator_title: props.operatorTitle,
            message: props.message,
            action: props.action,
            timestamp: props.timestamp,
            is_success: props.isSuccess,
            error: props.error
        };
    }

   
}