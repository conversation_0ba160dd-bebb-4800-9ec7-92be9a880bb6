import { SignAction, SignStatus, SignTaskMode } from "@prisma/client"
import { BusinessRuleViolationError } from "@/errors/domain.error"
import { genTaskSignId } from "@/utils/genId"
import { TaskSignProps } from "./schema/domain.schema"


export class TaskSign {
    // domain 會從db建立, 如果建立時有特殊規格, 請使用factory
    constructor(
        public readonly props: TaskSignProps
    ) {
    }

    getProps(): TaskSignProps {
        return this.props;
    }

    static factory(
        docId: string,
        operatorId: string,
        operatorTitle: string,
        position: number,
        status: SignStatus,
    ): TaskSign {
        const now = new Date()
        return new TaskSign({
            taskId: genTaskSignId(),
            docId,
            operatorId,
            operatorTitle,
            mode: SignTaskMode.sign,
            status,
            action: SignAction.null,
            position,
            createdAt: now,
            updatedAt: now,
            completedAt: null
        })
    }

    public do(operatorId: string, isAgree: boolean): void {
        this.props.action = isAgree ? SignAction.agree : SignAction.disagree

        if (this.props.operatorId !== operatorId)
            throw new BusinessRuleViolationError('PermissionDenied', 'You are not this task owner.')

        this.props.status = SignStatus.signed
        this.props.completedAt = new Date()
    }

    public undo(markReadyPosition: number): void {
        // back
        if (this.props.status === SignStatus.signed) {
            this.props.action = SignAction.backed
        }
        // ready/pending
        if (this.props.position === markReadyPosition) {
            this.props.status = SignStatus.ready
        }
        if (this.props.position > markReadyPosition) {
            this.props.status = SignStatus.pending
        }
        if (this.props.position < markReadyPosition) {
            return
        }
        this.props.completedAt = null
        this.props.updatedAt = new Date()
    }

    public disable(): void {
        this.props.status = SignStatus.disabled
        this.props.completedAt = new Date()
    }
}
