import { genDocHistoryId } from "@/utils/genId"
import { DocHistoryProps } from "./schema/domain.schema"
import { DomainEvent, EventTopic, TopicDocAction, TopicDocNotify, TopicDocResult, TopicDocSignAction, TopicDraftAction } from "./DomainEmitter";
import { HistoryAction } from "@prisma/client";
const EventHandlers = {
    //draft
    [TopicDraftAction.CREATE]: (args: DomainEvent) => null,
    [TopicDraftAction.DELETE]: (args: DomainEvent) => null,
    //action
    [TopicDocAction.COMMENT]: (args: DomainEvent) => ({ history: HistoryAction.comment, message: `${args.subjective} comment: ${args.message}.` }),
    [TopicDocAction.ADD]: (args: DomainEvent) => ({ history: HistoryAction.add, message: `${args.subjective} add ${args.objective} to join this doc.` }),
    [TopicDocAction.ADD_INFORM]: (args: DomainEvent) => null,
    [TopicDocAction.BACK]: (args: DomainEvent) => ({ history: HistoryAction.back, message: `${args.subjective} back to sign ${args.message}, undo ${args.objective}'s sign.` }),//flow
    [TopicDocAction.EDIT_FLOW]: (args: DomainEvent) => ({ history: HistoryAction.edit, message: `${args.subjective} edit the doc's flow.` }), //doc
    [TopicDocAction.EDIT_DATA]: (args: DomainEvent) => ({ history: HistoryAction.edit, message: `${args.subjective} edit the doc's data.` }),//doc
    //sign
    [TopicDocSignAction.UNDO]: (args: DomainEvent) => null,// deprecation
    [TopicDocSignAction.DISABLE]: (args: DomainEvent) => ({ history: HistoryAction.disable, message: `${args.subjective} disabled ${args.objective}'s task.` }),//sign
    [TopicDocSignAction.SIGN_SUBMIT]: (args: DomainEvent) => ({ history: HistoryAction.submit, message: `${args.subjective} submit this doc.` }),//sign
    [TopicDocSignAction.SIGN_AGREE]: (args: DomainEvent) => ({ history: HistoryAction.agree, message: `${args.subjective} agree this doc.` }),//sign
    [TopicDocSignAction.SIGN_DISAGREE]: (args: DomainEvent) => ({ history: HistoryAction.disagree, message: `${args.subjective} disagree this doc.` }),//sign
    [TopicDocSignAction.SIGN_INFORMED]: (args: DomainEvent) => ({ history: HistoryAction.agree, message: `${args.subjective} inform the doc.` }), //doc
    //result
    [TopicDocResult.APPROVED]: (args: DomainEvent) => ({ history: HistoryAction.approved, message: `${args.subjective}'s doc has been approved.` }),//TODO!!!!(args) =>({ `${args.subjective}'s doc has been appro})ved.`,
    [TopicDocResult.REJECTED]: (args: DomainEvent) => ({ history: HistoryAction.rejected, message: `${args.subjective}'s doc has been rejected.` }),//TODO!!!! (args) =>({ `${args.subjective}'s doc has been rejec})ted.`,
    [TopicDocResult.CANCELED]: (args: DomainEvent) => ({ history: HistoryAction.canceled, message: `${args.subjective}'s doc has been canceled.` }),
    //notify
    [TopicDocNotify.MARK_READY]: (args: DomainEvent) => null,
} as const satisfies Partial<Record<EventTopic, ((args: DomainEvent) => any) | null>>;

type ValidHistoryTopic = {
    [K in keyof typeof EventHandlers]: typeof EventHandlers[K] extends ((args: any) => null) | null ? never : K
}[keyof typeof EventHandlers];

function isValidEventTopic(topic: EventTopic): topic is ValidHistoryTopic {
    return !!EventHandlers[topic];
}
export class DocHistory {
    constructor(
        public readonly props: DocHistoryProps
    ) {
    }

    getProps(): DocHistoryProps {
        return this.props;
    }

    static factory(event: DomainEvent,timestampNumber:number): DocHistory | null {
        if (isValidEventTopic(event.topic)) {
            const result = EventHandlers[event.topic](event);
            if (result) {
                return new DocHistory({
                    id: genDocHistoryId(),
                    docId: event.docId,
                    operatorId: event.operatorId,
                    operatorTitle: event.operatorTitle,
                    message: result.message,
                    action: result.history,
                    timestamp: new Date(timestampNumber),
                    isSuccess: event.error ? false : true,
                    error: event.error || null
                })
            }
        }
        return null
    }
}