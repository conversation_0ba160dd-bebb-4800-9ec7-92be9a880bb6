import { DocStatus, SignAction, SignStatus, SignTaskMode } from "@prisma/client";
import { DocHistory } from "./DocHistory";
import { BusinessRuleViolationError } from "@/errors/domain.error";
import { TaskSign } from "./TaskSign";
import { TaskComment } from "./TaskComment";
import { genTaskCommentId, genTaskSignId } from "@/utils/genId";
import { DraftFormData } from "../docSpec/schema/docForm.schema";
import { DocProps, TaskCommentProps, TaskSignProps } from "./schema/domain.schema";
import { DomainEmitter, DomainEvent, TopicDocAction, TopicDocNotify, TopicDocResult, TopicDocSignAction } from "./DomainEmitter";
import { StampsFlow } from "../docSpec/schema/docFlow.schema";

export class Doc {
    private events: DomainEvent[] = [] // 紀錄事件
    constructor(
        public readonly props: DocProps
    ) {
    }
    getProps(): DocProps {
        return this.props;
    }
    get id(): string {
        return this.props.id
    }
    get readyPosition(): number | null {
        return this.props.currentPosition
    }

    public submit(operatorId: string, opeartorTitle: string, stamps: StampsFlow, now: Date): TaskSign {
        if (this.props.authorId !== operatorId)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only author can submit.')
        if (this.props.status !== DocStatus.draft)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only draft can be submit.')

        this.props.stamps = stamps
        this.props.submittedAt = now
        this.props.status = DocStatus.in_progress
        this.props.currentPosition = 0

        const authorSign = new TaskSign({
            taskId: genTaskSignId(),
            docId: this.id,
            operatorId: operatorId,
            operatorTitle: opeartorTitle,
            mode: SignTaskMode.sign,
            status: SignStatus.ready,
            action: SignAction.null,
            position: 0,
            createdAt: now,
            updatedAt: now,
            completedAt: now,
        } as TaskSignProps)
        return authorSign
    }

    public updateFormData(operatorId: string, opeartorTitle: string, formData: DraftFormData): void {
        if (this.props.authorId !== operatorId)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only author can edit.')
        if (this.props.status !== DocStatus.in_progress)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only in_progress can be edit.')

        this.events.push({
            topic: TopicDocAction.EDIT_DATA,
            docId: this.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: null,
            message: JSON.stringify({
                originalData: this.props.formData,
                newData: formData.formData
            }),
            error: null,
            receivers: []
        })
        this.props.formData = formData.formData
        this.props.topic = formData.topic
    }

    public cancel(operatorId: string, opeartorTitle: string, notifySigneds: TaskSign[], updDisableUnSigneds: TaskSign[]): void {
        if (this.props.status !== DocStatus.in_progress)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only in_progress can be canceled.')
        if (this.props.authorId !== operatorId)
            throw new BusinessRuleViolationError('PermissionDenied', 'Only author can cancel.')

        this.props.status = DocStatus.canceled
        this.props.completedAt = new Date()

        this.events.push({
            topic: TopicDocResult.CANCELED,
            docId: this.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: notifySigneds.map(sign => sign.props.operatorTitle).join(','),
            message: null,
            error: null,
            receivers: notifySigneds.map(sign => sign.props.operatorId)
        })

        this._disable(operatorId, opeartorTitle, updDisableUnSigneds)
    }

    disagree(operatorId: string, opeartorTitle: string, sign: TaskSign, disableSigns: TaskSign[]): void {
        if (this.props.status !== DocStatus.in_progress) throw new BusinessRuleViolationError('PermissionDenied', 'Only in progress can be disagreed.')
        if (sign.props.status !== SignStatus.ready) throw new BusinessRuleViolationError('PermissionDenied', 'Only current task can be disagreed. It not your turn.')
        sign.do(operatorId, false)
        this._disable(operatorId, opeartorTitle, disableSigns)
        this.props.status = DocStatus.rejected
        this.props.completedAt = new Date()
        const eventObj = {
            docId: this.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: sign.props.operatorTitle,
            message: null,
            error: null,
        }
        this.events.push({
            ...eventObj,
            topic: TopicDocSignAction.SIGN_DISAGREE,
            receivers: []
        })
        this.events.push({
            ...eventObj,
            topic: TopicDocResult.REJECTED,
            receivers: [this.props.authorId]
        })

    }

    agree(operatorId: string, opeartorTitle: string, sign: TaskSign): void {
        if (this.props.status !== DocStatus.in_progress) throw new BusinessRuleViolationError('PermissionDenied', 'Only in progress can be agreed.')
        if (sign.props.status !== SignStatus.ready) throw new BusinessRuleViolationError('PermissionDenied', 'Only current task can be agreed. It not your turn.')

        sign.do(operatorId, true)

        const topic = sign.props.mode === SignTaskMode.inform
            ? TopicDocSignAction.SIGN_INFORMED
            : sign.props.position !== 0
                ? TopicDocSignAction.SIGN_AGREE
                : TopicDocSignAction.SIGN_SUBMIT
        this.events.push({
            topic: topic,
            docId: this.props.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: sign.props.operatorTitle,
            message: null,
            error: null,
            receivers: []
        })
    }



    back(operatorId: string, opeartorTitle: string, position: number, tasks: TaskSign[]): void {
        const reverseTasks = tasks.reverse()
        const undoTasks: TaskSign[] = []
        const newReadyTasks: TaskSign[] = []
        for (const task of reverseTasks) {
            if (task.props.position < position) break
            task.undo(position)
            if(task.props.position===position){
                newReadyTasks.push(task)
            }
            if(task.props.action===SignAction.backed){
                undoTasks.push(task)
            }
        }
        this._ready(newReadyTasks)

        this.events.push({
            topic: TopicDocAction.BACK,
            docId: this.props.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: undoTasks.map(task => task.props.operatorTitle).join(','),
            message: position.toString(),
            error: null,
            receivers: []
        })
        this.props.currentPosition = position
    }

    add(operatorId: string, operatorTitle: string, toPosition: number, assigneeId: string, assigneeTitle: string): TaskSign {
        const position = toPosition + 0.5
        const status = this.props.currentPosition === position ? SignStatus.ready : SignStatus.pending
        const now = new Date()
        const newTask = new TaskSign({
            taskId: genTaskSignId(),
            docId: this.id,
            operatorId: assigneeId,
            operatorTitle: assigneeTitle,
            mode: SignTaskMode.added,
            status,
            action: SignAction.null,
            position,
            createdAt: now,
            updatedAt: now,
            completedAt: null
        })
        this.events.push({
            topic: TopicDocAction.ADD,
            docId: this.props.id,
            operatorId,
            operatorTitle: operatorTitle,
            subjective: operatorTitle,
            objective: assigneeTitle,
            message: position.toString(),
            error: null,
            receivers: []
        })
        return newTask
    }

    comment(operatorId: string, opeartorTitle: string, comment: string): TaskComment {
        const result = new TaskComment({
            taskId: genTaskCommentId(),
            docId: this.props.id,
            operatorId: operatorId,
            operatorTitle: opeartorTitle,
            comment,
            createdAt: new Date()
        } as TaskCommentProps)

        this.events.push({
            topic: TopicDocAction.COMMENT,
            docId: this.props.id,
            operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: null,
            message: comment,
            error: null,
            receivers: []
        })
        return result
    }

    commit(): DocHistory[] {
        const histories: DocHistory[] = []
        const now = +new Date()
        for (let i = 0; i < this.events.length; i++) {
            const history = DocHistory.factory(this.events[i], now + i)
            if (history !== null) histories.push(history)
        }
        return histories
    }

    emit() {
        const emitter = DomainEmitter.getInstance()
        for (const event of this.events) {
            emitter.emit('event', event)
        }
    }

    _done() {
        this.props.status = DocStatus.approved
        this.props.completedAt = new Date()
        this.props.currentPosition = null

        // this.histories.push(DocHistory.factory(
        //     this.props.id, operatorId, opeartorTitle, DocHistoryAction.COMMENT, { subjective: opeartorTitle, message: comment }
        // ))

        this.events.push({
            topic: TopicDocResult.APPROVED,
            docId: this.props.id,
            operatorId: this.props.authorId,
            operatorTitle: this.props.authorTitle,
            subjective: this.props.authorTitle,
            objective: null,
            message: null,
            error: null,
            receivers: [this.props.authorId]
        })
    }

    _ready(nexts: TaskSign[]) {
        const readyPosition = nexts[0].props.position
        for (const task of nexts) {
            task.props.status = SignStatus.ready
        }
        this.props.currentPosition = readyPosition

        this.events.push({
            topic: TopicDocNotify.MARK_READY,
            docId: this.props.id,
            operatorId: this.props.authorId,
            operatorTitle: this.props.authorTitle,
            subjective: null,
            objective: nexts.map(task => task.props.operatorTitle).join(','),
            message: null,
            error: null,
            receivers: nexts.map(task => task.props.operatorId)
        })
    }

    _disable(operatorId: string, opeartorTitle: string, tasks: TaskSign[]) {//op:'disagree'|'cancel'
        for (const task of tasks) {
            task.disable()
        }
        this.events.push({
            topic: TopicDocSignAction.DISABLE,
            docId: this.props.id,
            operatorId: operatorId,
            operatorTitle: opeartorTitle,
            subjective: opeartorTitle,
            objective: tasks.map(task => task.props.operatorTitle).join(','),
            message: null,
            error: null,
            receivers: tasks.map(task => task.props.operatorId)
        })
    }
}