import { DocStatus, SignStatus } from "@prisma/client";
import { DraftFormData } from "../docSpec/schema/docForm.schema";
import { SpecFlow } from "../docSpec/schema/docFlow.schema";
import { z } from "@hono/zod-openapi";
import { DocProps, DocPropsSchema, DocSpecProps } from "./schema/domain.schema";
import { DomainEmitter, DomainEvent, TopicDocNotify, TopicDraftAction } from "./DomainEmitter";
import { DocHistory } from "./DocHistory";
import { TaskSign } from "./TaskSign";

const DraftFactorySchema = DocPropsSchema.pick(
    {
        id: true,
        name: true,
        authorId: true,
        authorTitle: true,
        topic: true,
        formData: true
    }
)
export type DraftFactory = z.infer<typeof DraftFactorySchema>


export class DocDraft {
    // public histories: DocHistory[] = []
    // private events: DomainEvent[] = [] // 紀錄事件
    constructor(
        public readonly props: DocProps
    ) {
    }

    get readyPosition(): number | null {
        return this.props.currentPosition
    }

    getProps(): DocProps {
        return this.props;
    }

    static specFactory(factory: DraftFactory, docSpecProps: DocSpecProps) {
        const props = {
            id: factory.id,
            name: factory.name,
            status: DocStatus.draft,
            authorId: factory.authorId,
            authorTitle: factory.authorTitle,
            topic: factory.topic,
            priority: docSpecProps.priority,
            specId: docSpecProps.id,
            category: docSpecProps.category,
            flowData: docSpecProps.docFlow,
            formData: factory.formData,
            stamps: [],
            currentPosition: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            submittedAt: null,
            completedAt: null,
        } as DocProps
        return new DocDraft(props)
    }

    updateFormData(formData: DraftFormData): void {
        this.props.formData = formData.formData
        this.props.topic = formData.topic
    }
    updateFlowData(flowData: SpecFlow): void {
        this.props.flowData = flowData
    }

    // submit(readyPosition: number | null): void {
    //     this.props.status = DocStatus.in_progress
    //     this.props.currentPosition = readyPosition
    //     this.props.submittedAt = new Date()
    //     // this.histories.push(DocHistory.factory(
    //     //     this.props.id, this.props.authorId, this.props.authorTitle, DocHistoryAction.SUBMIT, { subjective: this.props.authorTitle }
    //     // ))
    //     this.events.push({
    //         topic: TopicDraftAction.SUBMIT,
    //         docId: this.props.id,
    //         operatorId: this.props.authorId,
    //         operatorTitle: this.props.authorTitle,
    //         subjective: this.props.authorTitle,
    //         objective: this.props.authorTitle,
    //         message: this.props.authorTitle,
    //         error: null,
    //         receivers: [],
    //     })
    // }

    delete(): void {
        console.log(`delete draft ${this.props.id}`)
        // this.events.push({
        //     topic: TopicDraftAction.DELETE,
        //     docId: this.props.id,
        //     operatorId: this.props.authorId,
        //     operatorTitle: this.props.authorTitle,
        //     subjective: this.props.authorTitle,
        //     objective: null,
        //     message: null,
        //     error: null,
        //     receivers: [],
        // })
    }

    // commit():DocHistory[] {
    //     const emitter = DomainEmitter.getInstance()
    //     const histories: DocHistory[] = []
    //     for (const event of this.events) {
    //         emitter.emit('event', event)
    //         const history = DocHistory.factory(event)
    //         if (history !== null) histories.push(history)
    //     }
    //     return histories
    // }

    // _ready(nexts: TaskSign[]) {
    //     const readyPosition = nexts[0].props.position
    //     for (const task of nexts) {
    //         task.props.status = SignStatus.ready
    //     }
    //     this.props.currentPosition = readyPosition

    //     this.events.push({
    //         topic: TopicDocNotify.MARK_READY,
    //         docId: this.props.id,
    //         operatorId: this.props.authorId,
    //         operatorTitle: this.props.authorTitle,
    //         subjective: null,
    //         objective: nexts.map(task => task.props.operatorTitle).join(','),
    //         message: JSON.stringify([nexts.map(task => task.props.operatorId)]),//寄信用
    //         error: null,
    //         receivers: nexts.map(task => task.props.operatorId),
    //     })
    // }
}