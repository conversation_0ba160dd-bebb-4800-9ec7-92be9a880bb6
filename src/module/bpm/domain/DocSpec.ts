import { genDocSpecId } from "@/utils/genId";
import { DocSpecProps } from "./schema/domain.schema";


export class DocSpec {
    constructor(
        public readonly props: DocSpecProps
    ) {
    }

    getProps(): DocSpecProps {
        return this.props;
    }

    public static factory(
        props: Omit<DocSpecProps, 'id'>
    ): DocSpec {
        const id = genDocSpecId()
        return new DocSpec({ id, ...props })
    }
}