import { SpecFlowSchema, StampsFlowSchema } from "../../docSpec/schema/docFlow.schema";
import { DocPriority, DocStatus, HistoryAction, SignAction, SignStatus, SignTaskMode } from "@prisma/client";
import { SpecFormSchema } from "../../docSpec/schema/docForm.schema";
import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi';

extendZodWithOpenApi(z);
export const EmpPkSchema = z.string().openapi({ example: "FC0056" });
export const EmpTitleSchema = z.string().openapi({ example: "Mike" });
// spec
export const DocSpecPkSchema = z.string().openapi({ example: "ds_001" })
export const DocSpecPropsSchema = z.object({
    id: DocSpecPkSchema,
    label: z.string().openapi({ example: "更新後的標題" }), // 為多國語系
    priority: z.nativeEnum(DocPriority).openapi({ example: DocPriority.normal }),
    category: z.string().openapi({ example: "預算類" }),
    isActive: z.boolean().openapi({ example: true }),
    docForm: SpecFormSchema,
    docFlow: SpecFlowSchema
})
export type DocSpecProps = z.infer<typeof DocSpecPropsSchema>
// doc
export const DocPkSchema = z.string().openapi({ example: "d20250520001" })
export const DocPropsSchema = z.object({
    id: DocPkSchema,
    name: z.string().openapi({ example: "年度預算" }),
    status: z.nativeEnum(DocStatus).openapi({ example: DocStatus.draft }),
    authorId: EmpPkSchema,
    authorTitle: EmpTitleSchema,
    // form spec
    topic: z.string().optional().openapi({ example: '2025年度預算' }),
    specId: DocSpecPkSchema,
    priority: z.nativeEnum(DocPriority).openapi({ example: DocPriority.normal }),
    category: z.string().openapi({ example: '預算類' }),
    formData: z.record(z.any()).openapi({
        example:
        {
            "budgetType": "tools",
            "budget": 10000,
            "reason": "buy"
        }
    }),
    // flow
    flowData: SpecFlowSchema,
    stamps: StampsFlowSchema,
    currentPosition: z.number().nullable().openapi({ example: 0 }),

    createdAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    updatedAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    submittedAt: z.date().nullable().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    completedAt: z.date().nullable().openapi({ example: "2025-01-01T00:00:00.000Z" }),
})
export type DocProps = z.infer<typeof DocPropsSchema>

// history
export const DocHistoryPropsSchema = z.object({
    id: z.string().openapi({ example: "hist-456" }),
    docId: DocPkSchema,
    operatorId: z.string().openapi({ example: "user-456" }),
    operatorTitle: z.string().openapi({ example: "王小明" }),
    message: z.string().openapi({ example: "已核准" }),
    action: z.nativeEnum(HistoryAction).openapi({ example: HistoryAction.agree }),
    timestamp: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    isSuccess: z.boolean().openapi({ example: true }),
    error: z.string().nullable().openapi({ example: null })
})
export type DocHistoryProps = z.infer<typeof DocHistoryPropsSchema>

//task comment
export const TaskCommentPkSchema=z.string().openapi({ example: "tsc_001" })
export const TaskCommentPropsSchema=z.object({
    taskId: TaskCommentPkSchema,
    docId: DocPkSchema,
    operatorId: z.string().openapi({ example: "FC0065" }),
    operatorTitle: z.string().openapi({ example: "Mike" }),
    comment:  z.string().openapi({ example: "我同意" }),
    createdAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    updatedAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
})
export type TaskCommentProps= z.infer<typeof TaskCommentPropsSchema>

// task sign
export const TaskSignPkSchema = z.string().openapi({ example: "tss_001" })
export const TaskSignPropsSchema = z.object({
    taskId: TaskSignPkSchema,
    docId: DocPkSchema,
    operatorId: EmpPkSchema,
    operatorTitle: EmpTitleSchema,
    mode: z.nativeEnum(SignTaskMode).openapi({ example: SignTaskMode.sign }),
    status: z.nativeEnum(SignStatus).openapi({ example: SignStatus.signed }),
    action: z.nativeEnum(SignAction).openapi({ example: SignAction.agree }),
    position: z.number().openapi({ example: 0 }),
    createdAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    updatedAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
    completedAt: z.date().nullable().openapi({ example: "2025-01-01T00:00:00.000Z" }),
})

export type TaskSignProps = z.infer<typeof TaskSignPropsSchema>