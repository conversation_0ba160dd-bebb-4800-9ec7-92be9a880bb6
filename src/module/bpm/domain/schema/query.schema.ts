import { DocFileSchema } from "@/module/bpm/files/file.schema";
import { DocStatus, SignAction, SignStatus, SignTaskMode } from "@prisma/client";
import { DocHistoryPropsSchema, DocPropsSchema, DocSpecPropsSchema, EmpPkSchema, TaskSignPropsSchema } from "./domain.schema";
import dayjs from "dayjs";
import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi';

extendZodWithOpenApi(z);
// submit
export const CreateDraftSchema = z.object({
    name: z.string().openapi({ example: "xxx買文具" }),
    day: z.string()
        .refine((val) => {
            return dayjs(val, "YYYY/MM/DD", true).isValid();
        }, {
            message: "The date format must be YYYY/MM/DD and must be a valid date.",
        })
        .refine((val) => {
            const parsed = dayjs(val, "YYYY/MM/DD");
            const now = dayjs();

            const diffInDays = Math.abs(parsed.diff(now, "day"));
            return diffInDays <= 2;
        }, {
            message: "The date differs from the system date by more than 2 days",
        }).transform((val) =>
            dayjs(val, "YYYY/MM/DD").format("YYYYMMDD")
        ).openapi({ example: "2025/05/20" }),
    data: z.record(z.any()).openapi({
        example:
        {
            "budgetType": "tools",
            "budget": 10000,
            "reason": "buy"
        }
    }),
    topic: z.string().optional().openapi({ example: "年度預算申請" }),
})
export type CreateDraft = z.infer<typeof CreateDraftSchema>

// response
export const DocWithFileSchema = DocPropsSchema.extend({
    files: z.array(DocFileSchema),
});
export type DocWithFile = z.infer<typeof DocWithFileSchema>
export const DocWithTaskSignSchema = DocPropsSchema
.extend({
    tasks: z.array(TaskSignPropsSchema),
    currentTasks: z.array(TaskSignPropsSchema),
});
export type DocWithTaskSign = z.infer<typeof DocWithTaskSignSchema>
export const DocWithFileAndTaskSchema = DocPropsSchema
.extend({
    tasks: z.array(TaskSignPropsSchema),
    currentTasks: z.array(TaskSignPropsSchema),
    files: z.array(DocFileSchema),
});
export type DocWithFileAndTask = z.infer<typeof DocWithFileAndTaskSchema>

//query
export const DocSpecListQuerySchema = DocSpecPropsSchema
    .pick({
        id: true,
        label: true,
        isActive: true,
        priority: true,
        category: true,
    })
    .extend({
        isActive: z.preprocess(
            (val) => {
                if (typeof val === 'boolean') return val;
                if (val === 'true') return true;
                if (val === 'false') return false;
                return val;
            },
            z.boolean(),
        ).openapi({ example: true }),
    }).partial()
export type DocSpecListQuery = z.infer<typeof DocSpecListQuerySchema>

export const DocDraftListQuerySchema = DocPropsSchema.pick(
    {
        id: true,
        name: true,
        status: true,
        authorId: true,
        specId: true,
        priority: true,
        category: true,
    }
).partial();
export type DocDraftListQuery = z.infer<typeof DocDraftListQuerySchema>
export const DocListQuerySchema = DocPropsSchema.pick({
    id: true,
    name: true,
    authorId: true,
}).extend({
    signerId: EmpPkSchema,
    docStatus: z.union([z.nativeEnum(DocStatus), z.array(z.nativeEnum(DocStatus))])
        .transform((val): string[] => {
            if (!val) return [] // 未傳 → 空陣列
            return typeof val === 'string' ? [val] : val
        }).optional().openapi({ example: [DocStatus.draft] }),
    signModes: z.union([z.nativeEnum(SignTaskMode), z.array(z.nativeEnum(SignTaskMode))])
        .transform((val): string[] => {
            if (!val) return [] // 未傳 → 空陣列
            return typeof val === 'string' ? [val] : val
        }).optional().openapi({ example: [SignTaskMode.inform] }),
    signStatus: z.union([z.nativeEnum(SignStatus), z.array(z.nativeEnum(SignStatus))])
        .transform((val): string[] => {
            if (!val) return [] // 未傳 → 空陣列
            return typeof val === 'string' ? [val] : val
        }).optional().openapi({ example: [SignStatus.pending] }),
    signActions: z.union([z.nativeEnum(SignAction), z.array(z.nativeEnum(SignAction))])
        .transform((val): string[] => {
            if (!val) return [] // 未傳 → 空陣列
            return typeof val === 'string' ? [val] : val
        }).optional().openapi({ example: [SignAction.agree] }),
    filterDraftSign: z.preprocess(
        (val) => {
            if (typeof val === 'boolean') return val;
            if (val === 'true') return true;
            if (val === 'false') return false;
            return val;
        },
        z.boolean().optional(),
    ).openapi({ example: true }),
}).partial()
export type DocListQuery = z.infer<typeof DocListQuerySchema>

export const DocHistoryListQuerySchema = DocHistoryPropsSchema.pick({
    id: true,
    operatorId: true,
    operatorTitle: true,
    message: true,
    action: true,
    timestamp: true,
}).partial()
    .extend({
        isSuccess: z.preprocess(
            (val) => {
                if (typeof val === 'boolean') return val;

                if (val === 'true') return true;
                if (val === 'false') return false;
                return val;
            },
            z.boolean(),
        ).openapi({ example: true }),
    });
export type DocHistoryListQuery = z.infer<typeof DocHistoryListQuerySchema>

const TaskSignFilterSchema = TaskSignPropsSchema.omit({
    operatorTitle: true,
    createdAt: true,
    updatedAt: true,
    completedAt: true,
}).extend({
    inStatus: z.array(z.nativeEnum(SignStatus)),
    inAction: z.array(z.nativeEnum(SignAction)),
    gtPosition: z.number(),
    gtePosition: z.number(),
    ltPosition: z.number(),
    ltePosition: z.number(),
    excludeTaskId: z.string(),
})
export type TaskSignListFilter = Partial<z.infer<typeof TaskSignFilterSchema>>