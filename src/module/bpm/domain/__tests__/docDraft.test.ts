import { deepcopyBaseDocProps } from './domain.mock';
import { DocDraft } from "@/module/bpm/domain/DocDraft";
import { DocStatus } from "@prisma/client";
jest.mock('nanoid', () => ({
  nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('DocDraft Domain', () => {
  describe('表單更新', () => {
    it('應該可以更新表單資料', () => {
      const draft = new DocDraft(deepcopyBaseDocProps());

      const newFormData = { topic: 'haha', formData: { newData: true } };
      draft.updateFormData(newFormData);
      expect(draft.props.formData).toEqual(newFormData.formData);
      expect(draft.props.topic).toEqual(newFormData.topic);
    });
  });
});