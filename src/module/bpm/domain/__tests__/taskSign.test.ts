import { TaskSign } from '@/module/bpm/domain/TaskSign';
import { SignStatus, SignAction } from '@prisma/client';
import { BusinessRuleViolationError } from '@/errors/domain.error';
jest.mock('nanoid', () => ({
  nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('TaskSign Domain', () => {
  describe('簽核執行', () => {
    it('應該可以成功執行同意簽核', () => {
      const task = TaskSign.factory(
        'doc-1',
        'user-1',
        '經理',
        1,
        SignStatus.ready
      );

      task.do('user-1', true);

      expect(task.props.status).toBe(SignStatus.signed);
      expect(task.props.action).toBe(SignAction.agree);
      expect(task.props.completedAt).toBeDefined();
    });

    it('非任務擁有者不能執行簽核', () => {
      const task = TaskSign.factory(
        'doc-1',
        'user-1',
        '經理',
        1,
        SignStatus.ready
      );

      expect(() => task.do('user-2', true))
        .toThrow(BusinessRuleViolationError);
    });
  });

  describe('簽核回退', () => {
    it('應該可以正確回退到指定位置', () => {
      const task = TaskSign.factory(
        'doc-1',
        'user-1',
        '經理',
        2,
        SignStatus.signed
      );

      task.undo(1);

      expect(task.props.status).toBe(SignStatus.pending);
      expect(task.props.action).toBe(SignAction.backed);
      expect(task.props.completedAt).toBeNull();
    });
  });
});