// import 順序不可亂 task,docspec, doc 原因不明
import { TaskSignProps, DocSpecProps, DocProps } from '../schema/domain.schema'
import { DocPriority, DocStatus, SignAction, SignStatus, SignTaskMode } from "@prisma/client";
import { FlowPartialMode } from "../../docSpec/schema/docFlow.schema";

const baseTaskSignProps: TaskSignProps = {
    taskId: "tss_001",
    docId: "d20250520001",
    operatorId: "FC0056",
    operatorTitle: "Mike",
    mode: SignTaskMode.sign,
    status: SignStatus.signed,
    action: SignAction.agree,
    position: 0,
    createdAt: new Date("2025-01-01T00:00:00.000Z"),
    updatedAt: new Date("2025-01-01T00:00:00.000Z"),
    completedAt: null
} as TaskSignProps
export const deepcopyBaseTaskSignProps = () => ({ ...baseTaskSignProps })

const baseDocSpecProps: DocSpecProps = {
    id: "ds_001",
    label: "更新後的標題", // 為多國語系
    priority: DocPriority.normal,
    category: "預算類",
    isActive: true,
    docForm: [
        { key: "budgetType", label: "預算類別", conditional: "required", inputType: "text", validate: true, hideLabel: false },
        { key: "budget", label: "預算金額", conditional: "required", inputType: "number", validate: true, hideLabel: false },
        { key: "reason", label: "申請原因", conditional: "required", inputType: "text", validate: true, hideLabel: false },
    ],
    docFlow: [
        { mode: FlowPartialMode.person, employeeId: 'FC0056' },
        [
            { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
            { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: false }
        ],
        { mode: FlowPartialMode.role, type: 'department'/*部門主管*/ },
    ]
} as DocSpecProps
export const deepcopyBaseDocSpecProps = () => ({ ...baseDocSpecProps })

const baseDocProps: DocProps = {
    id: "d20250520001",
    name: "年度預算",
    status: DocStatus.draft,
    authorId: "FC0056",
    authorTitle: "Mike",
    // form spec
    topic: '2025年度預算',
    specId: "ds_001",
    priority: DocPriority.normal,
    category: '預算類',
    formData: [
        { key: "budgetType", label: "預算類別", conditional: "required", inputType: "text", validate: true, hideLabel: false },
        { key: "budget", label: "預算金額", conditional: "required", inputType: "number", validate: true, hideLabel: false },
        { key: "reason", label: "申請原因", conditional: "required", inputType: "text", validate: true, hideLabel: false },
    ],
    // flow
    flowData: [
        { mode: FlowPartialMode.person, employeeId: 'FC0056' },
        [
            { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
            { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: false }
        ],
        { mode: FlowPartialMode.role, type: 'department'/*部門主管*/ },
    ],
    stamps: [
        { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
        { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
        { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: true }
    ],
    currentPosition: 0,

    createdAt: new Date("2025-01-01T00:00:00.000Z"),
    updatedAt: new Date("2025-01-01T00:00:00.000Z"),
    submittedAt: null,
    completedAt: null,
} as DocProps
export const deepcopyBaseDocProps = () => ({ ...baseDocProps })