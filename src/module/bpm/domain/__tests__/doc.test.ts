import { deepcopyBaseDocProps } from './domain.mock';
import { Doc } from '../Doc';
import { TaskSign } from '../TaskSign';
import { DocStatus, SignAction, SignStatus } from "@prisma/client";
jest.mock('nanoid', () => ({
  nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('Doc Domain', () => {
  describe('submit 文件提交', () => {
    it('應該可以成功提交文件, 建立所有author+sign task', () => {
      const draft = new Doc(deepcopyBaseDocProps());

      draft.submit('FC0056', 'Mike',[],new Date());

      expect(draft.props.status).toBe(DocStatus.in_progress);
      expect(draft.props.currentPosition).toBe(0);// service 會處理, 確認後續再變成1
      expect(draft.props.submittedAt).toBeDefined();
    });
  });
  describe('同意簽核', () => {
    it('應該可以成功同意簽核, 下一關 mark ready', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress, currentPosition: 0 });
      const currentSign = TaskSign.factory('doc-1', 'user-2', '經理', 1, SignStatus.ready);
      const nextSigns = [TaskSign.factory('doc-1', 'user-2', '經理', 2, SignStatus.ready)]

      doc.agree('user-2', '經理', currentSign);
      doc._ready(nextSigns)
      expect(doc.readyPosition).toBe(2);

      expect(currentSign.props.status).toBe(SignStatus.signed);
      expect(currentSign.props.action).toBe(SignAction.agree);
      nextSigns.map(n => expect(n.props.status).toBe(SignStatus.ready))
    });

    it('當全部簽核完成時應該將文件狀態改為已核准', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress, currentPosition: 9 });
      const currentSign = TaskSign.factory('doc-1', 'user-2', '經理', 9, SignStatus.ready);
      doc.agree('user-2', '經理', currentSign);
      doc._done()
      expect(doc.props.status).toBe(DocStatus.approved);
      expect(doc.props.completedAt).toBeDefined();
      expect(doc.readyPosition).toBe(null);
    });
  });

  describe('不同意簽核', () => {
    it('應該可以成功不同意並結束流程', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });
      const currentSign = TaskSign.factory('doc-1', 'user-2', '經理', 1, SignStatus.ready);

      doc.disagree('user-2', '經理', currentSign,[]);

      expect(currentSign.props.status).toBe(SignStatus.signed);
      expect(currentSign.props.action).toBe(SignAction.disagree);
      expect(doc.props.status).toBe(DocStatus.rejected);
      expect(doc.props.completedAt).toBeDefined();
    });
  });

  describe('退回文件', () => {
    it('應該可以成功退回到指定位置', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });
      const signedTasks = [
        TaskSign.factory('doc-1', 'authr', '經理', 0, SignStatus.signed),
        TaskSign.factory('doc-1', 'user-2', '經理', 1, SignStatus.signed),
        TaskSign.factory('doc-1', 'user-3', '總監', 2, SignStatus.ready)
      ];

      doc.back('user-3', '總監', 0, signedTasks);

      expect(doc.readyPosition).toBe(0);
      // 有做array reverse
      expect(signedTasks[0].props.status).toBe(SignStatus.pending);
      expect(signedTasks[0].props.action).toBe(SignAction.backed);

      expect(signedTasks[1].props.status).toBe(SignStatus.pending);
      expect(signedTasks[1].props.action).toBe(SignAction.backed);

      expect(signedTasks[2].props.status).toBe(SignStatus.ready);
      expect(signedTasks[2].props.action).toBe(SignAction.backed);
    });
  });

  describe('加簽', () => {
    it('應該可以成功加入新的簽核者', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });

      const newTask = doc.add('user-2', '經理', 1, 'user-4', '專案經理');

      expect(newTask.props.position).toBe(1.5);
      expect(newTask.props.status).toBe(SignStatus.pending);
    });
  });

  describe('更新表單', () => {
    it('非作者不能更新表單', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });

      expect(() => doc.updateFormData('user-2', '經理', { formData: { newData: true } }))
        .toThrow('Only author can edit.');
    });

    it('作者可以更新表單', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress, authorId: 'user-1',topic:undefined });
      const newFormData = { formData: { newData: true } };

      doc.updateFormData('user-1', '工程師', newFormData);

      expect(doc.props.formData).toEqual(newFormData.formData);
      expect(doc.props.topic).toEqual(undefined);

    });
  });

  describe('取消文件', () => {
    it('只有作者可以取消文件', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress, authorId: 'user-1' });
      const signedTasks = [
        TaskSign.factory('doc-1', 'user-1', '經理', 1, SignStatus.signed)
      ];
    
      const unSignedTasks = [
        TaskSign.factory('doc-1', 'user-2', '經理', 2, SignStatus.ready)
      ];

      doc.cancel('user-1', '工程師',signedTasks,unSignedTasks);

      expect(doc.props.status).toBe(DocStatus.canceled);
      expect(doc.props.completedAt).toBeDefined();
      expect(unSignedTasks[0].props.status).toBe(SignStatus.disabled);
    });

    it('非作者不能取消文件', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });
      const signedTasks = [
        TaskSign.factory('doc-1', 'user-1', '經理', 1, SignStatus.signed)
      ];
    
      const unSignedTasks = [
        TaskSign.factory('doc-1', 'user-2', '經理', 2, SignStatus.ready)
      ];
      expect(() => doc.cancel('user-2', '經理',signedTasks,unSignedTasks))
        .toThrow('Only author can cancel.');
    });
  });

  describe('新增評論', () => {
    it('應該可以成功新增評論', () => {
      const doc = new Doc({ ...deepcopyBaseDocProps(), status: DocStatus.in_progress });

      const comment = doc.comment('user-2', '經理', '請補充說明');

      expect(comment.props.comment).toBe('請補充說明');
      expect(comment.props.operatorId).toBe('user-2');
    });
  });
});