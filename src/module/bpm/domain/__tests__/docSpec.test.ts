import { deepcopyBaseDocSpecProps} from './domain.mock';
import { DocSpec } from '@/module/bpm/domain/DocSpec';
import { DocPriority } from '@prisma/client';
jest.mock('nanoid', () => ({
  nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('DocSpec Domain', () => {
  describe('建立文件規格', () => {
    it('應該可以成功建立新的文件規格', () => {

      const docSpec = DocSpec.factory(deepcopyBaseDocSpecProps());

      expect(docSpec.props.id).toBeDefined();
      expect(docSpec.props.label).toBe('更新後的標題');
      expect(docSpec.props.priority).toBe(DocPriority.normal);
    });
  });
});