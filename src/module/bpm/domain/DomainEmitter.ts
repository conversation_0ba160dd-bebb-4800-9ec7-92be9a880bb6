import EventEmitter from "events";

export enum TopicDocAction {
    BACK = 'DOC_ACTION_BACK',
    COMMENT = 'DOC_ACTION_COMMENT',
    ADD = 'DOC_ACTION_ADD',
    ADD_INFORM = 'DOC_ACTION_ADD_INFORM',
    EDIT_FLOW = 'DRAFT_EDIT_FLOW',
    EDIT_DATA = 'DRAFT_EDIT_DATA',
}

export enum TopicDocNotify {
    MARK_READY = 'DOC_MARK_READY',// email signer
}

export enum TopicDocSignAction {
    SIGN_SUBMIT = 'DOC_SIGN_SUBMIT',
    SIGN_AGREE = 'DOC_SIGN_AGREE',
    SIGN_DISAGREE = 'DOC_SIGN_DISAGREE',
    SIGN_INFORMED = 'DOC_SIGN_INFORMED',

    UNDO = 'DOC_ACTION_UNDO_SIGN',
    DISABLE = 'DOC_ACTION_DISABLE_SIGN',
}

export enum TopicDocResult {
    CANCELED = 'DOC_RESULT_CANCEL',// email 除了發起人, 其他都會收到
    APPROVED = 'DOC_RESULT_APPROVED',// email author
    REJECTED = 'DOC_RESULT_REJECTED',// email author
}

export enum TopicDraftAction {
    CREATE = 'DRAFT_ACTION_CREATE',
    DELETE = 'DRAFT_ACTION_DELETE',
}

export type EventTopic = TopicDocAction | TopicDocSignAction | TopicDocResult | TopicDraftAction | TopicDocNotify

export interface DomainEvent {
    topic: EventTopic
    docId: string
    operatorId: string
    operatorTitle: string
    subjective: string | null // name
    objective: string | null // name
    message: string | null
    error: string | null
    receivers: string[]
}

export class DomainEmitter extends EventEmitter {
    constructor() {
        super()
    }
    static instance: DomainEmitter | null = null;
    static getInstance() {
        // 如果沒有被初始化過，就初始化一個
        if (DomainEmitter.instance === null) {
            DomainEmitter.instance = new DomainEmitter();
        }
        return DomainEmitter.instance;
    }
}
