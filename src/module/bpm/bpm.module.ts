import { env } from "@/env";
import { AwsS3 } from "@/infra/AwsS3";
import { getPrisma } from "@/infra/db";
import { CommonModule, createModuleSwagger } from "@/infra/openapiSpec";
import { swaggerUI } from "@hono/swagger-ui";
import { Hono } from "hono";
import { DocFileRepository } from "./files/file.repository";
import { createFileRouter } from "./files/file.router";
import { CommonRepository } from "./common/common.repository";
import { DocSpecRepository } from "./docSpec/docSpec.repository";
import { DocDraftRepository } from "./docDraft/docDraft.repository";
import { TaskRepository } from "./docAction/repository/task.repository";
import { DocSpecService } from "./docSpec/docSpec.service";
import { createDocSpecRouter } from "./docSpec/docSpec.router";
import { DocActionRepository } from "./docAction/repository/action.repository";
import { DocActionService } from "./docAction/docAction.service";
import { createDocActionRouter } from "./docAction/docAction.router";
import { DocDraftService } from "./docDraft/docDraft.service";
import { createDocDraftRouter } from "./docDraft/docDraft.router";
import { DocQueryRepository } from "./docQuery/docQuery.repository";
import { DocQueryService } from "./docQuery/docQuery.service";
import { createDocQueryRouter } from "./docQuery/docQuery.router";
import { OrgRepository } from "./org/org.repository";
import { createOrgRouter } from "./org/org.router";
import { DomainMailer } from "./common/Mailer";

// TODO(Mike): 把bpm的功能放到 /bpm
export function bpmModule(commonModules: CommonModule[]) {
    // repo
    const prisma = getPrisma()

    // file
    const fileStorage = new AwsS3()
    const fileRepository = new DocFileRepository(prisma)
    const fileRouter = createFileRouter(fileStorage, fileRepository)

    // common
    const commonRepository = new CommonRepository(prisma)
    const docSpecRepository = new DocSpecRepository(prisma)
    const docDraftRepository = new DocDraftRepository(prisma)
    const taskRepository = new TaskRepository(prisma)

    // doc spec
    const docSpecService = new DocSpecService(docSpecRepository)
    const docSpecRouter = createDocSpecRouter(docSpecService)

    // doc action
    const docActionRepository = new DocActionRepository(prisma)
    const docActionService = new DocActionService(
        docActionRepository,
        taskRepository,
        commonRepository
    )
    const docActionRouter = createDocActionRouter(docActionService)

    // doc draft
    const docDraftService = new DocDraftService(docDraftRepository, commonRepository, fileRepository)
    const docDraftRouter = createDocDraftRouter(docDraftService, docActionService)

    //  doc query
    const docQueryRepository = new DocQueryRepository(prisma)
    const docQueryService = new DocQueryService(docQueryRepository, fileRepository)
    const docQueryRouter = createDocQueryRouter(docQueryService)

    // org
    const orgRepository = new OrgRepository(prisma)
    const orgRouter = createOrgRouter(orgRepository)

    const mailer = new DomainMailer(commonRepository)

    // Import the health router from the health module
    const router = new Hono()
    const moduleName = 'bpm';

    router.route('/v0/doc-operators', orgRouter)
    router.route('/v0/doc-spec', docSpecRouter);
    router.route('/v0/docs-draft', docDraftRouter)
    router.route('/v0/docs-action', docActionRouter)
    router.route('/v0/docs', docQueryRouter)
    router.route('/v0/files', fileRouter)

    // Swagger
    const bpmSpec = createModuleSwagger(
        router,
        {
            title: 'BPM API',
            description: '商業流程 API (包含通用認證 API)',
            modulePath: '/v0/bpm',
        },
        commonModules
    );
    router.get(`/bpm/openapi-doc`, bpmSpec)
    router.get(`/bpm/swagger`, swaggerUI({ url: '/v0/bpm/openapi-doc', }))
    console.log(`* [Module] BPM module initialized 
    - ${env.HOST_URL}/v0/${moduleName}(todo)
    - ${env.HOST_URL}/v0/${moduleName}/swagger`);
    return router
}