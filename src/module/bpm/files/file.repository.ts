import { Prisma, PrismaClient } from "@prisma/client"
import { DocFile } from "./file.schema"
import { CatchRepositoryError } from "@/utils/repoDecorator";

export class DocFileRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async list(query: Partial<DocFile>, tx?: Prisma.TransactionClient): Promise<DocFile[]> {
        const db = tx ?? this.prisma
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.taskId !== undefined ? { task_id: query.taskId } : {})
        }
        const list = await db.doc_file.findMany({
            where,
        });
        return list.map(doc => this.toDomain(doc));
    }

    @CatchRepositoryError()
    async bulkDelete(query: Partial<DocFile>, tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.taskId !== undefined ? { task_id: query.taskId } : {})
        }
        await db.doc_file.deleteMany({ where })
    }

    @CatchRepositoryError()
    async save(file: DocFile, tx?: Prisma.TransactionClient): Promise<DocFile> {
        const db = tx ?? this.prisma
        const result = await db.doc_file.upsert({
            where: { id: file.id },
            update: {
                doc_id: file.docId,
                task_id: file.taskId,
                file_name: file.fileName,
                file_type: file.fileType,
                file_size: file.fileSize,
                uploader_id: file.uploaderId,
                uploader_title: file.uploaderTitle,
                uploaded_at: file.uploadedAt,
            },
            create: {
                id: file.id,
                doc_id: file.docId,
                task_id: file.taskId,
                file_name: file.fileName,
                file_type: file.fileType,
                file_size: file.fileSize,
                uploader_id: file.uploaderId,
                uploader_title: file.uploaderTitle,
                uploaded_at: file.uploadedAt,
            },
        });
        return this.toDomain(result)
    }

    toDomain(doc: any): DocFile {
        return {
            id: doc.id,
            docId: doc.doc_id,
            taskId: doc.task_id,
            fileName: doc.file_name,
            fileType: doc.file_type,
            fileSize: doc.file_size,
            uploaderId: doc.uploader_id,
            uploaderTitle: doc.uploader_title,
            uploadedAt: doc.uploaded_at,
        }
    }
}