import { Context, Hono } from "hono";
import { AwsS3 } from "../../../infra/AwsS3";
import { openApiMiddleware } from "@/middleware/openapi";
import { z } from "@hono/zod-openapi";
import { validator } from "hono-openapi/zod";
import { DocFile, DocFileSchema, SpecifiedFile } from "./file.schema";
import { jwtAuthMiddleware } from "@/middleware/auth";
import { DocFileRepository } from "./file.repository";
import { createListResponseSchema, successBase, SuccessResponseSchema } from "../../common/schema";


export function createFileRouter(storage: AwsS3, fileRepo: DocFileRepository) {
  const router = new Hono()
  router.use('*', jwtAuthMiddleware)
  const spec = openApiMiddleware('Files', ['JwtKeyAuth'])
  /**
   * 使用方式：
   * 1. 使用presigned-url上傳檔案
   * 2. 使用notify通知檔案上傳完成
   * 3. 使用file-url取得檔案
   */
  router.post('/generate-presigned-url',
    spec(successBase(z.object({ url: z.string(), key: z.string() })), 'GeneratePresignedUrl'),
    validator('json', z.object({
      fileName: z.string(),
      fileType: z.string(),
      docId: z.string().openapi({ example: 'd_001' }),
      taskId: z.string().nullable(),
    })),
    async (c) => {
      try {
        const { fileName, fileType, docId, taskId } = await c.req.json()
        if (!fileName || !fileType) {
          return c.json({ error: 'Missing fields' }, 400)
        }
        const key = storage.genS3key(docId, fileName)
        const url = await storage.generatePresignedUrl(key, fileType)

        return c.json({
          success: true,
          data: {
            url, key
          }
        })
      } catch (err) {
        console.error(err)
      }
    })

  router.post('/notify',
    spec(SuccessResponseSchema, 'Notify uploaded file'),
    validator('json', DocFileSchema.omit({
      uploaderId: true,
      uploaderTitle: true,
      uploadedAt: true,
    })),
    async (c: Context) => {
      const body = await c.req.json()
      const { id, docId, taskId, fileType, fileSize, fileName } = body
      const user = c.get('user')
      const now = new Date()
      await fileRepo.save({
        id: id,
        docId: docId,
        taskId: taskId,
        fileName: fileName,
        fileType: fileType,
        fileSize: fileSize,
        uploaderId: user?.id,
        uploaderTitle: user?.name,
        uploadedAt: now,
      } as DocFile)
      return c.json({ success: true })
    })

  // Read - Get pre-signed GET URL
  router.get('/file-url',
    spec(successBase(z.object({ url: z.string() })), 'Read - Get pre-signed GET URL'),
    validator('query', SpecifiedFile),
    async (c) => {
      const { fileName, docId } = c.req.query()
      const key = storage.genS3key(docId, fileName)
      const url = await storage.get(key)
      return c.json({
        success: true,
        data: {
          url
        }
      })
    })

  // Delete
  router.delete('/file',
    spec(SuccessResponseSchema, 'Delete'),
    validator('json', SpecifiedFile),
    async (c) => {
      const { fileName, docId } = await c.req.json()
      const key = storage.genS3key(docId, fileName)
      await storage.delete(key)
      return c.json({ success: true })
    })


  // List
  router.get('/list',
    spec(createListResponseSchema(z.object({
      files: z.array(
        z.object({
          key: z.string(),
          lastModified: z.date(),
          size: z.number()
        }))
    })), 'List'),
    validator('query', z.object({
      docId: z.string().openapi({ example: 'd_001' }),
    })),
    async (c) => {
      const { docId } = c.req.query()
      const prefix = storage.genS3Prefix(docId)
      const files = await storage.list(prefix)
      return c.json({ files })
    })

  return router;
}
