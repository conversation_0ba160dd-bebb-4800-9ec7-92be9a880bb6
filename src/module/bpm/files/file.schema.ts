import { z } from 'zod'
import { extendZod<PERSON>ith<PERSON>penApi } from 'zod-openapi';

extendZodWithOpenApi(z);

export const ListFilesReponseSchema = z.object({
  files: z.array(z.string()),
})

export const UploadFilesResponseSchema = z.object({
  message: z.string(),
  urls: z.array(z.string()),
})

export const UploadFilesByChunkResponseSchema =
  z.object({
    message: z.string(),
    url: z.string(),
  })

export const ChunkNumberResponseSchema = z.object({
  uploadedChunks: z.array(z.number()),
})

export const SpecifiedFile = z.object({
  fileName: z.string().openapi({ example: '487894.jpg' }),
  docId: z.string().openapi({ example: 'd_001' }),
})


export const DocFileSchema = z.object({
  id: z.string().openapi({ example: 'd_001/487894.jpg'}),
  docId: z.string().openapi({ example: 'd_001' }),
  taskId: z.string().nullable().openapi({ example: null }),
  fileName: z.string().openapi({ example: '487894.jpg' }),
  fileType: z.string().openapi({ example: 'image/jpeg' }),
  fileSize: z.number().openapi({ example: 1024 , description: 'in bytes' }),
  uploaderId: z.string().openapi({ example: 'u_001' }),
  uploaderTitle: z.string().openapi({ example: 'John Doe' }),
  uploadedAt: z.date().openapi({ example: "2025-01-01T00:00:00.000Z" }),
})
export type DocFile = z.infer<typeof DocFileSchema>
