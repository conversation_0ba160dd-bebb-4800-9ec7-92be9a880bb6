import { jwtAuthMiddleware } from "@/middleware/auth";
import { openApiMiddleware } from "@/middleware/openapi";
import { Context, Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";

import { DocActionService } from "./docAction.service";
import { 
    DocActionCommentSchema,
    DocActionAddSchema,
    DocActionBackSchema,
    DocActionAgreeSchema
} from "./docAction.schema";
import { SuccessResponseSchema } from "@/module/common/schema";
import { DocDraftFormSchema } from "../docSpec/schema/docForm.schema";

export function createDocActionRouter(
    docActionService: DocActionService
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const spec = openApiMiddleware("DocAction", ['JwtKeyAuth']);

    router.patch('/:docId/edit',
        spec(SuccessResponseSchema, 'Update doc draft data by id'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocDraftFormSchema),
        async (c: Context) => {
            const user = c.get('user');
            const { docId } = c.req.param();
            const formData = await c.req.json();
            await docActionService.updateFormData(user.id, docId, formData);
            return c.json({ success: true});
        }
    );

    router.post('/:docId/cancel',
        spec(SuccessResponseSchema, 'Cancel document action'),
        validator('param', z.object({ docId: z.string() })),
        async (c:Context) => {
            const { docId } = c.req.param()
            const user = c.get('user');
            await docActionService.cancel(user.id, docId)
            return c.json({ success: true })
        }
    )

    router.post('/:docId/disagree',
        spec(SuccessResponseSchema, 'Disagree with document action'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocActionCommentSchema),
        async (c:Context) => {
            const { docId } = c.req.param()
            const { comment } = await c.req.json()
            const user = c.get('user')
            await docActionService.disagree(user.id, docId,comment)
            return c.json({ success: true })
        }
    )

    router.post('/:docId/agree',
        spec(SuccessResponseSchema, 'Agree with document action'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocActionAgreeSchema),
        async (c:Context) => {
            const { docId } = c.req.param()
            const { comment,adds } = await c.req.json()
            const user = c.get('user')
            await docActionService.agree(user.id, docId,comment,adds)
            return c.json({ success: true })
        }
    )

    router.post('/:docId/comment',
        spec(SuccessResponseSchema, 'Add comment to document'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocActionCommentSchema),
        async (c:Context) => {
            const { docId } = c.req.param()
            const { comment } = await c.req.json()
            const user = c.get('user')
            await docActionService.comment(user.id, comment, docId)
            return c.json({ success: true })
        }
    )

    router.post('/:docId/add',
        spec(SuccessResponseSchema, 'Add operator to document'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocActionAddSchema),
        async (c:Context) => {
            const { docId } = c.req.param()
            const adds = await c.req.json()
            const user = c.get('user')
            await docActionService.add(user.id, docId, adds)
            return c.json({ success: true })
        }
    )

    router.post('/:docId/back',
        spec(SuccessResponseSchema, 'Rollback document action'),
        validator('param', z.object({ docId: z.string() })),
        validator('json', DocActionBackSchema),
        async (c:Context) => {
            const { docId } = c.req.param()
            const { toPosition,comment } = await c.req.json()
            const user = c.get('user')
            await docActionService.back(user.id, docId, toPosition,comment)
            return c.json({ success: true })
        }
    )

    return router
}
