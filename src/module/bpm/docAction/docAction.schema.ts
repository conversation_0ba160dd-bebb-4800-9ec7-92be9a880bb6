import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi';
extendZodWithOpenApi(z);

export const DocActionAddSchema = z.object({
    position: z.number().openapi({example: 1}),
    employeeIds: z.array(z.string()).openapi({example: ["FC0056","FC0063"]})
});

export const DocActionAgreeSchema = z.object({
    adds:DocActionAddSchema.optional(),
    comment: z.string().optional().openapi({example: "請修正這個欄位"}),
});

export const DocActionCommentSchema = z.object({
    comment: z.string().openapi({example: "請修正這個欄位"}),
});


export const DocActionBackSchema = z.object({
    toPosition: z.number().openapi({example: 1}),
    comment: z.string().optional().openapi({example: "請修正這個欄位"}),
});
