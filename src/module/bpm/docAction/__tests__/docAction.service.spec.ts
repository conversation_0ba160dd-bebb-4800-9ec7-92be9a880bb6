import { deepcopyBaseDocProps, deepcopyBaseTaskSignProps } from '../../domain/__tests__/domain.mock'
import { DocActionService } from '../docAction.service'
import { DocActionRepository } from '../repository/action.repository'
import { TaskRepository } from '../repository/task.repository'
import { CommonRepository } from '../../common/common.repository'
import { DocStatus, SignAction, SignStatus,OrganizationType, SignTaskMode  } from '@prisma/client'
import { AppError, PermissionError } from '@/errors/app.error'
import { getPrisma } from '@/infra/db'
import { Doc } from '../../domain/Doc'
import { TaskSign } from '../../domain/TaskSign'
import { FlowPartialMode, FlowPartialOrg, FlowPartialRole } from '../../docSpec/schema/docFlow.schema'

jest.mock('@/infra/db')
jest.mock('nanoid', () => ({
    nanoid: () => 'this-is-a-fake-nano-id',
}))
describe('DocActionService', () => {
    let service: DocActionService
    let actionRepo: jest.Mocked<DocActionRepository>
    let taskRepo: jest.Mocked<TaskRepository>
    let commonRepo: jest.Mocked<CommonRepository>
    let mockPrisma: any

    beforeEach(() => {
        actionRepo = {
            findDocById: jest.fn(),
            updateDoc: jest.fn(),
            bulkCreatHistory: jest.fn()
        } as any

        taskRepo = {
            list: jest.fn(),
            find: jest.fn(),
            bulkUpdateTaskSign: jest.fn(),
            createTaskComment: jest.fn(),
            bulkCreateSigns: jest.fn()
        } as any

        commonRepo = {
            userIdToTitle: jest.fn()
        } as any

        mockPrisma = {
            $transaction: jest.fn(callback => callback(mockPrisma))
        }
            ; (getPrisma as jest.Mock).mockReturnValue(mockPrisma)

        service = new DocActionService(actionRepo, taskRepo, commonRepo)
    })

    describe('updateFormData', () => {
        it('should update form data when operator is author', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const formData = { formData: { field1: 'value1' } }

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                authorId: operatorId,
                status: 'in_progress'
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            await service.updateFormData(operatorId, docId, formData)

            expect(actionRepo.updateDoc).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw PermissionError when operator is not author', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const formData = { formData: { field1: 'value1' } }

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                authorId: 'other-user',
                status: 'draft'
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            await expect(service.updateFormData(operatorId, docId, formData))
                .rejects
                .toThrow(PermissionError)
        })
    })

    describe('cancel', () => {
        it('should cancel document and update task signs', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                authorId: operatorId,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            taskRepo.list.mockResolvedValue([
                new TaskSign({ ...deepcopyBaseTaskSignProps(), taskId: 'task1', status: SignStatus.pending }),
                new TaskSign({ ...deepcopyBaseTaskSignProps(), taskId: 'task2', status: SignStatus.ready })
            ])

            await service.cancel(operatorId, docId)

            expect(actionRepo.updateDoc).toHaveBeenCalled()
            expect(taskRepo.bulkUpdateTaskSign).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })
    })

    describe('disagree', () => {
        it('should process disagreement and update task signs', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const comment = 'Disagree comment'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                currentPosition: 1,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            taskRepo.find.mockResolvedValue(new TaskSign({
                ...deepcopyBaseTaskSignProps(),
                taskId: 'task1',
                operatorId: operatorId,
                status: SignStatus.ready,
                position: 1
            }))

            taskRepo.list.mockResolvedValue([
                new TaskSign({ ...deepcopyBaseTaskSignProps(), taskId: 'task2', status: SignStatus.pending, position: 2 })
            ])

            await service.disagree(operatorId, docId, comment)

            expect(actionRepo.updateDoc).toHaveBeenCalled()
            expect(taskRepo.createTaskComment).toHaveBeenCalled()
            expect(taskRepo.bulkUpdateTaskSign).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw error when document is completed', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                currentPosition: null,
                status: DocStatus.approved
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            await expect(service.disagree(operatorId, docId))
                .rejects
                .toThrow(AppError)
        })
    })

    describe('agree', () => {
        it('should process agreement with comment and additional signs, and set next task to ready', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const comment = '同意'

            // 設置當前文件狀態
            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                currentPosition: 0,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            // 設置當前簽核任務
            const currentTask = new TaskSign({
                ...deepcopyBaseTaskSignProps(),
                taskId: 'currentTask1',
                operatorId,
                status: SignStatus.ready,
                action: SignAction.null,
                position: 1
            })

            // 設置下一個簽核任務
            const nextTask = new TaskSign({
                ...deepcopyBaseTaskSignProps(),
                taskId: 'nextTask2',
                status: SignStatus.pending,
                action: SignAction.null,
                position: 2
            })

            // 模擬查詢當前任務
            taskRepo.find
                .mockResolvedValueOnce(currentTask)
                .mockResolvedValueOnce(nextTask);

            // 模擬查詢下個任務(非當前簽核)
            taskRepo.list.mockResolvedValue([
                new TaskSign({ ...nextTask.props, taskId: 'nextTask2', status: SignStatus.ready }),
                new TaskSign({ ...nextTask.props, taskId: 'nextTask3', status: SignStatus.ready })
            ])

            await service.agree(operatorId, docId, comment)
            // 驗證文件更新
            // console.log(JSON.stringify(taskRepo.bulkUpdateTaskSign.mock.calls),null,2);
            console.dir(JSON.stringify(taskRepo.bulkUpdateTaskSign.mock.calls[1][0],null,2), { depth: null });

            // expect(actionRepo.updateDoc).toHaveBeenCalledWith(
            //     expect.objectContaining({
            //         id: docId,
            //         currentPosition: 1
            //     }),
            //     expect.anything()
            // )

            // 驗證任務狀態更新
            // expect(taskRepo.bulkUpdateTaskSign).toHaveBeenNthCalledWith(
                //     1,
                //     expect.arrayContaining([
                //         expect.objectContaining({
                //             taskId: 'currentTask1',
                //             status: SignStatus.signed
                //         })
                //     ]),
                //     expect.anything()
                // )
            // expect(taskRepo.bulkUpdateTaskSign).toHaveBeenCalled()
           
            // expect(taskRepo.bulkUpdateTaskSign).toHaveBeenNthCalledWith(
            //     2, // 第二次呼叫
            //     expect.arrayContaining([
            //       expect.objectContaining({
            //         taskId: 'nextTask2',
            //         status: 'ready',action: 'null',
            //       }),
            //       expect.objectContaining({
            //         taskId: 'nextTask3',
            //         status: 'ready',action: 'null',
            //       })
            //     ]),
            //     expect.anything() // 第二個參數是 null，但你可以寫 anything 保險一點
            //   );

            // expect(taskRepo.bulkUpdateTaskSign).toHaveBeenNthCalledWith(
            //     2,
            //     expect.arrayContaining([
            //       expect.objectContaining({ taskId: 'nextTask2', status: 'ready', action: 'null' }),
            //       expect.objectContaining({ taskId: 'nextTask3', status: 'ready', action: 'null' }),
            //     ]),
            //     expect.any(Object) // or expect.any(Function) if it's a function

            //     // expect.anything() // 🔥你需要加上這個，因為你 mock 了 bulkUpdateTaskSign 傳了兩個參數！
            //   );
            expect(taskRepo.createTaskComment).toHaveBeenCalled()
            // expect(taskRepo.bulkCreateSigns).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw error when it is not user\'s turn', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                currentPosition: 1,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            taskRepo.find.mockResolvedValue(null)

            await expect(service.agree(operatorId, docId))
                .rejects
                .toThrow(AppError)
        })
    })

    describe('comment', () => {
        it('should add comment successfully', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const comment = '這是一個評論'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })
            await service.comment(operatorId, comment, docId)

            expect(taskRepo.createTaskComment).toHaveBeenCalledWith(
                expect.objectContaining({
                    docId,
                    operatorId,
                    comment
                }),
                expect.anything()
            )
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })
    })

    describe('add', () => {
        it('should add new task signs', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const add = { position: 2, employeeIds: ['emp1', 'emp2'] }

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            await service.add(operatorId, docId, add)

            expect(taskRepo.bulkCreateSigns).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw error when position is not integer', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const add = { position: 2.5, employeeIds: ['emp1'] }

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                status: DocStatus.in_progress
            }))
            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            await expect(service.add(operatorId, docId, add))
                .rejects
                .toThrow(AppError)
        })
    })

    describe('back', () => {
        it('should process back operation with comment', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const toPosition = 1
            const comment = '請重新檢查'

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                status: DocStatus.in_progress
            }))

            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })

            taskRepo.list.mockResolvedValue([
                new TaskSign({ ...deepcopyBaseTaskSignProps(), taskId: 'task1', status: SignStatus.signed, position: 1 }),
                new TaskSign({ ...deepcopyBaseTaskSignProps(), taskId: 'task2', status: SignStatus.ready, position: 2 })
            ])

            await service.back(operatorId, docId, toPosition, comment)

            expect(actionRepo.updateDoc).toHaveBeenCalled()
            expect(taskRepo.bulkUpdateTaskSign).toHaveBeenCalled()
            expect(taskRepo.createTaskComment).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw error when toPosition is not integer', async () => {
            const operatorId = 'user1'
            const docId = 'doc1'
            const toPosition = 1.5

            actionRepo.findDocById.mockResolvedValue(new Doc({
                ...deepcopyBaseDocProps(),
                id: docId,
                status: DocStatus.in_progress
            }))
            commonRepo.userIdToTitle.mockResolvedValue({
                id: operatorId,
                title: 'User 1'
            })
            await expect(service.back(operatorId, docId, toPosition))
                .rejects
                .toThrow(AppError)
        })
    })


    describe('submitDraft', () => {

        const mockDocProps = {...deepcopyBaseDocProps(),
            flowData: [
                { "mode": FlowPartialMode.role, "type": OrganizationType.department  } as FlowPartialRole,
                [{ "mode": FlowPartialMode.role, "type": OrganizationType.division  } as FlowPartialRole,{ "mode": FlowPartialMode.role, "type":  OrganizationType.division } as FlowPartialRole],
                { "mode": FlowPartialMode.role, "type":  OrganizationType.company  } as FlowPartialRole
            ],
            stamps:[
                { "mode": FlowPartialMode.org, "orgId": "o_department_finance", "isManager": true } as FlowPartialOrg,
                { "mode": FlowPartialMode.org, "orgId": "o_department_finance", "isManager": true } as FlowPartialOrg
            ]
        }
        const mockDoc = new Doc(mockDocProps)
        const mockAuthorTask = new TaskSign(deepcopyBaseTaskSignProps())

        beforeEach(() => {
            const mockUsers: Record<string, { id: string; title: string }> = {
                'FC0056': { id: 'FC0056', title: 'Mike' },
                'user-2': { id: 'user-2', title: '主管' },
                'user-3': { id: 'user-3', title: '經理A' },
                'user-4': { id: 'user-4', title: '經理B' },
                'user-5': { id: 'user-5', title: '總監' },
                'user-6': { id: 'user-6', title: '蓋章人A' },
                'user-7': { id: 'user-7', title: '蓋章人B' }
            }
            let counter = 0;
            const users = ['user-2', 'user-3', 'user-4', 'user-5', 'user-6', 'user-7'];
            service['flowSelect'] = jest.fn().mockImplementation(() => {
                return users[counter++];
            });
            commonRepo.userIdToTitle.mockImplementation((userId: string) => {
                // Add default mock response for unknown users
                return Promise.resolve(mockUsers[userId] || { id: userId, title: 'Default Title' })
            })
            actionRepo.findDocById.mockResolvedValue(mockDoc)
            taskRepo.findFirstOrThrow.mockResolvedValue(mockAuthorTask)
        })

        it('should submit draft and create all task signs successfully', async () => {
            let capturedSigns: TaskSign[] = []
            taskRepo.bulkCreateSigns.mockImplementation((signs) => {
                capturedSigns = signs.map(sign => new TaskSign(sign))
                return Promise.resolve()
            })
            await service.submit(mockDocProps.authorId, mockDocProps.id,[
                { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
                { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: true }
            ])
            // console.log(capturedSigns)
            // 驗證是否建立了正確數量的簽核任務（1作者 + 1個簽核人 + (2個並簽) + 1個簽核人 +  2個蓋章）
            expect(capturedSigns).toHaveLength(7)
        
            // 驗證簽核流程的任務
            const flowSigns = capturedSigns.slice(0, 5)// 0~4

            expect(flowSigns[0].props.operatorId).toBe('FC0056')
            expect(flowSigns[0].props.mode).toBe(SignTaskMode.sign)
            expect(flowSigns[0].props.status).toBe(SignStatus.signed)
            expect(flowSigns[0].props.position).toBe(0)

            expect(flowSigns[1].props.operatorId).toBe('user-2')
            expect(flowSigns[1].props.mode).toBe(SignTaskMode.sign)
            expect(flowSigns[1].props.status).toBe(SignStatus.ready)
            expect(flowSigns[1].props.position).toBe(1)

            expect(flowSigns[2].props.operatorId).toBe('user-3')
            expect(flowSigns[2].props.mode).toBe(SignTaskMode.added)
            expect(flowSigns[2].props.status).toBe(SignStatus.pending)
            expect(flowSigns[2].props.position).toBe(1.5)

            expect(flowSigns[3].props.operatorId).toBe('user-4')
            expect(flowSigns[3].props.mode).toBe(SignTaskMode.added)
            expect(flowSigns[3].props.status).toBe(SignStatus.pending)
            expect(flowSigns[3].props.position).toBe(1.5)

            expect(flowSigns[4].props.operatorId).toBe('user-5')
            expect(flowSigns[4].props.mode).toBe(SignTaskMode.sign)
            expect(flowSigns[4].props.status).toBe(SignStatus.pending)
            expect(flowSigns[4].props.position).toBe(2)

            // 驗證蓋章任務
            const stampSigns = capturedSigns.slice(5)//5~end

            expect(stampSigns[0].props.operatorId).toBe('user-6')
            expect(stampSigns[0].props.mode).toBe(SignTaskMode.inform)
            expect(stampSigns[0].props.status).toBe(SignStatus.pending)
            expect(stampSigns[0].props.position).toBe(3)

            expect(stampSigns[1].props.operatorId).toBe('user-7')
            expect(stampSigns[1].props.mode).toBe(SignTaskMode.inform)
            expect(stampSigns[1].props.status).toBe(SignStatus.pending)
            expect(stampSigns[1].props.position).toBe(4)

            // 驗證其他必要的方法調用
            expect(actionRepo.updateDoc).toHaveBeenCalled()
            expect(taskRepo.bulkCreateSigns).toHaveBeenCalled()
            expect(actionRepo.bulkCreatHistory).toHaveBeenCalled()
        })

        it('should throw PermissionError when user is not author', async () => {
            await expect(service.submit('other-user', mockDocProps.id,[
                { mode: FlowPartialMode.org, orgId: 'o_department_finance', isManager: true },
                { mode: FlowPartialMode.org, orgId: 'o_department_legal', isManager: true }
            ]))
                .rejects
                .toThrow(PermissionError)

            // expect(actionRepo.findDocById).toHaveBeenCalledWith(mockDocProps.id)
            // expect(taskRepo.findFirstOrThrow).not.toHaveBeenCalled()
        })
    })
})