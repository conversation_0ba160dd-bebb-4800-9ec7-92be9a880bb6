import { Prisma, PrismaClient } from "@prisma/client";
import { DocHistoryMapper } from "../../domain/mapper/history.mapper";
import { CatchRepositoryError } from "@/utils/repoDecorator";
import { DocMapper } from "../../domain/mapper/doc.mapper";
import { DocHistoryProps, DocProps} from "../../domain/schema/domain.schema";
import { Doc } from "../../domain/Doc";


export class DocActionRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async updateDoc(props: DocProps, tx?: Prisma.TransactionClient):Promise<void> {
        const db = tx ?? this.prisma
        await db.doc.update({
            where: { id: props.id },
            data: DocMapper.toPersistence(props)
        })
    }


    @CatchRepositoryError()
    async findDocById(docId: string, tx?: Prisma.TransactionClient): Promise<Doc> {
        const db = tx ?? this.prisma
        const result = await db.doc.findUniqueOrThrow({ where: { id: docId } })
        return DocMapper.toDomain(result)
    }

    @CatchRepositoryError()
    async bulkCreatHistory(props: DocHistoryProps[], tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma;
        await db.doc_history.createMany({
            data: props.map(t => DocHistoryMapper.toCreateMany(t)),
        });
    }
}