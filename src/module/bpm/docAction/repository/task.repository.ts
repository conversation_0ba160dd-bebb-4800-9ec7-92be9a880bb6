import { CatchRepositoryError } from "@/utils/repoDecorator";
import { EmployeeRole, OrganizationType, Prisma, PrismaClient } from "@prisma/client";
import { TaskSignMapper } from "../../domain/mapper/taskSign.mapper";
import { TaskCommentMapper } from "../../domain/mapper/taskComment.mapper";
import { TaskCommentProps, TaskSignProps } from "../../domain/schema/domain.schema";
import { TaskSignListFilter } from "../../domain/schema/query.schema";
import { TaskSign } from "../../domain/TaskSign";

export class TaskRepository {
    constructor(private readonly prisma: PrismaClient) { }

    @CatchRepositoryError()
    async createTaskComment(props: TaskCommentProps, tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma;
        await db.task_comment.create({
            data: TaskCommentMapper.toPersistence(props)
        });
    }

    @CatchRepositoryError()
    async bulkUpdateTaskSign(propsList: TaskSignProps[], tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma;
        await Promise.all(
            propsList.map(async (props) =>
                await db.task_sign.update({
                    where: { task_id: props.taskId },
                    data: TaskSignMapper.toPersistence(props)
                })
            ))
    }

    @CatchRepositoryError()
    async bulkCreateSigns(propsList: TaskSignProps[], tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma
        await db.task_sign.createMany({
            data: propsList.map(t => TaskSignMapper.toCreateMany(t)),
        });
    }

    @CatchRepositoryError()
    async list(query: TaskSignListFilter, tx?: Prisma.TransactionClient): Promise<TaskSign[]> {
        const db = tx ?? this.prisma;
        const position = query.position !== undefined || query.gtPosition !== undefined || query.gtePosition !== undefined || query.ltPosition !== undefined || query.ltePosition !== undefined
            ? {
                position: {
                    ...(query.position !== undefined && { equals: query.position }),
                    ...(query.gtPosition !== undefined && { gt: query.gtPosition }),
                    ...(query.gtePosition !== undefined && { gte: query.gtePosition }),
                    ...(query.ltPosition !== undefined && { lt: query.ltPosition }),
                    ...(query.ltePosition !== undefined && { lte: query.ltePosition }),
                },
            } : {}
        const status = query.status || query.inStatus
            ? {
                status: {
                    ...(query.status && { equals: query.status }),
                    ...(query.inStatus && { in: query.inStatus }),
                },
            } : {}
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.operatorId && { operator_id: query.operatorId }),
            ...(query.action && { action: query.action }),
            ...position,
            ...status,
            ...(query.excludeTaskId && { NOT: { task_id: query.excludeTaskId } })
        }
        const list = await db.task_sign.findMany({
            where,
            orderBy: [{ position: 'asc' }, { created_at: 'asc' }]
        });
        return list.map(task => TaskSignMapper.toDomain(task));
    }

    @CatchRepositoryError()
    async find(query: TaskSignListFilter, tx?: Prisma.TransactionClient): Promise<TaskSign | null> {
        const db = tx ?? this.prisma;
        const position = query.position !== undefined || query.gtPosition !== undefined || query.gtePosition !== undefined || query.ltPosition !== undefined || query.ltePosition !== undefined
            ? {
                position: {
                    ...(query.position !== undefined && { equals: query.position }),
                    ...(query.gtPosition !== undefined && { gt: query.gtPosition }),
                    ...(query.gtePosition !== undefined && { gte: query.gtePosition }),
                    ...(query.ltPosition !== undefined && { lt: query.ltPosition }),
                    ...(query.ltePosition !== undefined && { lte: query.ltePosition }),
                },
            } : {}
        const status = query.status || query.inStatus
            ? {
                status: {
                    ...(query.status && { equals: query.status }),
                    ...(query.inStatus && { in: query.inStatus }),
                },
            } : {}
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.operatorId && { operator_id: query.operatorId }),
            ...(query.action && { action: query.action }),
            ...position,
            ...status,
            ...(query.excludeTaskId && { NOT: { task_id: query.excludeTaskId } })
        }
        const result = await db.task_sign.findFirst({
            where,
            orderBy: [{ position: 'asc' }, { created_at: 'asc' }]
        });
        return result !== null ? TaskSignMapper.toDomain(result) : null
    }

    @CatchRepositoryError()
    async findFirstOrThrow(query: TaskSignListFilter, tx?: Prisma.TransactionClient): Promise<TaskSign> {
        const db = tx ?? this.prisma;
        const position = query.position !== undefined || query.gtPosition !== undefined || query.gtePosition !== undefined || query.ltPosition !== undefined || query.ltePosition !== undefined
            ? {
                position: {
                    ...(query.position !== undefined && { equals: query.position }),
                    ...(query.gtPosition !== undefined && { gt: query.gtPosition }),
                    ...(query.gtePosition !== undefined && { gte: query.gtePosition }),
                    ...(query.ltPosition !== undefined && { lt: query.ltPosition }),
                    ...(query.ltePosition !== undefined && { lte: query.ltePosition }),
                },
            } : {}
        const status = query.status || query.inStatus
            ? {
                status: {
                    ...(query.status && { equals: query.status }),
                    ...(query.inStatus && { in: query.inStatus }),
                },
            } : {}
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.operatorId && { operator_id: query.operatorId }),
            ...(query.action && { action: query.action }),
            ...position,
            ...status,
            ...(query.excludeTaskId && { NOT: { task_id: query.excludeTaskId } })
        }
        const result = await db.task_sign.findFirstOrThrow({
            where,
            orderBy: [{ position: 'asc' }, { created_at: 'asc' }]
        });
        return TaskSignMapper.toDomain(result)
    }

    @CatchRepositoryError()
    async bulkDelete(query: TaskSignListFilter, tx?: Prisma.TransactionClient): Promise<void> {
        const db = tx ?? this.prisma;
        const position = query.position !== undefined || query.gtPosition !== undefined || query.gtePosition !== undefined || query.ltPosition !== undefined || query.ltePosition !== undefined
            ? {
                position: {
                    ...(query.position !== undefined && { equals: query.position }),
                    ...(query.gtPosition !== undefined && { gt: query.gtPosition }),
                    ...(query.gtePosition !== undefined && { gte: query.gtePosition }),
                    ...(query.ltPosition !== undefined && { lt: query.ltPosition }),
                    ...(query.ltePosition !== undefined && { lte: query.ltePosition }),
                },
            } : {}
        const status = query.status || query.inStatus
            ? {
                status: {
                    ...(query.status && { equals: query.status }),
                    ...(query.inStatus && { in: query.inStatus }),
                },
            } : {}
        const where = {
            ...(query.docId && { doc_id: query.docId }),
            ...(query.operatorId && { operator_id: query.operatorId }),
            ...(query.action && { action: query.action }),
            ...position,
            ...status,
            ...(query.excludeTaskId && { NOT: { task_id: query.excludeTaskId } })
        }
        await db.task_sign.deleteMany({ where })
    }


    // find FlowPartial
    @CatchRepositoryError()
    async findFlowPartialOrgToEmployeeId(orgId: string, tx?: Prisma.TransactionClient): Promise<string | null> {
        const db = tx ?? this.prisma
        const result = await db.employee_assignment.findFirst({
            where: {
                org_id: orgId,
                role: EmployeeRole.manager,
                end_date: null
            }
        })
        if (result&&result.employee_id) {
            return result.employee_id;
        }
        return null;
    }

    @CatchRepositoryError()
    async findFlowPartialRoleToEmployeeId(targetType: OrganizationType, userId: string, tx?: Prisma.TransactionClient): Promise<string | null> {
        const db = tx ?? this.prisma
        // 找出最新一筆 assignment（可依實際情況調整）
        const assignment = await db.employee_assignment.findFirst({
            where: { employee_id: userId, end_date: null },
            orderBy: { start_date: 'desc' },
            include: { organization: true },
        });

        if (!assignment) return null;

        // 從該 assignment 的 org 開始往上找，直到找到 targetType 組織
        let org = assignment.organization;
        while (org && org.type !== targetType) {
            if (!org.parent_id) return null;
            const tempOrg = await db.organization.findUnique({ where: { id: org.parent_id } });
            if (tempOrg !== null) org = tempOrg;
        }

        // 找到 supervisor assignment 並回傳該 employee 
        const supervisorAssignment = await db.employee_assignment.findFirstOrThrow({
            where: {
                org_id: org.id,
                role: EmployeeRole.manager,
                end_date: null,
                employee_id: { not: userId } // filter self
            },
        });

        return supervisorAssignment.employee_id;
    }
}