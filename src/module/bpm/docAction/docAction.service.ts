import { getPrisma } from "@/infra/db"
import { TaskSign } from "../domain/TaskSign"
import { DocActionRepository } from "./repository/action.repository"
import { CommonRepository } from "../common/common.repository"
import { TaskRepository } from "./repository/task.repository"
import { Prisma, SignAction, SignStatus, SignTaskMode } from "@prisma/client"
import { AppError, PermissionError, ValidationError } from "@/errors/app.error"
import { isInteger } from "@/utils/func"
import { DraftFormData } from "../docSpec/schema/docForm.schema"
import { Doc } from "../domain/Doc"
import { domainListToProps } from "@/utils/toProps"
import { FlowNumber, FlowPartialOrg, FlowPartialPerson, FlowPartialRole, SpecFlow, StampsFlow } from "../docSpec/schema/docFlow.schema"
import { genTaskSignId } from "@/utils/genId"
import { TaskSignProps } from "../domain/schema/domain.schema"
/**
 * note: emit, 因為emit的查詢不在tx內, 所以必須將tx完成後再emit, 否則查不到過程中的變化
 */
export class DocActionService {
    constructor(
        private readonly actionRepo: DocActionRepository,
        private readonly taskRepo: TaskRepository,
        private readonly commonRepo: CommonRepository
    ) { }

    async submit(operatorId: string, docId: string, stampsFlow: StampsFlow): Promise<Doc> {
        /**
        * createDoc
        * docSpec create draft: create autho task/file/doc
        * draft submit: create else task
        */
        const doc = await this.actionRepo.findDocById(docId)
        if (doc.props.status !== 'draft')
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'Doc status is not draft.' })
        if (operatorId !== doc.props.authorId) throw new PermissionError('PermissionError', 'Only author can submit.')
        const now = new Date()
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const prisma = getPrisma()
        const submittedDoc = await prisma.$transaction(async (tx) => {
            // update doc
            const authorTask = doc.submit(operator.id, operator.title, stampsFlow, now)
            await this.actionRepo.updateDoc(doc.props, tx)

            // sign
            const signs = this.flowAddPosition(0, doc.props.flowData)
            const stamps = this.flowAddPosition(signs.length >= 1 ? signs[signs.length - 1].position : 0, stampsFlow)
            const tasks = await this.toTaskSigns(operatorId, docId, now, signs, stamps)
            await this.taskRepo.bulkCreateSigns([authorTask.props, ...domainListToProps(tasks)], tx)

            // agree author task
            await this._agree(operator.id, operator.title, doc, tx)
            
            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
            return doc
        })
        doc.emit()
        return submittedDoc
    }

    async updateFormData(operatorId: string, docId: string, formData: DraftFormData): Promise<void> {
        const doc = await this.actionRepo.findDocById(docId)
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        if (operatorId !== doc.props.authorId)
            throw new PermissionError('PermissionError', 'Only author can edit.')

        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            doc.updateFormData(operator.id, operator.title, formData)
            // update doc
            await this.actionRepo.updateDoc(doc.props, tx)

            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(histories.map(domain => domain.props), tx)
        })
        doc.emit()
    }

    async cancel(operatorId: string, docId: string): Promise<void> {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            const notifySigneds = await this.taskRepo.list({
                docId: doc.props.id,
                inStatus: [SignStatus.signed, SignStatus.ready],
            }, tx)
            const updDisableUnSigneds = await this.taskRepo.list({
                docId: doc.props.id,
                inStatus: [SignStatus.pending, SignStatus.ready],
            }, tx)
            doc.cancel(operator.id, operator.title, notifySigneds, updDisableUnSigneds)
            // update doc
            await this.actionRepo.updateDoc(doc.props, tx)
            await this.taskRepo.bulkUpdateTaskSign(updDisableUnSigneds.map(d => d.props), tx)

            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    async disagree(operatorId: string, docId: string, comment?: string): Promise<void> {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            // comment
            if (comment) await this._comment(operator.id, operator.title, doc, comment, tx)
            // disagree
            await this._disagree(operator.id, operator.title, doc, tx)
            // update doc
            await this.actionRepo.updateDoc(doc.props, tx)

            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    async agree(operatorId: string, docId: string, comment?: string, add?: { position: number, employeeIds: string[] }): Promise<void> {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            // comment
            if (comment) await this._comment(operator.id, operator.title, doc, comment, tx)
            // add
            if (add) await this._add(operator.id, operator.title, doc, add, tx)
            // agree
            await this._agree(operator.id, operator.title, doc, tx)
            // update doc
            await this.actionRepo.updateDoc(doc.props, tx)

            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    async comment(operatorId: string, comment: string, docId: string): Promise<void> {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            await this._comment(operator.id, operator.title, doc, comment, tx)
            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    async add(operatorId: string, docId: string, add: { position: number, employeeIds: string[] }): Promise<void> {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            await this._add(operator.id, operator.title, doc, add, tx)
            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    async back(operatorId: string, docId: string, toPosition: number, comment?: string) {
        const operator = await this.commonRepo.userIdToTitle(operatorId)
        const doc = await this.actionRepo.findDocById(docId)
        const prisma = getPrisma()
        await prisma.$transaction(async (tx) => {
            // comment
            if (comment) await this._comment(operator.id, operator.title, doc, comment, tx)
            // back
            await this._back(operator.id, operator.title, doc, toPosition, tx)
            // update doc
            await this.actionRepo.updateDoc(doc.props, tx)

            // history
            const histories = doc.commit()
            await this.actionRepo.bulkCreatHistory(domainListToProps(histories), tx)
        })
        doc.emit()
    }

    private async _agree(operatorId: string, operatorTitle: string, doc: Doc, tx: Prisma.TransactionClient) {
        if (doc.readyPosition === null)
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'Doc status has been done' })
        /**
         * 1. 此次簽核人: findunique(taskSign.status=ready,operatorId=operatorId)
         * 2. 未來簽核人: findMany(taskSign.status=ready||pending,excludeTaskId:agreeSign.id orderby[position asc,created_at asc])
         * 3. domain:  agree this sign
         * 4. set ready / done/ none
         */
        const agreeSign = await this.taskRepo.find({
            docId: doc.props.id, operatorId,
            status: SignStatus.ready,
            gtePosition: doc.readyPosition
        }, tx)
        if (agreeSign === null)
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'It is not your turn.' })
        doc.agree(operatorId, operatorTitle, agreeSign)// return ready

        await this.taskRepo.bulkUpdateTaskSign([agreeSign.props], tx)
        // set ready or done
        const nextSign = await this.taskRepo.find({
            docId: doc.props.id,
            inStatus: [SignStatus.pending, SignStatus.ready],
            gtePosition: doc.readyPosition,
            // ltePosition: doc.readyPosition + 1,// 有可能0,2,3 其中1可能被跳過(e.g. 主管申請)
        }, tx)
        if (nextSign !== null && nextSign.props.position !== agreeSign.props.position) {//往下一層簽(同層簽完)
            await this._agreeSetReady(doc, nextSign.props.position, tx)
        }
        if (nextSign === null) {// 全部完成
            doc._done()
        }
    }

    private async _agreeSetReady(doc: Doc, setPosition: number, tx: Prisma.TransactionClient): Promise<void> {
        const nexts = await this.taskRepo.list({
            docId: doc.props.id,
            status: SignStatus.pending,
            position: setPosition
        }, tx)
        doc._ready(nexts)
        await this.taskRepo.bulkUpdateTaskSign(nexts.map(d => d.props), tx)
        await this.actionRepo.updateDoc(doc.props, tx)
    }

    private async _disagree(operatorId: string, operatorTitle: string, doc: Doc, tx: Prisma.TransactionClient) {
        if (doc.readyPosition === null)
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'Doc status has been done' })
        const disagreeSign = await this.taskRepo.find({
            docId: doc.props.id, operatorId,
            status: SignStatus.ready,
            gtePosition: doc.readyPosition
        }, tx)
        if (disagreeSign === null)
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'It is not your turn.' })
        const disableSigns = await this.taskRepo.list({
            docId: doc.props.id,
            inStatus: [SignStatus.pending, SignStatus.ready],
            excludeTaskId: disagreeSign.props.taskId
        }, tx)
        if (disagreeSign === null)
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'It is not your turn.' })
        doc.disagree(operatorId, operatorTitle, disagreeSign, disableSigns)
        await this.taskRepo.bulkUpdateTaskSign([disagreeSign.props, ...disableSigns.map(t => t.props)], tx)
    }

    private async _comment(operatorId: string, operatorTitle: string, doc: Doc, comment: string, tx: Prisma.TransactionClient) {
        const newComment = doc.comment(operatorId, operatorTitle, comment)
        await this.taskRepo.createTaskComment(newComment.props, tx)
    }

    private async _add(operatorId: string, operatorTitle: string, doc: Doc, add: { position: number, employeeIds: string[] }, tx?: Prisma.TransactionClient) {
        if (!isInteger(add.position))
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'add position must be integer.' })
        const addSignTasks: TaskSign[] = []
        for (const id of add.employeeIds) {
            const assigneed = await this.commonRepo.userIdToTitle(id)
            const newTask = doc.add(operatorId, operatorTitle, add.position, assigneed.id, assigneed.title)
            addSignTasks.push(newTask)
        }
        await this.taskRepo.bulkCreateSigns(domainListToProps(addSignTasks), tx)
    }

    private async _back(operatorId: string, operatorTitle: string, doc: Doc, toPosition: number, tx: Prisma.TransactionClient) {
        if (!isInteger(toPosition))
            throw new AppError({ code: 'DOC_ACTION_ERROR', message: 'add position must be integer.' })

        const signeds = await this.taskRepo.list({
            docId: doc.props.id,
            ltePosition: doc.readyPosition!,
            gtePosition: toPosition,
            inStatus: [SignStatus.signed, SignStatus.ready]
        }, tx)
        doc.back(operatorId, operatorTitle, toPosition, signeds)
        await this.taskRepo.bulkUpdateTaskSign(signeds.map(d => d.props), tx)
    }


    protected flowAddPosition(init: number, flow: SpecFlow | StampsFlow): FlowNumber[] {
        let currentPosition = init
        const result = []
        for (const node of flow) {
            if (node instanceof Array) {
                currentPosition = Number.isInteger(currentPosition) ? currentPosition + 0.5 : currentPosition
                for (const part of node) {
                    result.push({ ...part, position: currentPosition })
                }
            } else {
                currentPosition = Number.isInteger(currentPosition) ? currentPosition + 1 : currentPosition + 0.5
                result.push({ ...node, position: currentPosition })
            }
        }
        return result
    }

    protected async toTaskSigns(userId: string, docId: string, now: Date, signs: FlowNumber[], stamps: FlowNumber[]): Promise<TaskSign[]> {
        const tasks: TaskSign[] = []
        for (const sign of signs) {
            const empId = await this.flowSelect(sign, userId)
            if (empId === null) continue
            const operator = await this.commonRepo.userIdToTitle(empId)
            const task = new TaskSign({
                taskId: genTaskSignId(),
                docId: docId,
                operatorId: empId,
                operatorTitle: operator.title,
                mode: Number.isInteger(sign.position) ? SignTaskMode.sign : SignTaskMode.added,
                status: SignStatus.pending,
                action: SignAction.null,
                position: sign.position,
                createdAt: now,
                updatedAt: now,
                completedAt: null,
            } as TaskSignProps)
            tasks.push(task)
        }
        for (const stamp of stamps) {
            const empId = await this.flowSelect(stamp, userId)
            if (empId === null) continue
            const operator = await this.commonRepo.userIdToTitle(empId)
            const task = new TaskSign({
                taskId: genTaskSignId(),
                docId: docId,
                operatorId: empId,
                operatorTitle: operator.title,
                mode: SignTaskMode.inform,
                status: SignStatus.pending,
                action: SignAction.null,
                position: stamp.position,
                createdAt: now,
                updatedAt: now,
                completedAt: null,
            } as TaskSignProps)
            tasks.push(task)
        }
        return tasks
    }

    protected async flowSelect(node: FlowPartialPerson | FlowPartialOrg | FlowPartialRole, userId: string): Promise<string | null> {
        switch (node.mode) {
            case 'person':
                return await this.flowFetchByPerson(node)
            case 'org':
                return await this.flowFetchByOrg(node)
            case 'role':
                return await this.flowFetchByRole(node, userId)
        }
        throw new ValidationError('Undeinfined flow mode', `Undeinfined ${node.mode} flow mode`)
    }

    protected async flowFetchByPerson(flowPartialPerson: FlowPartialPerson): Promise<string | null> {
        return flowPartialPerson.employeeId
    }

    protected async flowFetchByOrg(flowPartialOrg: FlowPartialOrg): Promise<string | null> {
        const result = await this.taskRepo.findFlowPartialOrgToEmployeeId(flowPartialOrg.orgId)
        if (result === null) console.warn(`can not find ${flowPartialOrg.orgId} org manager`)
        return result
    }

    protected async flowFetchByRole(flowPartialRole: FlowPartialRole, userId: string): Promise<string | null> {
        const result = await this.taskRepo.findFlowPartialRoleToEmployeeId(flowPartialRole.type, userId)

        if (result === null) console.warn(`can not find ${userId}'s ${flowPartialRole.type} manager`)
        return result
    }

}