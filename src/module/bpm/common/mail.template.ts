import { env } from "@/env"
import { EventTopic, TopicDocNotify, TopicDocResult } from "../domain/DomainEmitter"
export type ValidEventTopic =
    | TopicDocResult.APPROVED // email author
    | TopicDocResult.REJECTED // email author
    | TopicDocResult.CANCELED //email 除了發起人, 其他都會收到
    | TopicDocNotify.MARK_READY // email signer


interface MailContenTemplateProps {
    name: string
    docId: string
    completedAt: string
}
// TODO: completedAt 要轉換成 locale string
export const MailTemplate: Record<
    ValidEventTopic,
    (args: MailContenTemplateProps) => {
        subject: string,
        content: string
    }
> = {
    [TopicDocResult.APPROVED]: (args: MailContenTemplateProps) => ({
        subject: 'Approval Notification - Form Approved',
        content:
            `
            <p>Hello,</p>
            <p></p>
            <p>The form "${args.name}" (ID: ${args.docId}) you submitted has been fully approved.</p>
            <p></p>
            <p>Please follow up with the next steps accordingly.</p>
            <p></p>
            Thank you.
            `
    }),
    [TopicDocResult.REJECTED]: (args: MailContenTemplateProps) => ({
        subject: 'Approval Result Notification - Form Rejected',
        content:
            `
            <p>Hello,</p>
            <p></p>
            <p>The form "${args.name}" (ID: ${args.docId}) you submitted was rejected.</p>
            <p></p>
            <p>Please revise it based on the provided feedback and resubmit, or contact the relevant personnel.</p>
            <p></p>
            <p>Thank you.</p>
            `
    }),
    [TopicDocNotify.MARK_READY]: (args: MailContenTemplateProps) => ({
        subject: 'Approval Reminder - Action Required',
        content:
            `
            <p>Hello,</p>
            <p> </p>
            <p>A form titled "${args.name}" (ID: ${args.docId}) is currently pending your approval.</p>
            <p>Approval link: <a href="${env.FRONTEND_URL}/module/bpm?page=doc-detail/${args.docId}&lang=zh-TW">${env.FRONTEND_URL}/module/bpm?page=doc-detail/${args.docId}&lang=zh-TW</a></p>
            <p> </p>
            <p>Thank you.</p>
            `
    }),
    [TopicDocResult.CANCELED]: (args: MailContenTemplateProps) => ({
        subject: 'Approval Notification - Form Canceled',
        content:
            `
            <p>Hello, </p>
            <p> </p>
            <p>The form "${args.name}" (ID: ${args.docId}) that you participated in reviewing was canceled. </p>
            <p> </p>
            <p>If you have any questions, please contact the responsible party. </p>
            <p> </p>
            <p>Thank you. </p>
            `
    })
}


export function generateMailByTopic(
    topic: EventTopic
): Function | null {
    const template = MailTemplate[topic as keyof typeof MailTemplate];

    if (!template) {
        // 若非支援的 topic，不處理（也可以改為 throw）
        return null;
    }

    return template;
}