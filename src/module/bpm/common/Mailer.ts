import { MicrosoftGraphAP<PERSON> } from "@/infra/notify/email";
import { DomainEmitter, DomainEvent } from "../domain/DomainEmitter";
import { generateMailByTopic, } from "./mail.template";
import { CommonRepository } from "./common.repository";
import { Doc } from "../domain/Doc";
import { env } from "@/env";
import { AwsSes } from "@/infra/AwsSes";

export class DomainMailer {
    private emitter: DomainEmitter
    private mailer: AwsSes
    private commonRepo: CommonRepository
    constructor(commonRepo: CommonRepository) {
        this.commonRepo = commonRepo
        this.mailer = AwsSes.getInstance()
        this.emitter = DomainEmitter.getInstance()
        /**
        * Temporarily disable the mail function
        **/
        if (env.ENABLE_MAIL) {
            this.emitter.on('event', (event) => this.send(event))
        }
    }

    async send(event: DomainEvent) {
        const template = generateMailByTopic(event.topic)
        if (template) {
            try {
                const doc = await this.findDoc(event.docId)
                const receivers = await this.findReceivers(event.receivers)
                const result = template({
                    name: doc.props.name,
                    docId: doc.props.id,
                    completedAt: doc.props.completedAt,
                })
                await this.mailer.sendEmail({
                    to: receivers,
                    subject: result.subject,
                    content: result.content
                })
            } catch (e) {
                console.error(e)
            }
        }
    }

    async findReceivers(employeeIds: string[]): Promise<string[]> {
        const emils = await this.commonRepo.findEmailByEmployeeIds(employeeIds)
        return emils
    }

    async findDoc(docId: string): Promise<Doc> {
        const doc = await this.commonRepo.findDocById(docId)
        return doc
    }
}
