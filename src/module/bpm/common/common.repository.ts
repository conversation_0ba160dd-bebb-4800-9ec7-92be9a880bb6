
import { Prisma, PrismaClient } from "@prisma/client";
import { CatchRepositoryError } from "@/utils/repoDecorator";
import { Doc } from "../domain/Doc";
import { DocMapper } from "../domain/mapper/doc.mapper";

export class CommonRepository {
    constructor(private readonly prisma: PrismaClient) { }
    @CatchRepositoryError()
    async userIdToTitle(userId: string, tx?: Prisma.TransactionClient): Promise<{ id: string, title: string }> {
        const db = tx ?? this.prisma
        const result = await db.employee.findUniqueOrThrow({
            where: { id: userId },
        })
        return { id: result.id, title: `${result.nick_name}${result.title===null?'':` ${result.title}`}` }
    }
    @CatchRepositoryError()
    async findDocById(docId: string, tx?: Prisma.TransactionClient): Promise<Doc> {
        const db = tx?? this.prisma
        const result = await db.doc.findUniqueOrThrow({
            where: { id: docId },
        })
        return DocMapper.toDomain(result)
    }
    public async findEmailByEmployeeIds(employeeIds: string[],tx?: Prisma.TransactionClient): Promise<string[]> {//email address
        const db = tx ?? this.prisma

        const employees = await db.employee.findMany({
            where: {
                id: { in: employeeIds },
            },
        });
        return employees.map((e) => e.email);
    }

}