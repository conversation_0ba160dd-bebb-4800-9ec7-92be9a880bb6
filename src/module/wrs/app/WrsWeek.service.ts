import { WrsWeekRepository } from '../infra/WrsWeek.repository'
import { WrsWeek } from '../domain/WrsWeek'
import { WrsWeekProps } from '../domain/domain.schema'
import { WeekPaginationQuery, WeekPaginationQuerySchema } from '../domain/query.schema'



export class WrsWeekService {

    constructor(
        private readonly repo: WrsWeekRepository
    ) { }

    /**
     * 查詢所有週期列表，支援分頁與條件
     */
    async paging(params?: WeekPaginationQuery): Promise<{ data: WrsWeek[], total: number }> {
        const safeParams: WeekPaginationQuery = WeekPaginationQuerySchema.parse(params || {});
        return this.repo.pagingList(safeParams)
    }

    /**
     * 建立新週期
     */
    async create(weekProps: WrsWeekProps): Promise<WrsWeek> {
        return this.repo.create(weekProps)
    }

    /**
     * 查詢單一週期
     */
    async getById(id: string): Promise<WrsWeek> {
        return this.repo.findById(id)
    }
}
