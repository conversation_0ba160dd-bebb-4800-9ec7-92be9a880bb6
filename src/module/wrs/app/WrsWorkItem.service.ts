import { WrsWorkItemRepository } from '../infra/WrsWorkItem.repository'
import { WrsWorkItem } from '../domain/WrsWorkItem'
import { WrsWorkItemProps, CreateWrsWorkItemProps, WrsEmployeeReportProps } from '../domain/domain.schema'
import { EmployeeAggregation } from '../domain/response.schema';
import { WorkItemListQuery, WorkItemListQuerySchema, MyWorkItemListQuery, MyWorkItemListQuerySchema, MyOrgWorkItemListQuerySchema, MyOrgWorkItemListQuery } from '../domain/query.schema'
import { nanoid } from 'nanoid';
import { WrsWeekRepository } from '../infra/WrsWeek.repository';
import { WrsEmployeeReportRepository } from '../infra/WrsEmployeeReport.repository';
import { JwtPayload } from '@/module/auth/auth.schema';
import { getPrisma } from "@/infra/db"
import { WrsDepartmentReportRepository } from '../infra/WrsDepartmentReport.repository';

// WrsWorkItemService: Service for WrsWorkItem domain logic
// TODO: Implement all methods per API spec and domain model
export class WrsWorkItemService {

    constructor(
        private readonly repo: WrsWorkItemRepository,
        private readonly wrsEmployeeReportRepo: WrsEmployeeReportRepository,
        private readonly wrsWeekRepo: WrsWeekRepository,
        private readonly wrsDepartmentReportRepo: WrsDepartmentReportRepository
    ) { }

    // TODO(Mike): magicfunction功能為輸入userId與查詢摘要的employeeId比對是否有權限閱讀
    magicfunction(userId: string, itemUserId: string): boolean {
        return true;
    }

    async listMyReport(user: JwtPayload, params?: MyWorkItemListQuery): Promise<{ data: WrsWorkItem[], total: number }> {
        const safeParams: MyWorkItemListQuery = MyWorkItemListQuerySchema.parse(params || {});
        const mergeParams = {
            ...safeParams,
            employeeId: user.id
        };
        return this.repo.list(mergeParams, ['draft', 'submitted', 'done']);
    }

    /**
     * 新增工作項目
     */
    async create(user: JwtPayload, workItemProps: CreateWrsWorkItemProps): Promise<WrsWorkItem> {
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        const userId = user.id;
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const userName = user.name;
        if (!userId || !orgId || !userName) {
            throw new Error('User information incomplete');
        }
        const employeeReport = await this.getOrCreateEmployeeReport(userId, orgId, userName, currentWeek.getProps().id);
        return this.repo.create({
            ...workItemProps,
            id: `wi_${nanoid(10)}`,
            employeeReportId: employeeReport.id,
            employeeId: userId,
            employeeTitle: userName,
            orgId: orgId,
            weekId: currentWeek.getProps().id,
            historyRefId: null,
            status: 'draft'
        })
    }

    /**
     * 查詢單一工作項目
     */
    async getById(id: string, userId: string): Promise<WrsWorkItem> {
        const result = await this.repo.findById(id);
        if (!this.magicfunction(userId, result.getProps().employeeId)) { // TODO(Mike): magicfunction功能為輸入userId與查詢摘要的employeeId比對是否有權限閱讀
            throw new Error(`No permission`);
        }
        return result;
    }

    /**
     * 更新工作項目草稿
     */
    async updateDraft(id: string, userId: string, updateProps: Partial<CreateWrsWorkItemProps>): Promise<WrsWorkItem> {
        const workItem = await this.repo.findById(id);
        if (!workItem) {
            throw new Error(`Work item not found with id: ${id}`);
        }
        if (workItem.getProps().status !== 'draft') {
            throw new Error(`Only draft can be updated`);
        }
        if (userId != workItem.getProps().employeeId) {
            throw new Error(`No permission`);
        }
        workItem.newUpdate(updateProps);
        return this.repo.save(workItem);
    }

    /**
     * 提交後異動產生新版本
     */
    async updateVersion(id: string, userId: string, updateProps: Partial<WrsWorkItemProps>): Promise<WrsWorkItem> {
        const oldWorkItem = await this.repo.findById(id);
        if (oldWorkItem.getProps().status !== 'submitted') {
            throw new Error(`Only submitted can be updated`);
        }
        if (!this.magicfunction(userId, oldWorkItem.getProps().employeeId)) { // TODO(Mike): magicfunction功能為輸入userId與查詢摘要的employeeId比對是否有權限閱讀
            throw new Error(`No permission`);
        }
        oldWorkItem.setAsDeleted();
        const prisma = getPrisma();
        return await prisma.$transaction(async (tx) => {
            await this.repo.save(oldWorkItem, tx);
            const newItem = await this.repo.create({
                ...oldWorkItem.getProps(),
                ...updateProps,
                id: `wi_${nanoid(10)}`,
                status: 'submitted',
                historyRefId: oldWorkItem.getProps().id,
                editorEmployeeId: userId,
            }, tx);
            return newItem;
        })
    }

    /**
     * 刪除工作項目
     * @param id 工作項目ID
     */
    async delete(id: string, userId: string): Promise<WrsWorkItem> {
        const workItem = await this.repo.findById(id);
        if (!workItem) {
            throw new Error(`Work item not found with id: ${id}`);
        }
        if (workItem.getProps().status !== 'draft') {
            throw new Error(`Only draft can be deleted`);
        }
        if (userId != workItem.getProps().employeeId) {
            throw new Error(`No permission`);
        }
        return this.repo.delete(id, userId);
    }

    async submit(user: JwtPayload): Promise<{ total: number }> {
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const userId = user.id;
        const userName = user.name;
        if (!userId || !orgId || !userName) {
            throw new Error('User information incomplete');
        }
        const departmentReport = await this.wrsDepartmentReportRepo.find({ weekId: currentWeek.getProps().id, orgId: orgId });
        
        // 如果部門報告存在且狀態為已提交，則不允許提交個人摘要
        if (departmentReport && departmentReport.getProps().status === 'submitted') {
            throw new Error(`Department report submitted`);
        }

        const employeeReport = await this.getExistingEmployeeReport(userId, currentWeek.getProps().id);
        if (!employeeReport) {
            throw new Error(`Employee report not found`);
        }
        if (employeeReport.status === 'submitted') {
            throw new Error(`Employee report already submitted`);
        }

        const prisma = getPrisma();
        return await prisma.$transaction(async (tx) => {
            await this.wrsEmployeeReportRepo.submitByUserWeekId(userId, currentWeek.getProps().id, tx);
            const result = await this.repo.updateManyStatus({ weekId: currentWeek.getProps().id, employeeId: userId, status: 'draft' }, 'submitted', tx);
            return result
        })
    }

    async pagingMyOrgReport(user: JwtPayload, params?: MyOrgWorkItemListQuery): Promise<{ data: WrsWorkItem[], total: number }> {
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const safeParams: MyOrgWorkItemListQuery = MyOrgWorkItemListQuerySchema.parse(params || {});
        const mergeParams = {
            ...safeParams,
            orgId: orgId
        };
        return this.repo.list(mergeParams, ['submitted', 'done']);
    }

    /**
     * 複製上週工作項目
     * 1. 檢查當週是否已有工作項目
     * 2. 尋找最後一次提交的工作項目
     * 3. 取得該週期的所有工作項目
     * 4. 複製這些工作項目並設定為草稿狀態
     */
    async copyLastWeekWorkItems(user: JwtPayload): Promise<WrsWorkItem[]> {
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        const userId = user.id;
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const userName = user.name;

        if (!userId || !orgId || !userName) {
            throw new Error('User information incomplete');
        }

        // 1. 檢查當週是否已有工作項目
        const currentWeekItems = await this.repo.list({
            weekId: currentWeek.getProps().id,
            employeeId: userId
        }, ['draft', 'submitted', 'done']);

        if (currentWeekItems.data.length > 0) {
            throw new Error('Current week already has work items');
        }

        // 2. 尋找最後一次提交的工作項目
        const lastSubmittedWorkItem = await this.repo.findLastSubmittedWorkItem(userId);

        if (!lastSubmittedWorkItem) {
            return [];
        }

        // 3. 取得該週期的所有工作項目
        const lastWeekItems = await this.repo.list({
            weekId: lastSubmittedWorkItem.getProps().weekId,
            employeeId: userId
        }, ['submitted', 'done']);

        if (lastWeekItems.data.length === 0) {
            return [];
        }

        // 4. 複製這些工作項目並設定為草稿狀態
        const prisma = getPrisma();
        return await prisma.$transaction(async (tx) => {
            const employeeReport = await this.getOrCreateEmployeeReport(userId, orgId, userName, currentWeek.getProps().id);
            const newItems = await Promise.all(
                lastWeekItems.data.map(async (item) => {
                    const props = item.getProps();
                    return this.repo.create({
                        ...props,
                        id: `wi_${nanoid(10)}`,
                        employeeReportId: employeeReport.id,
                        weekId: currentWeek.getProps().id,
                        status: 'draft',
                        historyRefId: null,
                        submittedAt: null,
                        doneAt: null
                    }, tx);
                })
            );
            return newItems;
        });
    }

    /**
     * 查詢所有工作項目列表，支援分頁與條件
     */
    async list(params?: MyWorkItemListQuery): Promise<{ data: WrsWorkItem[], total: number }> {
        const safeParams: MyWorkItemListQuery = MyWorkItemListQuerySchema.parse(params || {});
        return this.repo.list(safeParams, ['done']);
    }

    async getExistingEmployeeReport(userId: string, weekId: string): Promise<EmployeeAggregation | null> {
        return await this.wrsEmployeeReportRepo.findOne({
            employeeId: userId,
            weekId: weekId
        });
    }

    /**
     * 取得或建立員工週報
     * 如果指定 employeeId 和 weekId 的週報已存在，則返回該週報
     * 如果不存在，則建立新的週報
     */
    async getOrCreateEmployeeReport(userId: string, orgId: string, userName: string, weekId: string): Promise<EmployeeAggregation> {
        // 檢查是否已存在相同 employeeId 和 weekId 的報告
        const existingReport = await this.getExistingEmployeeReport(userId, weekId);

        if (existingReport) {
            return existingReport;
        }

        const reportData: WrsEmployeeReportProps = {
            id: `emp${weekId}_${userId}`,
            employeeId: userId,
            employeeTitle: userName,
            orgId: orgId,
            weekId: weekId,
            status: 'draft',
            submittedAt: null,
        };

        const createdReport = await this.wrsEmployeeReportRepo.create(reportData);
        return this.wrsEmployeeReportRepo.findById(createdReport.getProps().id);
    }

}
