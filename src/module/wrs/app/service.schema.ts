import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi';
import { WorkItemTypeEnum } from '../domain/domain.schema';
extendZodWithOpenApi(z);

export const LlmSummaryReportInputSchema = z.object({
    name: z.string()
        .describe('任務的標題。')
        .openapi({ example: 'BPM系統開發V1版完成UAT' }),
    type: WorkItemTypeEnum,
    progress_percent: z.string()
        .describe('任務的完成百分比。')
        .openapi({ example: '85' }),
    result: z.string()
        .describe('本週完成工作的具體描述。')
        .openapi({ example: '完成UAT測試，修正所有回報的問題，準備下週上線。' }),
    suggestion: z.string()
        .describe('遇到的困難或提出的建議。')
        .openapi({ example: '測試環境與正式環境設定不同，需要調整部署流程。' }),
    next_week_goal: z.string()
        .describe('下一週的計畫。')
        .openapi({ example: '完成正式環境部署，進行上線前最後確認。' }),
    employee_title: z.string()
        .describe('任務的實施者。')
        .openapi({ example: '陳小明' }),
})
export type LlmSummaryReportInput = z.infer<typeof LlmSummaryReportInputSchema>;

export const LlmSummaryReportResponseSchema  = z.object({
    summary: z.string()
        .describe('部門週報總結。')
        .openapi({ example: '本週研發部在BPM系統開發上取得顯著進展，已完成UAT測試並準備上線。API整合開發也持續推進，完成主要API端點開發。同時，團隊成員王小傑成功取得AWS架構師認證，提升了團隊的雲端技術能力。此外，團隊也支援業務部進行資料整理，完成一半的資料清洗工作，並持續進行每日系統備份檢查。' }),
    highlight: z.string()
        .describe('本週重點')
        .openapi({
            example: '1. BPM系統開發V1版完成UAT測試，即將進入上線階段。\n' +
                '2. 每日系統備份檢查完成，確保資料安全。\n' +
                '3. 團隊成員完成AWS架構師進階訓練並取得認證。'
        }),
    plan: z.string()
        .describe('下週計劃')
        .openapi({ example: '1. 完成BPM系統的正式環境部署與上線。\n2. 完成剩餘API開發，進行全面整合測試。\n3. 完成剩餘的業務部資料清洗工作。' }),
})
export type LlmSummaryReportResponse= z.infer<typeof LlmSummaryReportResponseSchema>;