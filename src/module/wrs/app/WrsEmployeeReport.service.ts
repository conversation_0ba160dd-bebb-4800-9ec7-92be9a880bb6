import { WrsEmployeeReportRepository } from '../infra/WrsEmployeeReport.repository'
import { WrsEmployeeReport } from '../domain/WrsEmployeeReport'
import { CreateWrsWorkItemProps, WrsEmployeeReportProps, WrsWorkItemProps } from '../domain/domain.schema'
import { EmployeeReportPaginationQuery, EmployeeReportPaginationQuerySchema, ListEmpReportQuery, ListEmpReportQuerySchema } from '../domain/query.schema'
import { EmployeeAggregation, StatisticsEmployeeReportResponse } from '../domain/response.schema';
import { nanoid } from 'nanoid';
import { WrsWeekRepository } from '../infra/WrsWeek.repository';
import { WrsWorkItemRepository } from '../infra/WrsWorkItem.repository';
import { JwtPayload } from '@/module/auth/auth.schema';

export class WrsEmployeeReportService {

    constructor(
        private readonly repo: WrsEmployeeReportRepository,
        private readonly wrsWeekRepo: WrsWeekRepository,
        private readonly wrsWorkItemRepo: WrsWorkItemRepository
    ) { }
    /**
     * 列出「所有人」週報統計(可透過query指定部門或員工)
     */
    async statistics(user: JwtPayload, params?: ListEmpReportQuery): Promise<StatisticsEmployeeReportResponse[]> {
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        const safeParams: ListEmpReportQuery = ListEmpReportQuerySchema.partial().parse(params);
        safeParams.orgId = orgId;
        const result = await this.repo.statistics(safeParams);
        return result
    }
    /**
     * 查詢所有個人週報列表，支援分頁與條件
     */
    async paging(params?: EmployeeReportPaginationQuery): Promise<{ data: EmployeeAggregation[], total: number }> {
        const safeParams: EmployeeReportPaginationQuery = EmployeeReportPaginationQuerySchema.parse(params || {});
        return this.repo.pagingList(safeParams)
    }

    /**
     * 建立個人週報草稿
     */
    async create(reportProps: WrsEmployeeReportProps): Promise<WrsEmployeeReport> {
        return this.repo.create(reportProps)
    }

    /**
     * 建立個人週報草稿，包含工作項目
     */
    async createDraftWithWorkItems(user: JwtPayload, workItems: CreateWrsWorkItemProps[]): Promise<WrsEmployeeReport> {
        // 使用目前日期建立一個wrs_week, 如果不存在則建立
        const currentWeek = await this.wrsWeekRepo.getOrCreateCurrentWeek(new Date());
        // find org by userId
        const userId = user.id;
        const orgId = user.assignments.find(assignment => assignment.assignmentType === 'primary')?.orgId;
        if (!userId || !orgId) {
            throw new Error('User information incomplete');
        }

        const reportData: WrsEmployeeReportProps = {
            id: `emp${currentWeek.getProps().id}_${userId}`,
            employeeId: userId,
            employeeTitle: '',// TODO(Mike): get title from token
            orgId: orgId,
            weekId: currentWeek.getProps().id,
            status: 'draft',
            submittedAt: null,
        };
        const report = await this.repo.create(reportData);
        const items: WrsWorkItemProps[] = workItems.map(item => ({
            id: `wi_${nanoid(10)}`, // 使用nanoid生成唯一ID
            employeeReportId: report.getProps().id,
            employeeId: userId,
            employeeTitle: '',// TODO(Mike): get title from token
            orgId: orgId,
            weekId: currentWeek.getProps().id,
            type: item.type,
            name: item.name,
            progressPercent: item.progressPercent,
            result: item.result,
            suggestion: item.suggestion,
            nextWeekGoal: item.nextWeekGoal,
            quantitativeMetric: item.quantitativeMetric,
            historyRefId: null,
            submittedAt: null,
            doneAt: null,
            status: 'draft',
            editorEmployeeId: null,
        }));
        // 新增工作項目到週報
        if (items && items.length > 0) {
            await Promise.all(items.map(item => this.wrsWorkItemRepo.addWorkItem(report.getProps().id, item)));
        }

        return report;
    }

    async getLast(userId: string): Promise<EmployeeAggregation> {
        const draft = await this.repo.findOne({ employeeId: userId, status: 'draft' })
        if (draft) {
            return draft
        }
        const lastReport = await this.repo.findOne({ employeeId: userId, status: 'submitted' })

        if (lastReport) {
            return lastReport
        }
        throw new Error('No last report found for user')
    }
    /**
     * 查詢單一個人週報
     */
    async getById(id: string): Promise<EmployeeAggregation> {
        return this.repo.findById(id)
    }

    /**
     * 更新個人週報草稿
     */
    async updateDraft(id: string, updateProps: Partial<WrsEmployeeReportProps>): Promise<WrsEmployeeReport> {
        return this.repo.updateDraft(id, updateProps)
    }

    /**
     * 提交個人週報
     */
    async submit(id: string): Promise<WrsEmployeeReport> {
        return this.repo.submit(id)
    }
}
