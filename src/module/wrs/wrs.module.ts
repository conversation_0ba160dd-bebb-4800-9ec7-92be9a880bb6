// DI Container for WRS module

import { getPrisma } from "@/infra/db";
import { CommonModule, createModuleSwagger } from "@/infra/openapiSpec";
import { swaggerUI } from "@hono/swagger-ui";
import { Hono } from "hono";
import { WrsDepartmentReportRepository } from "./infra/WrsDepartmentReport.repository";
import { WrsEmployeeReportRepository } from "./infra/WrsEmployeeReport.repository";
import { WrsWeekRepository } from "./infra/WrsWeek.repository";
import { WrsWorkItemRepository } from "./infra/WrsWorkItem.repository";
import { WrsDepartmentReportService } from "./app/WrsDepartmentReport.service";
import { WrsEmployeeReportService } from "./app/WrsEmployeeReport.service";
import { WrsWeekService } from "./app/WrsWeek.service";
import { WrsWorkItemService } from "./app/WrsWorkItem.service";
import { createWrsDepartmentReportRouter } from "./router/departmentReport.router";
import { createWrsEmployeeReportRouter } from "./router/employeeReport.router";
import { createWrsWeekRouter } from "./router/week.router";
import { createWrsWorkItemRouter } from "./router/workItem.router";
import { env } from "@/env";

export function wrsModule(commonModules: CommonModule[]) {
    // repo
    const prisma = getPrisma()
    const wrsDepartmentReportRepository = new WrsDepartmentReportRepository(prisma)
    const wrsEmployeeReportRepository = new WrsEmployeeReportRepository(prisma)
    const wrsWeekRepository = new WrsWeekRepository(prisma)
    const wrsWorkItemRepository = new WrsWorkItemRepository(prisma)

    // service
    const wrsDepartmentReportService = new WrsDepartmentReportService(wrsDepartmentReportRepository,wrsWeekRepository, wrsWorkItemRepository)
    const wrsEmployeeReportService = new WrsEmployeeReportService(wrsEmployeeReportRepository, wrsWeekRepository, wrsWorkItemRepository)
    const wrsWeekService = new WrsWeekService(wrsWeekRepository)
    const wrsWorkItemService = new WrsWorkItemService(wrsWorkItemRepository,wrsEmployeeReportRepository,wrsWeekRepository, wrsDepartmentReportRepository)
    // router
    const wrsDepartmentReportRouter = createWrsDepartmentReportRouter(wrsDepartmentReportService)
    const wrsEmployeeReportRouter = createWrsEmployeeReportRouter(wrsEmployeeReportService)
    const wrsWeekRouter = createWrsWeekRouter(wrsWeekService)
    const wrsWorkItemRouter = createWrsWorkItemRouter(wrsWorkItemService)

    // Import the health router from the health module
    const router = new Hono()
    const moduleName = 'wrs';
 

    router.get('/', async (c) => c.json({ success: true }));
    router.route(`/employee-report`, wrsEmployeeReportRouter)
    router.route(`/work-item`, wrsWorkItemRouter)
    router.route(`/department-report`, wrsDepartmentReportRouter)
    router.route(`/week`, wrsWeekRouter)

    // Swagger
    const wrsSpec = createModuleSwagger(
        router,
        {
            title: 'WRS API',
            description: '週報系統API (包含通用認證 API)',
            modulePath: '/v0/wrs',
        },
        commonModules
    );
    router.get(`/openapi-doc`, wrsSpec)
    router.get(`/swagger`, swaggerUI({ url: '/v0/wrs/openapi-doc', }))
    console.log(`* [Module] WRS module initialized 
    - ${env.HOST_URL}/v0/${moduleName}
    - ${env.HOST_URL}/v0/${moduleName}/swagger`);
    return router
}