# wrs module - Task Manager

## 1. Domain Props Agent: 撰寫domain props, 建立type
- [x] 撰寫zod domain props 與 type
    - [x] WrsWeek domain entity
    - [x] WrsEmployeeReport domain entity
    - [x] WrsWorkItem domain entity
    - [x] WrsDepartmentReport domain entity

## 2. Domain Model Agent: 撰寫domain function
- [x] 撰寫ts docs
    - [x] WrsWeek domain entity
    - [x] WrsEmployeeReport domain entity
    - [x] WrsWorkItem domain entity
    - [x] WrsDepartmentReport domain entity
- [x] 撰寫/通過單元測試
    - [x] WrsWeek domain entity
    - [x] WrsEmployeeReport domain entity
    - [x] WrsWorkItem domain entity
    - [x] WrsDepartmentReport domain entity

## 3. Domain Mapper Agent: 撰寫domain mapper, 使domain 對應repository之CRUD操作
- [x] 撰寫/通過單元測試
    - [x] WrsWeek domain mapper
    - [x] WrsEmployeeReport domain mapper
    - [x] WrsWorkItem domain mapper
    - [x] WrsDepartmentReport domain mapper

## 4. Function Agent: 撰寫service/ repository
- [x] 列出需要撰寫的function與功能說明
    - [x] WrsWeek service
    - [x] WrsWeek repository
    - [x] WrsEmployeeReport service
    - [x] WrsEmployeeReport repository
    - [x] WrsWorkItem service
    - [x] WrsWorkItem repository
    - [x] WrsDepartmentReport service
    - [x] WrsDepartmentReport repository
- [x] 撰寫service和repository功能
    - [x] WrsWeek service
    - [x] WrsWeek repository
    - [x] WrsEmployeeReport service
    - [x] WrsEmployeeReport repository
    - [x] WrsWorkItem service
    - [x] WrsWorkItem repository
    - [x] WrsDepartmentReport service
    - [x] WrsDepartmentReport repository
- [x] 撰寫/通過單元測試
    - [x] WrsWeek service
    - [x] WrsWeek repository
    - [x] WrsEmployeeReport service
    - [x] WrsEmployeeReport repository
    - [x] WrsWorkItem service
    - [x] WrsWorkItem repository
    - [x] WrsDepartmentReport service
    - [x] WrsDepartmentReport repository

## 5. RouterAgent: 撰寫router,  在router上加上zod schema, 做出基本swagger文件
- [ ] 撰寫router
    - [ ] WrsWeek router
    - [ ] WrsEmployeeReport router
    - [ ] WrsWorkItem router
    - [ ] WrsDepartmentReport router
- [ ] 串接service
    - [ ] WrsWeek router
    - [ ] WrsEmployeeReport router
    - [ ] WrsWorkItem router
    - [ ] WrsDepartmentReport router
- [ ] 加上Request Validation與Response Schema
    - [ ] WrsWeek router
    - [ ] WrsEmployeeReport router
    - [ ] WrsWorkItem router
    - [ ] WrsDepartmentReport router
- [ ] 撰寫/通過單元測試
    - [ ] WrsWeek router
    - [ ] WrsEmployeeReport router
    - [ ] WrsWorkItem router
    - [ ] WrsDepartmentReport router

// 其餘如統計、歷史、ACL等尚未明確需求，暫不實作
// 已完成 domain entity 建立，待補充 ts docs 與單元測試
// 2025-06-05 更新
