# wrs ERD

## 一、ACL
```mermaid
erDiagram
  employee ||--o{ employee_assignment : ""
  organization ||--o{ employee_assignment : ""
```

## 二、ERD
```mermaid
erDiagram
  wrs_week ||--o{ wrs_employee_report : ""
  wrs_week ||--o{ wrs_department_report : ""
  wrs_week ||--o{ wrs_work_item : ""
  wrs_department_report ||--o{ wrs_employee_report : ""
  wrs_employee_report ||--o{ wrs_work_item : ""
  organization ||..o{ wrs_department_report : ""
  employee ||..o{ wrs_employee_report : ""
  employee ||..o{ wrs_department_report : ""
  organization ||..o{ wrs_work_item : ""
  employee ||..o{ wrs_work_item : ""
```

## 三、ERD資料表與欄位設計

#### wrs_week
| 欄位         | 型別      | 屬性   |說明| 
|--------------|-----------|--------|--------|
| id           | text      | PK     | 週期ID，格式為p_yyyymmdd_yyyymmdd，代表該週起訖 |
| start_date   | date      | not null | 週開始日期 |
| end_date     | date      | not null | 週結束日期 |

#### wrs_work_item
| 欄位                 | 型別   | 屬性   |說明| 
|----------------------|--------|--------|--------|
| id                   | text   | PK     | 工作項目ID |
| employee_report_id   | text   | not null, FK | 關聯wrs_employee_report |
| employee_id          | text   | not null, FK | 填寫人ID（*註2） |
| org_id               | text   | not null, FK | 填寫人ID（*註3） |
| week_id              | text   | not null, FK | 關聯wrs_week.id |
| type                 | text   | not null, enum | 類型(* 註1) |
| name                 | text   | not null | 名稱 |
| progress_percent     | int    | not null | 進度% |
| result               | text   | not null | 本週成果 |
| suggestion           | text   |          | 問題建議(選填) |
| next_week_goal       | text   | not null | 下週目標 |
| progress_value       | int    | not null | 進度值(0~100) |
| history_ref_id       | text   | FK, nullable | 追蹤來源wrs_work_item.id(可選, 若為修改則指向原work_item) |
| is_active            | boolean| not null, default true | 是否為目前作用中版本 |
* 註1: wrs_work_item.type enum
  ```typescript
  {
    'project',//專案
    'routine', //例行
    'support', //支援
    'admin',//行政
    'training',//培訓
    'other'//其他
  }   
  ```
- *註2: ACL employee 弱關聯
- *註3: ACL organization 弱關聯

#### wrs_employee_report
| 欄位         | 型別      | 屬性   |說明| 
|--------------|-----------|--------|--------|
| id           | text      | PK     | 員工週報ID |
| employee_id  | text      | not null | 填寫人ID（*註1） |
| org_id               | text   | not null | 所屬部門ID（*註2） |
| week_id      | text      | not null, FK | 關聯wrs_week.id |
| submitted_at | timestamp |         | 提交時間 |
| status       | text      | not null, enum | 狀態(draft/submitted) |

- *註1: ACL employee 弱關聯
- *註2: ACL organization 弱關聯

#### wrs_department_report
| 欄位                 | 型別   | 屬性   |說明| 
|----------------------|--------|--------|--------|
| id                   | text   | PK     | 部門週報ID  |
| org_id               | text   | not null | 所屬部門ID（*註2） |
| week_id              | text   | not null, FK | 關聯wrs_week.id |
| summary_employee_id  | text   | nullable | 部門摘要填寫人ID（*註1） |
| summary              | text   |         | 部門總結 |
| highlight            | text   |         | 本週重點 |
| plan                 | text   |         | 下週計劃 |
| submitted_at         | timestamp |      | 提交時間 |
| status               | text   | not null, enum | 狀態(draft/submitted) |
| reviewed_by_supervisor | boolean |         | 處主管審閱標記 |

- *註1: ACL employee 弱關聯，僅submitted時有值
- *註2: ACL organization 弱關聯

## 四、設計重點說明
- ACL: employee/organization/employee_assignment。
- employee_report、department_report、work_item皆採用snake_case命名，並加上wrs_前綴。
- 所有提交與修改皆需永久保存，work_item可追蹤修改記錄。
- 部門主管可修改組員work_item，需記錄history_ref_id。
- 週期(period)以台灣時區週一~週日為單位。
- 欄位型別與enum依據PRD設計。

## TODO
- 量化指標與進度%型別可依實際需求調整為decimal。
- 若有進階審核/簽核流程，需補充對應表設計。
- 需與ACL現有表結構進行mapping確認。
- 將原kpi_percent欄位更名為progress_value，明確標註為進度值(0~100, 整數)。
- progress_percent與progress_value用途可依實際需求調整。
- history_ref_id 欄位設為 nullable，並加註為 FK wrs_work_item.id。
- 新增 wrs_week 表，id 為 p_yyyymmdd_yyyymmdd，記錄週起訖。
- wrs_employee_report、wrs_department_report period 改為 week_id，FK 關聯 wrs_week。
- 新增 is_active 欄位於 wrs_work_item，標示是否為目前作用中版本。
- wrs_department_report 移除 manager_id 欄位。
- wrs_work_item 增加 week_id 欄位，not null, FK，關聯 wrs_week.id。
