# 類別圖
```mermaid
classDiagram
    class WrsWeek {
      + id: string
      + startDate: Date
      + endDate: Date
      + getProps()
    }
    class WrsEmployeeReport {
      + id: string
      + employeeId: string
      + weekId: string
      + submittedAt: DateTime
      + status: EmployeeReportStatus
      + getProps()
      + submit()
      + saveDraft()
    }
    class WrsWorkItem {
      + id: string
      + employeeReportId: string
      + employeeId: string
      + weekId: string
      + type: WorkItemType
      + name: string
      + progressPercent: number
      + result: string
      + suggestion: string
      + nextWeekGoal: string
      + progressValue: number
      + historyRefId: string
      + isActive: boolean
      + getProps()
      + update()
    }
    class WrsDepartmentReport {
      + id: string
      + orgId: string
      + weekId: string
      + summaryEmployeeId: string
      + summary: string
      + highlight: string
      + plan: string
      + submittedAt: DateTime
      + status: DepartmentReportStatus
      + reviewedBySupervisor: boolean
      + getProps()
      + submit()
      + saveDraft()
      + markReviewed()
    }
    WrsWeek "1" --o "*" WrsEmployeeReport
    WrsWeek "1" --o "*" WrsDepartmentReport
    WrsWeek "1" --o "*" WrsWorkItem
    WrsEmployeeReport "1" --o "*" WrsWorkItem
    WrsDepartmentReport "1" --o "*" WrsEmployeeReport
```

# 領域核心

## WrsWeek
### 1. 領域職責
- 管理週期資訊，作為所有週報的時間基準。
### 2. 屬性
- id: string // 週期ID，格式為w_yyyymmdd_yyyymmdd
- startDate: Date // 週開始日期
- endDate: Date // 週結束日期
### 3. 方法
- getProps(): 取得所有屬性
### 4. 設計重點
- 週期唯一性，所有報告均依此週期關聯。
### 5. 狀態圖
無

## WrsWorkItem
### 1. 領域職責
- 員工週報中的具體工作項目，支援版本追蹤與修改記錄。
### 2. 屬性
- id: string
- employeeReportId: string
- employeeId: string  // 0606added
- orgId: string  // 0606added
- weekId: string
- type: WorkItemType // enum: 專案/例行/支援/行政/培訓/其他
- name: string
- progressPercent: number
- result: string
- suggestion: string
- nextWeekGoal: string
- progressValue: number // 0~100
- historyRefId: string // 若為修改則指向原work_item
- isActive: boolean
### 3. 方法
- getProps(): 取得所有屬性
- update(): 修改內容並產生新版本（保留歷史）
### 4. 設計重點
- **僅當所屬 WrsEmployeeReport.status = submitted 時，異動才需保留歷史版本。**
- 修改需保留歷史版本，isActive標示當前版本。
### 5. 狀態圖
```mermaid
stateDiagram-v2
    [*] --> Active
    Active --> Inactive: update()
    Inactive --> [*]
```

## WrsEmployeeReport
### 1. 領域職責
- 員工個人週報，記錄個人本週工作摘要與工作項目。
- 管理草稿/提交狀態。
### 2. 屬性
- id: string // 個人週報ID，格式為empw_yyyymmdd_yyyymmdd_{empId} // 0606added
- orgId: string
- employeeId: string
- weekId: string
- submittedAt: DateTime
- status: EmployeeReportStatus // enum: draft, submitted
### 3. 方法
- getProps(): 取得所有屬性
- submit(): 提交週報，狀態轉為submitted
- saveDraft(): 儲存草稿
### 4. 設計重點
- 提交後不可修改，草稿可多次儲存。
- 可複製上次週報內容作為草稿。
- **只有 status=submitted 後，WrsWorkItem 的異動才需記錄歷史版本。**
### 5. 狀態圖
```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Submitted: submit()
    Submitted --> [*]
```

## WrsDepartmentReport
### 1. 領域職責
- 部門彙整週報，包含部門摘要、重點、計劃。
- 管理提交、審閱、草稿狀態。
### 2. 屬性
- id: string //部門週報ID，格式為orgw_yyyymmdd_yyyymmdd_{orgId} // 0606added
- orgId: string
- weekId: string
- summaryEmployeeId: string
- summary: string
- highlight: string
- plan: string
- submittedAt: DateTime
- status: DepartmentReportStatus // enum: draft, submitted
- reviewedBySupervisor: boolean
### 3. 方法
- getProps(): 取得所有屬性
- submit(): 提交部門週報
- saveDraft(): 儲存草稿
- markReviewed(): 處主管標記已審閱
### 4. 設計重點
- 提交後不可修改，主管可標記審閱。
- 彙整時可修改組員work_item，需記錄history_ref_id。
### 5. 狀態圖
```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Submitted: submit()
    Submitted --> Reviewed: markReviewed()
    Submitted --> [*]
    Reviewed --> [*]
```

# TODO
- 進階審核/簽核流程：目前未設計，待未來需求明確後補充。
- ACL employee/organization/employee_assignment：僅弱關聯，未納入本次核心領域類別。
- 量化指標型別可依實際需求調整為decimal。
- 需與ACL現有表結構進行mapping確認。
