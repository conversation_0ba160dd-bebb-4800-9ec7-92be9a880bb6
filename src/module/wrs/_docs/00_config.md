# 模組說明
# Agent Env
MODULE_NAME=wrs
MODULE_TITLE=週報系統
PAHT_PRD=[](./01_prd.md)
PAHT_ERD=[](./02_erd.md)
PAHT_DOMAIN_MODEL=[](./03_domain_model.md)
PAHT_TASK_MANAGER=[](./task_manager.md)

## 資料夾結構
wrs module
    | - docs 
    | - domain
    |   | - __tests__/
    |   | - domain.schema.ts
    |   | - xxx.entity.ts
    |   | - xxx.mapper.ts
    | - infra # domain entity in db
    |   | - a repository
    |   | - b repository
    | - app # application
    |   | - doc
    |   | - __tests__/
    |   | - service/
    |   | - router/
    |   | - query.schema.ts
    | - xxx.moudle.ts