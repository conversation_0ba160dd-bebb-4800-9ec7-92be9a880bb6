# WRS API Spec 目錄
本目錄彙整 `wrs` module 之 API 規格，依據領域模型與週報流程設計排序。

## 目錄
1. [week.md](./week.md)：週期 API
2. [employee-report.md](./employee-report.md)：個人週報 API
3. [work-item.md](./work-item.md)：工作項目 API
4. [department-report.md](./department-report.md)：部門週報 API
5. [statistic.md](./statistic.md)：統計與監控 API
6. [history.md](./history.md)：歷史與追溯 API
7. [TODO.md](./TODO.md)：暫未設計/未明確需求 API

---
> 各 API 規格請詳見對應檔案。

## API 順序設計說明
本 API 目錄依據領域模型與實際週報流程設計排序：
1. 週期（week）為所有週報資料的時間基準，需先建立與查詢。
2. 個人週報（employee-report）是最基礎的填報單元，流程起點。
3. 工作項目（work-item）為個人週報的核心內容，與個人週報緊密關聯。
4. 部門週報（department-report）需彙整個人週報內容，流程上晚於個人週報。
5. 統計與監控（statistic）依賴前述資料，供管理層查詢與決策。
6. 歷史與追溯（history）為所有資料的查詢輔助，支援追蹤與稽核。
7. TODO 為需求未明確或非本次核心者，暫留空位。

## Q&A
> 為什麼不是先工作項目再做個人週報？
> 
> 因為「個人週報」是主體，決定了該週的填報人、週期與狀態（草稿/已提交），「工作項目」必須隸屬於某一份個人週報下才能成立。只有先建立個人週報，才能新增、編輯該週的多個工作項目。這樣設計可確保資料歸屬明確、流程清晰，也方便後續彙整、查詢與權限控管。
此順序有助於開發時依據資料依賴關係逐步實作，並貼合實際業務流程。
