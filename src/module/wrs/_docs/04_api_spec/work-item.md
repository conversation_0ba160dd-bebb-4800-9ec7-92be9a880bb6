# WorkItem Api Spec
## 一、API 目錄
- GET `/v0/wrs/work-items`：查詢工作項目列表
- POST `/v0/wrs/work-items`：新增工作項目
- GET `/v0/wrs/work-items/{id}`：查詢單一工作項目
- PATCH `/v0/wrs/work-items/{id}`：更新工作項目（草稿）
- POST `/v0/wrs/work-items/{id}/update-version`：提交後異動，產生新版本

## 二、工作項目 API 詳細規格
### GET `/v0/wrs/work-items`
- 功能說明：查詢所有工作項目，支援依employeeReportId、weekId等條件。
- Request Schema: 無
- Response Schema: [PagingResponse](../basic-schema.md#pagingresponse)([WrsWorkItem](#wrsworkitem))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "data": [
    { "id": "wi1", "employeeReportId": "rpt1", "weekId": "p_20240603_20240609", "type": "專案", "name": "A專案開發", "progressPercent": 80, "result": "完成主流程", "suggestion": "無", "nextWeekGoal": "收尾測試", "progressValue": 80, "historyRefId": null, "isActive": true }
  ],
  "total": 1
}
```

### POST `/v0/wrs/work-items`
- 功能說明：新增工作項目（草稿）。
- Request Schema:
```json
{
  "employeeReportId": "rpt1",
  "weekId": "p_20240603_20240609",
  "type": "專案",
  "name": "A專案開發",
  "progressPercent": 80,
  "result": "完成主流程",
  "suggestion": "無",
  "nextWeekGoal": "收尾測試",
  "progressValue": 80
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsWorkItem](#wrsworkitem))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### GET `/v0/wrs/work-items/{id}`
- 功能說明：查詢單一工作項目。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsWorkItem](#wrsworkitem))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### PATCH `/v0/wrs/work-items/{id}`
- 功能說明：更新工作項目（草稿）。
- Request Schema:
```json
{
  "type": "專案",
  "name": "B專案測試",
  "progressPercent": 90
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsWorkItem](#wrsworkitem))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### POST `/v0/wrs/work-items/{id}/update-version`
- 功能說明：個人週報已提交後異動，產生新版本，保留歷史。
- Request Schema:
```json
{
  "type": "專案",
  "name": "B專案測試",
  "progressPercent": 90,
  "result": "修正bug",
  "suggestion": "優化流程",
  "nextWeekGoal": "驗收",
  "progressValue": 90
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsWorkItem](#wrsworkitem))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

## 三、schema
### Domain Schema
#### <a name="wrsworkitem"></a>WrsWorkItem
```json
{
  "id": "string",
  "employeeReportId": "string",
  "weekId": "string",
  "type": WrsWorkItemTypeEnum,
  "name": "string",
  "progressPercent": 0,
  "result": "string",
  "suggestion": "string",
  "nextWeekGoal": "string",
  "progressValue": 0,
  "historyRefId": "string|null",
  "isActive": true
}
```
### 其他Schema
#### <a name="wrsworkitemtypeenum"></a>WrsWorkItemTypeEnum
```typescript
{
    'project',//專案
    'routine', //例行
    'support', //支援
    'admin',//行政
    'training',//培訓
    'other'//其他
}  
```