# History Api Spec
## 一、API 目錄
- GET `/v0/wrs/history/employee-report/{id}`：查詢個人週報歷史紀錄
- GET `/v0/wrs/history/work-item/{id}`：查詢工作項目歷史版本
- GET `/v0/wrs/history/department-report/{id}`：查詢部門週報歷史紀錄

## 二、歷史與追溯 API 詳細規格
### GET `/v0/wrs/history/employee-report/{id}`
- 功能說明：查詢單一個人週報的所有歷史紀錄（含草稿、已提交、異動紀錄）。
- Response Schema: [PagingResponse](../basic-schema.md#pagingresponse)([EmployeeReportHistory](#employeereporthistory))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
[
  { "id": "rpt1", "employeeId": "emp1", "weekId": "p_20240603_20240609", "summary": "本週完成A專案...", "submittedAt": "2024-06-09T23:59:00+08:00", "status": "submitted" }
]
```

### GET `/v0/wrs/history/work-item/{id}`
- 功能說明：查詢單一工作項目的所有歷史版本。
- Response Schema: [ListResponse](../basic-schema.md#listresponse)([WorkItemHistory](#workitemhistory))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
[
  { "id": "wi1", "employeeReportId": "rpt1", "weekId": "p_20240603_20240609", "type": "專案", "name": "A專案開發", "progressPercent": 80, "result": "完成主流程", "suggestion": "無", "nextWeekGoal": "收尾測試", "progressValue": 80, "historyRefId": null, "isActive": true }
]
```

### GET `/v0/wrs/history/department-report/{id}`
- 功能說明：查詢單一部門週報的所有歷史紀錄。
- Response Schema: [ListResponse](../basic-schema.md#listresponse)([DepartmentReportHistory](#departmentreporthistory))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
[
  { "id": "deptRpt1", "orgId": "org1", "weekId": "p_20240603_20240609", "summaryEmployeeId": "emp1", "summary": "部門總結...", "highlight": "本週重點...", "plan": "下週計劃...", "submittedAt": "2024-06-10T10:00:00+08:00", "status": "submitted", "reviewedBySupervisor": false }
]
```

## 三、schema
### 其他Schema
#### <a name="employeereporthistory"></a>EmployeeReportHistory
同 [WrsEmployeeReport](./employee-report.md#wrsEmployeeReport)

#### <a name="workitemhistory"></a>WorkItemHistory
同 [WrsWorkItem](./work-item.md#wrsWorkItem)

#### <a name="departmentreporthistory"></a>DepartmentReportHistory
同 [WrsDepartmentReport](./department-report.md#wrsDepartmentReport)
