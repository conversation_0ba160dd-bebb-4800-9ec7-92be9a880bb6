# Week Api Spec
## 一、API 目錄
- GET `/v0/wrs/weeks`：查詢週期列表
- POST `/v0/wrs/weeks`：建立週期
- GET `/v0/wrs/weeks/{id}`：查詢單一週期

## 二、週期 API 詳細規格
### GET `/v0/wrs/weeks`
- 功能說明：查詢所有週期，支援分頁與條件篩選（如起訖日）。
- Request Schema: 無
- Response Schema: [PagingResponse](../basic-schema.md#pagingresponse)([WrsWeek](#wrsweek))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "data": [
    { "id": "p_20240603_20240609", "startDate": "2024-06-03", "endDate": "2024-06-09" }
  ],
  "total": 1
}
```

### GET `/v0/wrs/weeks/{id}`
- 功能說明：查詢單一週期。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsWeek](#wrsweek))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "id": "p_20240603_20240609",
  "startDate": "2024-06-03",
  "endDate": "2024-06-09"
}
```

## 三、schema
### Domain Schema
#### <a name="wrsweek"></a>WrsWeek
```json
{
  "id": "string", // 週期ID，格式為p_yyyymmdd_yyyymmdd
  "startDate": "string", // yyyy-mm-dd
  "endDate": "string" // yyyy-mm-dd
}
```
### 其他Schema
無
