# Statistic Api Spec
## 一、API 目錄
- GET `/v0/wrs/statistics/overview`：查詢CEO儀表板統計
- GET `/v0/wrs/statistics/department-submission`：查詢部門週報提交狀態
- GET `/v0/wrs/statistics/employee-submission`：查詢員工週報提交狀態

## 二、統計與監控 API 詳細規格
### GET `/v0/wrs/statistics/overview`
- 功能說明：查詢CEO儀表板統計（專案進度均值、工作類型佔比、部門/員工週報提交率、work_item總數，均與上週比較）。
- Request Schema: 無
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([StatisticOverview](#statisticoverview))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "projectProgressAvg": 82,
  "workTypeRatio": { "專案": 60, "例行": 20, "支援": 10, "行政": 5, "培訓": 5 },
  "departmentSubmissionRate": 95,
  "employeeSubmissionRate": 90,
  "workItemCount": 120,
  "compareToLastWeek": { "projectProgressAvg": 2, "workTypeRatio": { "專案": 5 }, "departmentSubmissionRate": 1, "employeeSubmissionRate": -2, "workItemCount": 10 }
}
```

### GET `/v0/wrs/statistics/department-submission`
- 功能說明：查詢各部門週報提交狀態（草稿/已提交/未交）。
- Request Schema: 無
- Response Schema: [ListResponse](../basic-schema.md#listresponse)([DepartmentSubmissionStatus](#departmentsubmissionstatus))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
[
  { "orgId": "org1", "orgName": "研發部", "status": "submitted" },
  { "orgId": "org2", "orgName": "行銷部", "status": "draft" }
]
```

### GET `/v0/wrs/statistics/employee-submission`
- 功能說明：查詢各員工週報提交狀態。
- Request Schema: 無
- Response Schema: [ListResponse](../basic-schema.md#listresponse)([EmployeeSubmissionStatus](#employeesubmissionstatus))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
[
  { "employeeId": "emp1", "employeeName": "王小明", "status": "submitted" },
  { "employeeId": "emp2", "employeeName": "李小華", "status": "draft" }
]
```

## 三、schema
### 其他Schema
#### <a name="statisticoverview"></a>StatisticOverview
```json
{
  "projectProgressAvg": 82,
  "workTypeRatio": { "專案": 60, "例行": 20, "支援": 10, "行政": 5, "培訓": 5 },
  "departmentSubmissionRate": 95,
  "employeeSubmissionRate": 90,
  "workItemCount": 120,
  "compareToLastWeek": { "projectProgressAvg": 2, "workTypeRatio": { "專案": 5 }, "departmentSubmissionRate": 1, "employeeSubmissionRate": -2, "workItemCount": 10 }
}
```
#### <a name="departmentsubmissionstatus"></a>DepartmentSubmissionStatus
```json
{
  "orgId": "string",
  "orgName": "string",
  "status": "draft | submitted | not_submitted"
}
```
#### <a name="employeesubmissionstatus"></a>EmployeeSubmissionStatus
```json
{
  "employeeId": "string",
  "employeeName": "string",
  "status": "draft | submitted | not_submitted"
}
```
