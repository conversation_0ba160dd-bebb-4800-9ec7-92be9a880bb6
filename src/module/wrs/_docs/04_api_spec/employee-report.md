# EmployeeReport Api Spec
## 一、API 目錄
- GET `/v0/wrs/employee-reports`：查詢個人週報列表
- POST `/v0/wrs/employee-reports`：建立個人週報（草稿）
- GET `/v0/wrs/employee-reports/{id}`：查詢單一個人週報
- PATCH `/v0/wrs/employee-reports/{id}`：更新個人週報（草稿）
- POST `/v0/wrs/employee-reports/{id}/submit`：提交個人週報

## 二、個人週報 API 詳細規格
### GET `/v0/wrs/employee-reports`
- 功能說明：查詢所有個人週報，支援分頁、週期、填寫人等條件。
- Request Schema: 無
- Response Schema: [PagingResponse](../basic-schema.md#pagingresponse)([WrsEmployeeReport](#wrsemployeereport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "data": [
    { "id": "rpt1", "employeeId": "emp1", "weekId": "p_20240603_20240609", "summary": "本週完成A專案...", "submittedAt": "2024-06-09T23:59:00+08:00", "status": "submitted" }
  ],
  "total": 1
}
```

### POST `/v0/wrs/employee-reports`
- 功能說明：建立個人週報草稿。回應將會載入上次週報內容 TODO(Mike)
- Request Schema:
```json
{
  "employeeId": "emp1",
  "weekId": "p_20240603_20240609",
  "summary": "本週完成A專案..."
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsEmployeeReport](#wrsemployeereport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### GET `/v0/wrs/employee-reports/{id}`
- 功能說明：查詢單一個人週報。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsEmployeeReport](#wrsemployeereport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### PATCH `/v0/wrs/employee-reports/{id}`
- 功能說明：更新個人週報草稿。
- Request Schema:
```json
{
  "summary": "本週完成B專案..."
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsEmployeeReport](#wrsemployeereport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### POST `/v0/wrs/employee-reports/{id}/submit`
- 功能說明：提交個人週報，狀態轉為submitted。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsEmployeeReport](#wrsemployeereport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

## 三、schema
### Domain Schema
#### <a name="wrsemployeereport"></a>WrsEmployeeReport
```json
{
  "id": "string",
  "employeeId": "string",
  "weekId": "string",
  "summary": "string",
  "submittedAt": "string", // ISO8601
  "status": "draft | submitted"
}
```
### 其他Schema
無
