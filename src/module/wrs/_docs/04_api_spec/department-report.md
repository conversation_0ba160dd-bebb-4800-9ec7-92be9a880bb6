# DepartmentReport Api Spec
## 一、API 目錄
- GET `/v0/wrs/department-reports`：查詢部門週報列表
- POST `/v0/wrs/department-reports`：建立部門週報（草稿）
- GET `/v0/wrs/department-reports/{id}`：查詢單一部門週報
- PATCH `/v0/wrs/department-reports/{id}`：更新部門週報（草稿）
- POST `/v0/wrs/department-reports/{id}/submit`：提交部門週報
- POST `/v0/wrs/department-reports/{id}/mark-reviewed`：處主管標記已審閱

## 二、部門週報 API 詳細規格
### GET `/v0/wrs/department-reports`
- 功能說明：查詢所有部門週報，支援分頁、週期、部門等條件。
- Request Schema: 無
- Response Schema: [PagingResponse](../basic-schema.md#pagingresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)
- Response Example:
```json
{
  "data": [
    { "id": "deptRpt1", "orgId": "org1", "weekId": "p_20240603_20240609", "summaryEmployeeId": "emp1", "summary": "部門總結...", "highlight": "本週重點...", "plan": "下週計劃...", "submittedAt": "2024-06-10T10:00:00+08:00", "status": "submitted", "reviewedBySupervisor": false }
  ],
  "total": 1
}
```

### POST `/v0/wrs/department-reports`
- 功能說明：建立部門週報草稿。
- Request Schema:
```json
{
  "orgId": "org1",
  "weekId": "p_20240603_20240609",
  "summaryEmployeeId": "emp1",
  "summary": "部門總結...",
  "highlight": "本週重點...",
  "plan": "下週計劃..."
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### GET `/v0/wrs/department-reports/{id}`
- 功能說明：查詢單一部門週報。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### PATCH `/v0/wrs/department-reports/{id}`
- 功能說明：更新部門週報草稿。
- Request Schema:
```json
{
  "summary": "部門總結(修正)...",
  "highlight": "重點(修正)...",
  "plan": "計劃(修正)..."
}
```
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### POST `/v0/wrs/department-reports/{id}/submit`
- 功能說明：提交部門週報，狀態轉為submitted。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

### POST `/v0/wrs/department-reports/{id}/mark-reviewed`
- 功能說明：處主管標記部門週報已審閱。
- Response Schema: [ItemResponse](../basic-schema.md#itemresponse)([WrsDepartmentReport](#wrsdepartmentreport))
- Error Schema: [ErrorResponse](../basic-schema.md#errorresponse)

## 三、schema
### Domain Schema
#### <a name="wrsdepartmentreport"></a>WrsDepartmentReport
```json
{
  "id": "string",
  "orgId": "string",
  "weekId": "string",
  "summaryEmployeeId": "string",
  "summary": "string",
  "highlight": "string",
  "plan": "string",
  "submittedAt": "string", // ISO8601
  "status": "draft | submitted",
  "reviewedBySupervisor": true
}
```
### 其他Schema
無
