**產品需求文檔 (PRD): 週報系統**

**版本**: v0.2.0 (精簡版)
**日期**: 2025-06-10

**1. 概述 (Overview)**

*   **1.1. 產品名稱**: 週報系統
*   **1.2. 核心問題**: 現行週報流程紙本化、格式不一、彙整耗時、追蹤困難。
*   **1.3. 產品目標**: 实现週報電子化、結構化，提升資訊透明度與管理效率。
*   **1.4. 核心價值**:
    *   **易用性**: 直觀操作。
    *   **即時性**: 便捷提交，即時查看。
    *   **統計性**: 系統收集，多維分析。
    *   **追蹤性**: 進度清晰，輔助決策。
*   **1.5. 目標用戶**: 組員、部門主管、處主管、HR、CEO。

**2. 組織架構與角色職責 (Organizational Structure & Roles)**

*   **2.1. 組織層級**: Company (公司) > Division (處) > Department (部門)。
*   **2.2. 視圖**:

| 視圖 |  主要受眾 |週報職責  | 
| :--- |  :--- | :--- | 
| 個人視圖 | 部門所有人(包含主管)     | 1. 創建/編輯/提交個人週報(`employee_report`)；<br>2.查看個人歷史|
| 部門視圖 |  部門所有人(包含主管) | 1.編輯/查看同部門組員已提交週報。 (`department_report`) <br> 2.查看部門歷史週報；<br> 3. 彙整部門摘要<br>  4.交部門週報給處HR。  |
| HR視圖  | HR部門所有人 |1. 查看全公司部門週報；<br> 2. 監控提交狀態；<br> 3. 提醒未交部門；<br> 4. 查看統計。|
| 主管視圖| 處主管, CEO| 1. 查閱全公司週報；掌握運營狀況。<br> 2. 訪問儀表板; <br> 3. **不填寫個人週報。**；| 

**3. 核心功能 (Core Features)**

*   **3.1. 個人週報 (`employee_report`) 管理**
    *   **週期**: 台灣時區，週一 00:00 至 週日 23:59。預設當週，可查歷史。
    *   **摘要**: 選填個人文字摘要。
    *   **工作事項 (`work_item`)**:
        *   **類型**: 專案、例行、支援、行政、培訓、其他。
        *   **欄位**: 名稱、進度%、本週成果、問題建議(選填)、下週目標、量化指標%(選填)。
        *   **草稿帶入**: 直接複製上次的employee_report(並非上週, 有可能有人請假一週)
    *   **操作**: 新增/編輯/刪除摘要及工作事項；保存草稿；提交給部門主管 (提交後鎖定)。

*   **3.2. 部門週報 (`department_report`) 管理**
    *   **彙整 (部門主管)**: 修改組員 `work_item` (需記錄)。
    *   **摘要 (部門主管)**: 填寫部門總結、本週重點、下週計劃 (即使部門僅一人)。
    *   **提交 (部門主管)**: 提交後對處主管/HR/CEO可見，部門主管不可修改。
    *   **審閱 (處主管)**: 查看部門週報，可選標記已審閱 (非強制)。

*   **3.3. 統計與監控 (HR & CEO)**
    *   **CEO儀表板**: 專案進度均值、工作類型佔比、部門/員工週報提交率、`work_item`總數 (均與上週比較)。
    *   **提交監控 (HR)**: 各部門提交狀態 (草稿/已提交/未交)。
    *   **詳情查看**: HR/CEO可下鑽查看各級報告。

*   **3.4. 歷史與追溯**: 永久保存所有提交記錄及 `work_item` 修改記錄。

**4. 資料模型概要 (Data Model - Key Entities)**

| 實體               | 主要屬性                                                                                                |
| :----------------- | :------------------------------------------------------------------------------------------------------ |
| WorkItem           | ID, 關聯EmployeeReportID, 類型, 名稱, 進度%, 成果, 問題建議, 下週目標, 量化指標                                  |
| EmployeeReport     | ID, 填寫人ID, 週期, 個人摘要, 提交時間, 狀態                                                                |
| DepartmentReport   | ID, 彙整部門主管ID, 所屬部門ID, 週期, 部門摘要 (3項), 提交時間, 狀態, (可選)處主管審閱標記                       |

**5. 非功能性需求 (Non-Functional Requirements)**

*   **易用性**: 界面簡潔直觀，操作符合習慣。
*   **性能**: 頁面載入 < 3s，API響應 < 500ms。
*   **可靠性**: 數據準確，系統穩定，常規備份。
*   **安全性**: 角色權限控制，HTTPS，防常見Web漏洞。

**6. 擴展預留 (Scalability Provisions)**

*   預留外部專案ID關聯。
*   預留通知服務集成點。
*   預留數據匯出機制 (本版不含此功能)。

**7. 未來考慮 (Future Considerations)**

*   自動化提醒。
*   加權平均進度。
*   PM系統深度整合。
*   客製化報表與匯出。

**8. 核心流程圖 (Core Flowcharts)**

*   **8.1. 個人週報填寫與提交流程**
    ```mermaid
    graph LR
        B{是否有本週草稿}-- 是 --> E[載入草稿];
        B -- 否 --> C[載入上次個人週報];
        C & E --> D[編輯/填個認工作項目摘要];
        D --> G{提交?};
        G -- 是 --> H[完成個人週報];
        G -- 否 --> F[存草稿];
    ```
    一般職員、部門主管、辦公室 都可以填寫個人週報。
*   **8.2. 部門週報彙整與提交流程**
     ```mermaid
    graph LR
        B{是否有本週草稿}-- 是 --> E[載入草稿];
        B -- 否 --> C[載入上次部門週報];
        C & E --> D[填寫部門摘要];
        D --> G{提交?};
        G -- 是 --> H[完成部門週報];
        G -- 否 --> F[存草稿];
    ```
    一般職員、部門主管、辦公室 都可以填寫個人週報。

*   **8.3. 週報查閱與監控流程 (處主管, HR, CEO)**
    ```mermaid
    graph TD
        A[登入] --> B{進入各自視圖};
        B --> B1[處:查部門報告/標記/提醒];
        B --> B2[HR:查全公司/監控/統計/提醒];
        B --> B3[CEO:看儀表板/下鑽詳情];
    ```
    處級主管、HR、CEO 可以查閱週報。