import { CatchRepositoryError } from '@/utils/repoDecorator'
import { WrsWorkItemMapper } from '../domain/mapper/WrsWorkItemMapper'
import { Prisma, PrismaClient, WorkItemStatus } from "@prisma/client";
import { WorkItemListQuery } from '../domain/query.schema';
import { WrsWorkItemProps } from '../domain/domain.schema';
import { nanoid } from 'nanoid';
import { WrsWorkItem } from '../domain/WrsWorkItem'


export class WrsWorkItemRepository {
    constructor(private readonly prisma: PrismaClient) { }
    /**
     * 查詢所有工作項目列表
     * @param params 查詢參數
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async list(params: WorkItemListQuery, status: WorkItemStatus[], tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const { employeeId, weekId, type } = params || {};
        const where: Prisma.wrs_work_itemWhereInput = {
            status: { in: status }, // 排除已刪除的工作項目
            ...employeeId ? { employee_id: employeeId } : {},
            ...weekId ? { week_id: weekId } : {},
            ...type ? { type: type } : {}
        };
        const [data, total] = await Promise.all([
            db.wrs_work_item.findMany({
                where,
                orderBy: { id: 'desc' },
            }),
            db.wrs_work_item.count({ where })
        ]);
        return {
            data: data.map(WrsWorkItemMapper.toDomain),
            total
        };
    }

    /**
     * 新增工作項目
     * @param props WrsWorkItemProps
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async create(props: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const created = await db.wrs_work_item.create({
            data: WrsWorkItemMapper.toPersistence(props)
        });
        return WrsWorkItemMapper.toDomain(created);
    }

    /**
     * Adds a work item to an employee report
     * @param reportId The ID of the employee report
     * @param workItem The work item to add
     */
    async addWorkItem(reportId: string, workItem: WrsWorkItemProps): Promise<void> {
        await this.prisma.wrs_work_item.create({
            data: {
                id: workItem.id,
                employee_report_id: reportId,
                employee_id: workItem.employeeId,
                employee_title: workItem.employeeTitle,
                org_id: workItem.orgId,
                week_id: workItem.weekId,
                type: workItem.type,
                name: workItem.name,
                progress_percent: workItem.progressPercent,
                result: workItem.result,
                suggestion: workItem.suggestion,
                next_week_goal: workItem.nextWeekGoal,
                quantitative_metric: workItem.quantitativeMetric,
                history_ref_id: workItem.historyRefId,
                submitted_at: workItem.submittedAt,
                done_at: workItem.doneAt,
                status: workItem.status,
                editor_employee_id: workItem.editorEmployeeId,
            },
        });
    }
    /**
     * 查詢單一工作項目
     * @param id 工作項目ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async findById(id: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const found = await db.wrs_work_item.findUnique({ where: { id } });
        if (!found) throw new Error('WorkItem not found');
        return WrsWorkItemMapper.toDomain(found);
    }

    /**
    * 儲存工作項目
    * @param workItem 工作項目實體
    * @param tx Transaction client
    */
    @CatchRepositoryError()
    async save(workItem: WrsWorkItem, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const updated = await db.wrs_work_item.update({
            where: { id: workItem.getProps().id },
            data: WrsWorkItemMapper.toPersistence(workItem.getProps())
        });
        return WrsWorkItemMapper.toDomain(updated);
    }

    /**
     * 更新工作項目草稿
     * @param id 工作項目ID
     * @param updateProps 更新內容
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async updateDraft(id: string, updateProps: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const updated = await db.wrs_work_item.update({
            where: { id },
            data: WrsWorkItemMapper.toPersistence(updateProps)
        });
        return WrsWorkItemMapper.toDomain(updated);
    }

    /**
     * 提交後異動產生新版本
     * @param id 工作項目ID
     * @param updateProps 更新內容
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async updateVersion(id: string, updateProps: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        await db.wrs_work_item.update({ where: { id }, data: { status: 'deleted' } });
        const old = await db.wrs_work_item.findUnique({ where: { id } });
        console.log('old', old);
        if (!old) throw new Error('WorkItem not found');
        const { employee_report, ...newObj } = WrsWorkItemMapper.toPersistence(updateProps)
        const payload = WrsWorkItemMapper.toDomain({
            ...old,
            ...newObj,
            id: `wi_${nanoid(10)}`,
            history_ref_id: old.id,
            status: 'submitted'
        })
        const newItem = await db.wrs_work_item.create({
            data: WrsWorkItemMapper.toPersistence(payload.getProps())
        });
        return WrsWorkItemMapper.toDomain(newItem);
    }


    /**
     * 硬刪除工作項目
     * @param id 工作項目ID
     * @param userId 編輯者ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async delete(id: string, userId: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const deleted = await db.wrs_work_item.update({
            where: { id },
            data: {
                status: 'deleted',
                editor_employee_id: userId
            }
        });
        return WrsWorkItemMapper.toDomain(deleted);
    }

    /**
     * 批量更新工作項目狀態
     * @param props 查詢條件 { weekId: string, employeeId: string }
     * @param status 要更新的狀態
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async updateManyStatus(props: Partial<{ weekId: string, employeeId: string, orgId: string, status: 'draft' | 'submitted' | 'done' }>, status: 'draft' | 'submitted' | 'done', tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const now = new Date().toISOString();
        const where: Prisma.wrs_work_itemWhereInput = {
            week_id: props.weekId,
            employee_id: props.employeeId,
            org_id: props.orgId,
            status: props.status,
        };

        const result = await db.wrs_work_item.updateMany({
            where,
            data: {
                status,
                submitted_at: status === 'submitted' ? now : undefined,
                done_at: status === 'done' ? now : undefined
            }
        });

        return { total: result.count };
    }

    /**
     * 尋找最後一次提交的工作項目
     * @param userId 員工ID
     * @param tx Transaction client
     * @returns 最後提交的工作項目或 null
     */
    @CatchRepositoryError()
    async findLastSubmittedWorkItem(userId: string, tx?: Prisma.TransactionClient): Promise<WrsWorkItem | null> {
        const db = tx ?? this.prisma;
        const lastSubmittedWorkItem = await db.wrs_work_item.findFirst({
            where: {
                employee_id: userId,
                status: { in: ['submitted', 'done'] }
            },
            orderBy: { submitted_at: 'desc' }
        });

        if (!lastSubmittedWorkItem) {
            return null;
        }

        return WrsWorkItemMapper.toDomain(lastSubmittedWorkItem);
    }
}
