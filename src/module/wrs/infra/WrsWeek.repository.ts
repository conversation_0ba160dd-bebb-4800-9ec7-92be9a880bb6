// WrsWeekRepository: Repository for WrsWeek persistence
import { CatchRepositoryError } from '@/utils/repoDecorator'
import { WrsWeekMapper } from '../domain/mapper/WrsWeekMapper'
import { Prisma, PrismaClient } from "@prisma/client";
import { WeekPaginationQuery } from '../domain/query.schema';
import { WrsWeekProps } from '../domain/domain.schema';
import { WrsWeek } from '../domain/WrsWeek';


export class WrsWeekRepository {
    constructor(private readonly prisma: PrismaClient) {}

    /**
     * 查詢所有週期列表，支援分頁與條件
     * @param params 查詢參數
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async pagingList(params: WeekPaginationQuery, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const { startDate, endDate } = params || {};
        const where: Prisma.wrs_weekWhereInput = {
            ...(startDate ? { start_date: { gte: startDate } } : {}),
            ...(endDate ? { end_date: { lte: endDate } } : {})
        };

        const [data, total] = await Promise.all([
            db.wrs_week.findMany({
                where,
                skip: params?.skip ?? 0,
                take: params?.take ?? 20,
                orderBy: { start_date: 'desc' },
            }),
            db.wrs_week.count({ where })
        ]);
        return {
            data: data.map(WrsWeekMapper.toDomain),
            total
        };
    }

    /**
     * 建立新週期
     * @param props WrsWeekProps
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async create(props: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const created = await db.wrs_week.create({
            data: WrsWeekMapper.toPersistence(props)
        });
        return WrsWeekMapper.toDomain(created);
    }

    /**
     * 查詢單一週期
     * @param id 週期ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async findById(id: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma;
        const found = await db.wrs_week.findUnique({ where: { id } });
        if (!found) throw new Error('Week not found');
        return WrsWeekMapper.toDomain(found);
    }

    @CatchRepositoryError()
    async getOrCreateCurrentWeek(now:Date): Promise<WrsWeek> {  
        const db = this.prisma;
        const startOfWeek = new Date(now);
        startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1); // 设置为星期一
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(endOfWeek.getDate() + 6); // 设置为星期日

        const weekId = `w_${startOfWeek.toISOString().slice(0, 10).replace(/-/g, '')}_${endOfWeek.toISOString().slice(0, 10).replace(/-/g, '')}`;

        let week = await db.wrs_week.findUnique({ where: { id: weekId } });
        if (!week) {
            week = await db.wrs_week.create({
                data: {
                    id: weekId,
                    start_date: startOfWeek,
                    end_date: endOfWeek
                }
            });
        }
        return WrsWeekMapper.toDomain(week);
    }

    /**
     * 取得上一個週期
     * @param currentWeekId 當前週期ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async getLastWeek(currentWeekId: string, tx?: Prisma.TransactionClient): Promise<WrsWeek | null> {
        const db = tx ?? this.prisma;
        const currentWeek = await db.wrs_week.findUnique({ where: { id: currentWeekId } });
        if (!currentWeek) {
            throw new Error('Current week not found');
        }

        const lastWeek = await db.wrs_week.findFirst({
            where: {
                end_date: { lt: currentWeek.start_date }
            },
            orderBy: { end_date: 'desc' }
        });

        if (!lastWeek) {
            return null;
        }

        return WrsWeekMapper.toDomain(lastWeek);
    }
}
