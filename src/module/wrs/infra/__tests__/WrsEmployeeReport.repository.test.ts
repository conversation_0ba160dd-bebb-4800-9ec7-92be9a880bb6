import { PrismaClient } from "@prisma/client";
import { WrsEmployeeReportRepository } from '../WrsEmployeeReport.repository';

describe('WrsEmployeeReportRepository', () => {
    const prisma = new PrismaClient();
    const repo = new WrsEmployeeReportRepository(prisma);

    afterAll(async () => {
        await prisma.$disconnect();
    });

    it('should create, find, update draft, and submit an employee report', async () => {
        const props = {
            id: 'test_emp_001',
            employeeId: 'emp_001',
            weekId: 'p_20240603_20240609',
            summary: 'summary',
            status: 'draft' as const,
        };
        // 建立
        const created = await repo.create(props);
        expect(created.getProps().id).toBe(props.id);
        // 查詢
        const found = await repo.findById(props.id);
        expect(found.getProps().employeeId).toBe(props.employeeId);
        // 更新草稿
        const updated = await repo.updateDraft(props.id, { summary: 'updated summary' });
        expect(updated.getProps().summary).toBe('updated summary');
        // 提交
        const submitted = await repo.submit(props.id);
        expect(submitted.getProps().status).toBe('submitted');
        expect(submitted.getProps().submittedAt).toBeDefined();
    });

    it('should list employee reports', async () => {
        const result = await repo.pagingList({ weekId: 'p_20240603_20240609' });
        expect(Array.isArray(result.data)).toBe(true);
        expect(typeof result.total).toBe('number');
    });
});
