import { PrismaClient } from "@prisma/client";
import { WrsWorkItemRepository } from '../WrsWorkItem.repository';

describe('WrsWorkItemRepository', () => {
    const prisma = new PrismaClient();
    const repo = new WrsWorkItemRepository(prisma);

    afterAll(async () => {
        await prisma.$disconnect();
    });

    it('should create, find, update draft, and update version of a work item', async () => {
        const props = {
            id: 'test_wi_001',
            employeeReportId: 'erpt_20240603_001',
            weekId: 'p_20240603_20240609',
            type: 'project',
            name: 'test work item',
            progressPercent: 50,
            result: 'result',
            suggestion: 'suggestion',
            nextWeekGoal: 'goal',
            progressValue: 50,
            status: 'draft',
        };
        // 建立
        const created = await repo.create(props);
        expect(created.getProps().id).toBe(props.id);
        // 查詢
        const found = await repo.findById(props.id);
        expect(found.getProps().name).toBe(props.name);
        // 更新草稿
        const updated = await repo.updateDraft(props.id, { name: 'updated name' });
        expect(updated.getProps().name).toBe('updated name');
        // 產生新版本
        const versioned = await repo.updateVersion(props.id, { name: 'v2', status: 'submitted' });
        expect(versioned.getProps().name).toBe('v2');
    });

    it('should list work items', async () => {
        const result = await repo.pagingList({ weekId: 'p_20240603_20240609' });
        expect(Array.isArray(result.data)).toBe(true);
        expect(typeof result.total).toBe('number');
    });
});
