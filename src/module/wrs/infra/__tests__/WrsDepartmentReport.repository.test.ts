import { WrsDepartmentReportRepository } from '../WrsDepartmentReport.repository';
import { PrismaClient } from "@prisma/client";

describe('WrsDepartmentReportRepository', () => {
    const prisma = new PrismaClient();
    const repo = new WrsDepartmentReportRepository(prisma);

    afterAll(async () => {
        await prisma.$disconnect();
    });

    it('should create, find, update, submit, and markReviewed a department report', async () => {
        // 測試資料
        const props = {
            id: 'test_dept_001',
            orgId: 'org_001',
            weekId: 'p_20240603_20240609',
            summaryEmployeeId: 'emp_001',
            summary: 'summary',
            highlight: 'highlight',
            plan: 'plan',
            status: 'draft',
            reviewedBySupervisor: false,
        };
        // 建立
        const created = await repo.create(props);
        expect(created.getProps().id).toBe(props.id);
        // 查詢
        const found = await repo.findById(props.id);
        expect(found.getProps().orgId).toBe(props.orgId);
        // 更新
        const updated = await repo.updateDraft(props.id, { summary: 'updated summary' });
        expect(updated.getProps().summary).toBe('updated summary');
        // 提交
        const submitted = await repo.submit(props.id);
        expect(submitted.getProps().status).toBe('submitted');
        expect(submitted.getProps().submittedAt).toBeDefined();
        // 標記審閱
        const reviewed = await repo.markReviewed(props.id);
        expect(reviewed.getProps().reviewedBySupervisor).toBe(true);
    });

    it('should list department reports', async () => {
        const result = await repo.pagingList({ orgId: 'org_001' });
        expect(Array.isArray(result.data)).toBe(true);
        expect(typeof result.total).toBe('number');
    });
});
