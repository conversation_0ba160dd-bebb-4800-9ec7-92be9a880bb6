import { PrismaClient } from "@prisma/client";
import { WrsWeekRepository } from '../WrsWeek.repository';

describe('WrsWeekRepository', () => {
    const prisma = new PrismaClient();
    const repo = new WrsWeekRepository(prisma);

    afterAll(async () => {
        await prisma.$disconnect();
    });

    it('should throw not implemented for all methods (template)', async () => {
        await expect(repo.pagingList({})).rejects.toThrow('Not implemented');
        await expect(repo.create({})).rejects.toThrow('Not implemented');
        await expect(repo.findById('id')).rejects.toThrow('Not implemented');
    });
});
