import { CatchRepositoryError } from '@/utils/repoDecorator'
import { WrsDepartmentReportMapper } from '../domain/mapper/WrsDepartmentReportMapper'
import { EmployeeReportStatus, Prisma, PrismaClient, WorkItemType } from "@prisma/client";
import { DepartmentReportPaginationQuery, ListDepartmentReportQuery } from '../domain/query.schema';
import { StatisticsDepartmentReportResponse } from '../domain/response.schema';
import { LlmSummaryReportInput } from '../app/service.schema';

export class WrsDepartmentReportRepository {
    constructor(private readonly prisma: PrismaClient) { }

    // TODO(Mike): magicfunction功能為輸入userId找到該使用者底下的所有org_id
    magicfunction(_userId: string): string[] {
        return [];
    }

    /**
     * 列出「所有部門」週報統計
     * @param params 查詢參數 { weekId?: string }
     */
    @CatchRepositoryError()
    async statistics(userId: string, params?: ListDepartmentReportQuery): Promise<(StatisticsDepartmentReportResponse)[]> {
        const { id, weekId, status } = params || {}
        //Warring: ACL TODO(Mike)
        const orgIds = await this.prisma.organization.findMany({
            where: {
                type: { in: ['department', 'office'] },
            },
            select: { id: true }
        })
        const reportOrgIds = orgIds.map(r => r.id)

        const where: Prisma.wrs_department_reportWhereInput = {
            ...id && { id },
            org_id: { in: this.magicfunction(userId) }, // TODO(Mike): magicfunction功能為輸入userId找到該使用者底下的所有org_id
            ...weekId && { week_id: weekId },
            ...status && { status },
        }
        const reports = await this.prisma.wrs_department_report.findMany({
            where,
            orderBy: { submitted_at: 'desc' },
        })

        const workItemStats = await this.prisma.wrs_work_item.groupBy({
            by: ['org_id', 'type'],
            where: {
                status: 'done',
                ...weekId && { week_id: weekId },
                org_id: {
                    in: reportOrgIds,
                }
            },
            _count: {
                _all: true,
            }
        })
        const reportWithStats = orgIds.map(org => {
            const report = reports.find(r => r.org_id === org.id) || {
                id: '',
                org_id: org.id,
                week_id: weekId || '',
                status: 'draft',
                submitted_at: null,
                reviewed_by_supervisor: false,
            }

            const relatedStats = workItemStats.filter(
                w => w.org_id === report.org_id
            )
            let total = 0
            return {
                ...report,
                org_id: org.id,
                statistics: {
                    ...Object.fromEntries(
                        Object.keys(WorkItemType).map(type => {
                            const found = relatedStats.find(stat => stat.type === type);
                            const count = found?._count?._all ?? 0;
                            total += count;
                            return [type, count];
                        })
                    ),
                    total
                }
            }
        })
        return reportWithStats.map(WrsDepartmentReportMapper.toStatistics);
    }

    /**
     * 查詢所有部門週報列表，支援分頁與條件
     * @param params 查詢參數 { orgId?: string, weekId?: string, status?: string, skip?: number, take?: number }
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async pagingList(params: DepartmentReportPaginationQuery, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const where: Prisma.wrs_department_reportWhereInput = {}
        if (params?.orgId) where.org_id = params.orgId
        if (params?.weekId) where.week_id = params.weekId
        if (params?.status) where.status = params.status
        const [data, total] = await Promise.all([
            db.wrs_department_report.findMany({
                where,
                skip: params?.skip ?? 0,
                take: params?.take ?? 20,
                orderBy: { submitted_at: 'desc' },
            }),
            db.wrs_department_report.count({ where })
        ])
        return {
            data: data.map(WrsDepartmentReportMapper.toDomain),
            total
        }
    }

    /**
     * 建立部門週報草稿
     * @param props WrsDepartmentReportProps
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async create(props: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const d = WrsDepartmentReportMapper.toPersistence(props)
        console.log(d)
        const created = await db.wrs_department_report.create({
            data: d
        })
        return WrsDepartmentReportMapper.toDomain(created)
    }

    /**
     * 查詢單一部門週報
     * @param id 部門週報ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async findById(id: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const found = await db.wrs_department_report.findUnique({ where: { id } })
        if (!found) throw new Error('DepartmentReport not found')
        return WrsDepartmentReportMapper.toDomain(found)
    }

    /**
     * 根據週次ID和部門ID查詢部門週報
     * @param props 查詢參數 { weekId: string, orgId: string }
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async find(props: { weekId: string, orgId: string }, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const found = await db.wrs_department_report.findFirst({
            where: {
                week_id: props.weekId,
                org_id: props.orgId
            }
        })
        if (!found) {
            return null
        }
        return WrsDepartmentReportMapper.toDomain(found)
    }

    /**
     * 更新部門週報草稿
     * @param id 部門週報ID
     * @param updateProps 更新內容
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async updateDraft(id: string, updateProps: any, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const updated = await db.wrs_department_report.update({
            where: { id },
            data: updateProps
        })
        return WrsDepartmentReportMapper.toDomain(updated)
    }

    /**
     * 提交部門週報
     * @param id 部門週報ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async submit(id: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const now = new Date().toISOString()
        const updated = await db.wrs_department_report.update({
            where: { id },
            data: { status: 'submitted', submitted_at: now }
        })
        return WrsDepartmentReportMapper.toDomain(updated)
    }

    /**
     * 處主管標記已審閱
     * @param id 部門週報ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async markReviewed(id: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const updated = await db.wrs_department_report.update({
            where: { id },
            data: { reviewed_by_supervisor: true }
        })
        return WrsDepartmentReportMapper.toDomain(updated)
    }

    /**
     * AI 彙整部門週報
     * @param orgId 部門ID
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async getDepAllMemberReportByOrgId(orgId: string, weekId: string, tx?: Prisma.TransactionClient): Promise<LlmSummaryReportInput[]> {
        const db = tx ?? this.prisma
        const employeeReports = await db.wrs_employee_report.findMany({
            where: { org_id: orgId, week_id: weekId, status: EmployeeReportStatus.submitted },
            include: {
                work_items: true
            },
        })
        console.log({ employeeReports })

        return this.toLlmInput(employeeReports);
    }

    toLlmInput(reports: any): LlmSummaryReportInput[] {
        const list = []
        for (let employee of reports) {
            for (let item of employee.work_items) {
                list.push({
                    name: item.name,
                    progress_percent: item.progress_percent,
                    type: item.type,
                    result: item.result,
                    suggestion: item.suggestion,
                    next_week_goal: item.next_week_goal,
                    employee_title: item.employee_title,
                } as LlmSummaryReportInput)
            }
        }
        return list
    }

    @CatchRepositoryError()
    async getOrgNameByOrgId(orgId: string, tx?: Prisma.TransactionClient): Promise<string> {
        const db = tx ?? this.prisma
        //Warring: ACL TODO(Mike)
        const org = await db.organization.findUniqueOrThrow({
            where: {
                id: orgId
            },
            select: { name: true }
        })
        return org.name
    }

    /**
     * Dashboard 資料查詢 - 取得公司概況和部門摘要分佈
     * @param userId 使用者ID
     * @param weekId 週期ID (必要參數)
     * @param tx Transaction client
     */
    @CatchRepositoryError()
    async dashboard(userId: string, weekId: string, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma

        // 取得使用者可閱覽的組織ID
        const orgIds = this.magicfunction(userId);

        // 如果沒有權限看任何組織，回傳空資料
        if (orgIds.length === 0) {
            return {
                companyOverview: {
                    totalDepartments: 0,
                    totalEmployees: 0,
                    totalWorkItems: 0,
                    totalProjects: 0,
                    departmentSubmissionRate: 0,
                    employeeSubmissionRate: 0,
                    projectCompletionRate: 0,
                    workItemGrowthRate: 0
                },
                departmentDistribution: []
            };
        }

        // 建立查詢條件
        const whereCondition = {
            org_id: { in: orgIds },
            week_id: weekId
        };

        // 先查找前一週期
        const previousWeek = await db.wrs_week.findFirst({
            where: {
                end_date: {
                    lt: (await db.wrs_week.findUnique({
                        where: { id: weekId },
                        select: { start_date: true }
                    }))?.start_date
                }
            },
            orderBy: { end_date: 'desc' }
        });

        // 並行查詢各種統計資料
        const [
            departmentReports,
            workItems,
            organizations,
            employeeAssignments,
            previousWorkItems
        ] = await Promise.all([
            // 部門週報統計
            db.wrs_department_report.findMany({
                where: whereCondition,
                select: {
                    org_id: true,
                    status: true,
                    submitted_at: true
                }
            }),
            // 工作項目統計 - 只計算 status 為 'done' 的項目
            db.wrs_work_item.findMany({
                where: {
                    ...whereCondition,
                    status: 'done'
                },
                select: {
                    org_id: true,
                    type: true,
                    progress_percent: true,
                    status: true,
                    employee_id: true
                }
            }),
            // 組織資訊
            db.organization.findMany({
                where: {
                    id: { in: orgIds },
                    type: { in: ['department', 'office'] }
                },
                select: {
                    id: true,
                    name: true
                }
            }),
            // 員工分配資訊 - 用於正確計算員工數
            db.employee_assignment.findMany({
                where: {
                    org_id: { in: orgIds },
                    end_date: null
                },
                select: {
                    org_id: true,
                    employee_id: true
                }
            }),
            // 前一週期的工作項目統計 - 用於計算增減率
            previousWeek ? db.wrs_work_item.findMany({
                where: {
                    org_id: { in: orgIds },
                    week_id: previousWeek.id,
                    status: 'done'
                },
                select: {
                    org_id: true,
                    type: true,
                    employee_id: true
                }
            }) : Promise.resolve([])
        ]);

        // 計算公司概況
        const totalDepartments = organizations.length;
        // 修正：使用 employee_assignment 來計算實際員工數，而非基於 employeeReports
        const totalEmployees = new Set(employeeAssignments.map(ea => ea.employee_id)).size;
        const totalWorkItems = workItems.length;
        const totalProjects = workItems.filter(wi => wi.type === 'project').length;

        const submittedDepartmentReports = departmentReports.filter(dr => dr.status === 'submitted').length;
        const departmentSubmissionRate = totalDepartments > 0 ? Math.round((submittedDepartmentReports / totalDepartments) * 100) : 0;

        // 修正：員工提交率改為檢查是否有 status 為 'done' 的 work_item
        const employeesWithDoneWorkItems = new Set(workItems.map(wi => wi.employee_id)).size;
        const employeeSubmissionRate = totalEmployees > 0 ? Math.round((employeesWithDoneWorkItems / totalEmployees) * 100) : 0;

        // 修正：專案完成率改為所有 status 為 'done' 的專案進度平均值
        const doneProjectProgressValues = workItems
            .filter(wi => wi.type === 'project' && wi.status === 'done')
            .map(wi => wi.progress_percent || 0);
        const projectCompletionRate = doneProjectProgressValues.length > 0
            ? Math.round(doneProjectProgressValues.reduce((sum, progress) => sum + progress, 0) / doneProjectProgressValues.length)
            : 0;

        // 計算本週與上週的摘要總數比較
        const currentTotalWorkItems = workItems.length;
        const previousTotalWorkItems = previousWorkItems.length;
        const workItemGrowthRate = previousTotalWorkItems > 0
            ? Math.round(((currentTotalWorkItems - previousTotalWorkItems) / previousTotalWorkItems) * 100)
            : 0;

        // 計算部門分佈統計
        const departmentDistribution = organizations.map(org => {
            const orgWorkItems = workItems.filter(wi => wi.org_id === org.id);
            const orgDepartmentReport = departmentReports.find(dr => dr.org_id === org.id);
            // 修正：使用 employee_assignment 來計算實際員工數
            const orgEmployeeAssignments = employeeAssignments.filter(ea => ea.org_id === org.id);

            const employeeCount = new Set(orgEmployeeAssignments.map(ea => ea.employee_id)).size;
            const workItemCount = orgWorkItems.length;
            const projectCount = orgWorkItems.filter(wi => wi.type === 'project').length;

            // 修正：員工提交率改為檢查是否有 status 為 'done' 的 work_item
            const orgEmployeesWithDoneWorkItems = new Set(orgWorkItems.map(wi => wi.employee_id)).size;

            return {
                orgId: org.id,
                orgName: org.name,
                employeeCount,
                workItemCount,
                projectCount,
                departmentReportStatus: orgDepartmentReport?.status || 'draft',
                departmentReportSubmittedAt: orgDepartmentReport?.submitted_at || null,
                employeeSubmissionRate: employeeCount > 0 ?
                    Math.round((orgEmployeesWithDoneWorkItems / employeeCount) * 100) : 0
            };
        });

        return {
            companyOverview: {
                totalDepartments,
                totalEmployees,
                totalWorkItems,
                totalProjects,
                departmentSubmissionRate,
                employeeSubmissionRate,
                projectCompletionRate,
                workItemGrowthRate
            },
            departmentDistribution
        };
    }
}
