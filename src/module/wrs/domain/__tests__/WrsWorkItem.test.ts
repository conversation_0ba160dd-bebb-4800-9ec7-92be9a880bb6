import { WrsWorkItem } from '../WrsWorkItem'
import { WrsWorkItemProps } from '../domain.schema'

describe('WrsWorkItem', () => {
    const baseProps: WrsWorkItemProps = {
        id: 'wi_20240603_001',
        employeeReportId: 'erpt_20240603_001',
        weekId: 'p_20240603_20240609',
        type: 'project',
        name: '專案A開發',
        progressPercent: 80,
        result: '完成模組設計與單元測試',
        suggestion: '建議增加測試資源',
        nextWeekGoal: '完成整合測試',
        status: 'draft'
    }

    it('should return props via getProps()', () => {
        const item = new WrsWorkItem(baseProps)
        expect(item.getProps()).toEqual(baseProps)
    })

    it('should update and keep history if requested', () => {
        const item = new WrsWorkItem(baseProps)
        const updated = item.update({ name: '新名稱' }, true)
        expect(updated.getProps().name).toBe('新名稱')
        expect(updated.getProps().historyRefId).toBe(baseProps.id)
        expect(updated.getProps().status).not.toBe('deleted')
    })

    it('should update without history if not requested', () => {
        const item = new WrsWorkItem(baseProps)
        const updated = item.update({ name: '新名稱' }, false)
        expect(updated.getProps().name).toBe('新名稱')
        expect(updated.getProps().historyRefId).toBeUndefined()
    })
})
