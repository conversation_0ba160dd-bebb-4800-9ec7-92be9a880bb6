import { WrsDepartmentReport } from '../WrsDepartmentReport'
import { WrsDepartmentReportProps } from '../domain.schema'

describe('WrsDepartmentReport', () => {
    const baseProps: WrsDepartmentReportProps = {
        id: 'dept_20240603_001',
        orgId: 'org_001',
        weekId: 'p_20240603_20240609',
        summaryEmployeeId: 'emp_002',
        summary: '本週完成專案A與B的整合',
        highlight: '專案A提前完成，專案B進度落後',
        plan: '推進專案B進度，啟動專案C',
        status: 'draft',
        reviewedBySupervisor: false,
    }

    it('should return props via getProps()', () => {
        const report = new WrsDepartmentReport(baseProps)
        expect(report.getProps()).toEqual(baseProps)
    })

    it('should submit draft report', () => {
        const report = new WrsDepartmentReport(baseProps)
        const submittedAt = '2025-06-05T12:00:00Z'
        const submitted = report.submit(submittedAt)
        expect(submitted.getProps().status).toBe('submitted')
        expect(submitted.getProps().submittedAt).toBe(submittedAt)
    })

    it('should not submit already submitted report', () => {
        const submitted = new WrsDepartmentReport({ ...baseProps, status: 'submitted', submittedAt: '2025-06-05T12:00:00Z' })
        expect(() => submitted.submit('2025-06-05T12:00:00Z')).toThrow('Department report already submitted')
    })

    it('should save draft if not submitted', () => {
        const report = new WrsDepartmentReport(baseProps)
        const draft = report.saveDraft()
        expect(draft.getProps().status).toBe('draft')
    })

    it('should not save draft after submission', () => {
        const submitted = new WrsDepartmentReport({ ...baseProps, status: 'submitted', submittedAt: '2025-06-05T12:00:00Z' })
        expect(() => submitted.saveDraft()).toThrow('Cannot save draft after submission')
    })

    it('should mark reviewed if submitted', () => {
        const submitted = new WrsDepartmentReport({ ...baseProps, status: 'submitted', submittedAt: '2025-06-05T12:00:00Z', reviewedBySupervisor: false })
        const reviewed = submitted.markReviewed()
        expect(reviewed.getProps().reviewedBySupervisor).toBe(true)
    })

    it('should not mark reviewed if not submitted', () => {
        const draft = new WrsDepartmentReport(baseProps)
        expect(() => draft.markReviewed()).toThrow('Can only review after submission')
    })
})
