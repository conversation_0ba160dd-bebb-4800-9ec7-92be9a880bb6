import { WrsWeek } from '../WrsWeek'
import { WrsWeekProps } from '../domain.schema'

describe('WrsWeek', () => {
    const props: WrsWeekProps = {
        id: 'p_20240603_20240609',
        startDate: '2024-06-03',
        endDate: '2024-06-09',
    }

    it('should return props via getProps()', () => {
        const week = new WrsWeek(props)
        expect(week.getProps()).toEqual(props)
    })
})
