import { WrsEmployeeReport } from '../WrsEmployeeReport'
import { WrsEmployeeReportProps } from '../domain.schema'

describe('WrsEmployeeReport', () => {
    const baseProps: WrsEmployeeReportProps = {
        id: 'erpt_20240603_001',
        employeeId: 'emp_001',
        weekId: 'p_20240603_20240609',
        summary: '本週完成專案A開發與測試',
        status: 'draft',
        // submittedAt 可選
    }

    it('should return props via getProps()', () => {
        const report = new WrsEmployeeReport(baseProps)
        expect(report.getProps()).toEqual(baseProps)
    })

    it('should submit draft report', () => {
        const report = new WrsEmployeeReport(baseProps)
        const submittedAt = '2025-06-05T12:00:00Z'
        const submitted = report.submit(submittedAt)
        expect(submitted.getProps().status).toBe('submitted')
        expect(submitted.getProps().submittedAt).toBe(submittedAt)
    })

    it('should not submit already submitted report', () => {
        const submitted = new WrsEmployeeReport({ ...baseProps, status: 'submitted', submittedAt: '2025-06-05T12:00:00Z' })
        expect(() => submitted.submit('2025-06-05T12:00:00Z')).toThrow('Report already submitted')
    })

    it('should save draft if not submitted', () => {
        const report = new WrsEmployeeReport(baseProps)
        const draft = report.saveDraft()
        expect(draft.getProps().status).toBe('draft')
    })

    it('should not save draft after submission', () => {
        const submitted = new WrsEmployeeReport({ ...baseProps, status: 'submitted', submittedAt: '2025-06-05T12:00:00Z' })
        expect(() => submitted.saveDraft()).toThrow('Cannot save draft after submission')
    })
})
