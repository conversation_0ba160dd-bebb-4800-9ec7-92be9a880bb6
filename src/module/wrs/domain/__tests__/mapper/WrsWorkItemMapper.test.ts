// Jest unit tests for WrsWorkItemMapper
import { WrsWorkItemMapper } from '../../mapper/WrsWorkItemMapper'
import { WrsWorkItem } from '../../WrsWorkItem'

describe('WrsWorkItemMapper', () => {
  it('should map raw to domain', () => {
    const raw = {
      id: 'wi_001',
      employeeReportId: 'erpt_20240603_001',
      weekId: 'p_20240603_20240609',
      type: 'project',
      name: '工作A',
      progressPercent: 80,
      result: '完成',
      suggestion: '無',
      nextWeekGoal: '繼續B',
      progressValue: 80,
      historyRefId: null,
      status: 'draft',
    }
    const domain = WrsWorkItemMapper.toDomain(raw)
    expect(domain).toBeInstanceOf(WrsWorkItem)
    expect(domain.getProps()).toMatchObject(raw)
  })

  it('should map domain props to persistence', () => {
    const props = {
      id: 'wi_001',
      employeeReportId: 'erpt_20240603_001',
      weekId: 'p_20240603_20240609',
      type: 'project' as const,
      name: '工作A',
      progressPercent: 80,
      result: '完成',
      suggestion: '無',
      nextWeekGoal: '繼續B',
      progressValue: 80,
      historyRefId: undefined,
      status: 'draft',
    }
    const persistence = WrsWorkItemMapper.toPersistence(props)
    expect(persistence).toMatchObject({
      ...props,
      historyRefId: undefined,
    })
  })
})
