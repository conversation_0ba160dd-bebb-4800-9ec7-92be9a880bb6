// Jest unit tests for WrsEmployeeReportMapper
import { WrsEmployeeReportMapper } from '../../mapper/WrsEmployeeReportMapper'
import { WrsEmployeeReport } from '../../WrsEmployeeReport'

describe('WrsEmployeeReportMapper', () => {
  it('should map raw to domain', () => {
    const raw = {
      id: 'erpt_20240603_001',
      employeeId: 'emp_001',
      weekId: 'p_20240603_20240609',
      summary: 'summary',
      submittedAt: '2024-06-05T10:00:00.000Z',
      status: 'submitted',
    }
    const domain = WrsEmployeeReportMapper.toDomain(raw)
    expect(domain).toBeInstanceOf(WrsEmployeeReport)
    expect(domain.getProps()).toMatchObject(raw)
  })

  it('should map domain props to persistence', () => {
    const props = {
      id: 'erpt_20240603_001',
      employeeId: 'emp_001',
      weekId: 'p_20240603_20240609',
      summary: 'summary',
      submittedAt: '2024-06-05T10:00:00.000Z',
      status: 'submitted' as const,
    }
    const persistence = WrsEmployeeReportMapper.toPersistence(props)
    expect(persistence).toMatchObject({
      id: props.id,
      employeeId: props.employeeId,
      weekId: props.weekId,
      summary: props.summary,
      submittedAt: new Date(props.submittedAt),
      status: props.status,
    })
  })
})
