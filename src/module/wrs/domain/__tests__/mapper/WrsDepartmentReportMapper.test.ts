// Jest unit tests for WrsDepartmentReportMapper
import { WrsDepartmentReportMapper } from '../../mapper/WrsDepartmentReportMapper'
import { WrsDepartmentReport } from '../../WrsDepartmentReport'

describe('WrsDepartmentReportMapper', () => {
  it('should map raw to domain', () => {
    const raw = {
      id: 'dept_001',
      orgId: 'org_001',
      weekId: 'p_20240603_20240609',
      summaryEmployeeId: 'emp_001',
      summary: 'summary',
      highlight: 'highlight',
      plan: 'plan',
      submittedAt: '2024-06-05T10:00:00.000Z',
      status: 'submitted',
      reviewedBySupervisor: true,
    }
    const domain = WrsDepartmentReportMapper.toDomain(raw)
    expect(domain).toBeInstanceOf(WrsDepartmentReport)
    expect(domain.getProps()).toMatchObject(raw)
  })

  it('should map domain props to persistence', () => {
    const props = {
      id: 'dept_001',
      orgId: 'org_001',
      weekId: 'p_20240603_20240609',
      summaryEmployeeId: 'emp_001',
      summary: 'summary',
      highlight: 'highlight',
      plan: 'plan',
      submittedAt: '2024-06-05T10:00:00.000Z',
      status: 'submitted' as const,
      reviewedBySupervisor: true,
    }
    const persistence = WrsDepartmentReportMapper.toPersistence(props)
    expect(persistence).toMatchObject({
      ...props,
      submittedAt: new Date(props.submittedAt),
    })
  })
})
