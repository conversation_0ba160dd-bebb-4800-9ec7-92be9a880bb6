// Jest unit tests for WrsWeekMapper
import { WrsWeekMapper } from '../../mapper/WrsWeekMapper'
import { WrsWeek } from '../../WrsWeek'

describe('WrsWeekMapper', () => {
  it('should map raw to domain', () => {
    const raw = { id: 'p_20240603_20240609', startDate: '2024-06-03', endDate: '2024-06-09' }
    const domain = WrsWeekMapper.toDomain(raw)
    expect(domain).toBeInstanceOf(WrsWeek)
    expect(domain.getProps()).toEqual(raw)
  })

  it('should map domain props to persistence', () => {
    const props = { id: 'p_20240603_20240609', startDate: '2024-06-03', endDate: '2024-06-09' }
    const persistence = WrsWeekMapper.toPersistence(props)
    expect(persistence).toMatchObject({
      id: props.id,
      startDate: new Date(props.startDate),
      endDate: new Date(props.endDate),
    })
  })
})
