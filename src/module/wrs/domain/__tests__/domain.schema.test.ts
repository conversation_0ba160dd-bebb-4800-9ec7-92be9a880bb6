import { describe, it, expect } from '@jest/globals';
import {
  WrsWeekPropsSchema,
  WrsEmployeeReportPropsSchema,
  WrsWorkItemPropsSchema,
  WrsDepartmentReportPropsSchema
} from '../domain.schema';

describe('WrsWeekPropsSchema', () => {
  it('should validate correct props', () => {
    const data = {
      id: 'p_20240603_20240609',
      startDate: '2024-06-03',
      endDate: '2024-06-09',
    };
    expect(() => WrsWeekPropsSchema.parse(data)).not.toThrow();
  });
});

describe('WrsEmployeeReportPropsSchema', () => {
  it('should fail without required fields', () => {
    expect(() => WrsEmployeeReportPropsSchema.parse({})).toThrow();
  });
  it('should validate correct props', () => {
    const data = {
      id: 'erpt_20240603_001',
      employeeId: 'emp_001',
      weekId: 'p_20240603_20240609',
      summary: '本週完成專案A開發與測試',
      status: 'draft',
    };
    expect(() => WrsEmployeeReportPropsSchema.parse(data)).not.toThrow();
  });
});

describe('WrsWorkItemPropsSchema', () => {
  it('should fail without required fields', () => {
    expect(() => WrsWorkItemPropsSchema.parse({})).toThrow();
  });
  it('should validate correct props', () => {
    const data = {
      id: 'wi_20240603_001',
      employeeReportId: 'erpt_20240603_001',
      weekId: 'p_20240603_20240609',
      type: 'project',
      name: '專案A開發',
      progressPercent: 80,
      result: '完成模組設計與單元測試',
      suggestion: '建議增加測試資源',
      nextWeekGoal: '完成整合測試',
      progressValue: 80,
      status: 'draft',
    };
    expect(() => WrsWorkItemPropsSchema.parse(data)).not.toThrow();
  });
});

describe('WrsDepartmentReportPropsSchema', () => {
  it('should fail without required fields', () => {
    expect(() => WrsDepartmentReportPropsSchema.parse({})).toThrow();
  });
  it('should validate correct props', () => {
    const data = {
      id: 'dept_20240603_001',
      orgId: 'org_001',
      weekId: 'p_20240603_20240609',
      summaryEmployeeId: 'emp_002',
      summary: '本週完成專案A與B的整合',
      highlight: '專案A提前完成，專案B進度落後',
      plan: '推進專案B進度，啟動專案C',
      status: 'draft',
      reviewedBySupervisor: false,
    };
    expect(() => WrsDepartmentReportPropsSchema.parse(data)).not.toThrow();
  });
});
