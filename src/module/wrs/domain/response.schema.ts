import { z } from "zod";
import {  WrsDepartmentReportPropsSchema, WrsEmployeeReportPropsSchema, WrsWorkItemPropsSchema } from "./domain.schema";

export const NumberOfItemsSchema = z.object({
  total: z.number().int().min(0).describe('工作項目數量統計，表示該週的總工作項目總數'),
  project: z.number().int().min(0).describe('專案工作項目數量'),
  routine: z.number().int().min(0).describe('例行工作項目數量'),
  support: z.number().int().min(0).describe('支援工作項目數量'),
  admin: z.number().int().min(0).describe('行政工作項目數量'),
  training: z.number().int().min(0).describe('培訓工作項目數量'),
  other: z.number().int().min(0).describe('其他工作項目數量')
})

export const StatisticsEmployeeReportResponseSchema = WrsEmployeeReportPropsSchema.extend({
  statistics: NumberOfItemsSchema,
});
export type StatisticsEmployeeReportResponse = Partial<z.infer<typeof StatisticsEmployeeReportResponseSchema>>;
export const StatisticsDepartmentReportResponseSchema = WrsDepartmentReportPropsSchema.extend({
  statistics: NumberOfItemsSchema,
});
export type StatisticsDepartmentReportResponse =Partial<z.infer<typeof StatisticsDepartmentReportResponseSchema>>;

export const EmployeeAggregationSchema = WrsEmployeeReportPropsSchema.extend({
  items: z.array(WrsWorkItemPropsSchema).describe('工作項目列表，包含該週報的所有工作項目')
})
export type EmployeeAggregation = z.infer<typeof EmployeeAggregationSchema>;

// Dashboard 回應 Schema
export const CompanyOverviewSchema = z.object({
  totalDepartments: z.number().int().min(0).describe('總部門數'),
  totalEmployees: z.number().int().min(0).describe('總員工數'),
  totalWorkItems: z.number().int().min(0).describe('總工作項目數'),
  totalProjects: z.number().int().min(0).describe('總專案數'),
  departmentSubmissionRate: z.number().int().min(0).max(100).describe('部門提交率(%)'),
  employeeSubmissionRate: z.number().int().min(0).max(100).describe('員工提交率(%)'),
  projectCompletionRate: z.number().int().min(0).max(100).describe('專案完成率(%)'),
  workItemGrowthRate: z.number().int().describe('摘要總數週比週增減率(%)')
});

export const DepartmentDistributionSchema = z.object({
  orgId: z.string().describe('部門ID'),
  orgName: z.string().describe('部門名稱'),
  employeeCount: z.number().int().min(0).describe('部門員工數'),
  workItemCount: z.number().int().min(0).describe('部門工作項目數'),
  projectCount: z.number().int().min(0).describe('部門專案數'),
  departmentReportStatus: z.enum(['draft', 'submitted']).describe('部門週報狀態'),
  departmentReportSubmittedAt: z.string().datetime().nullable().describe('部門週報提交時間'),
  employeeSubmissionRate: z.number().int().min(0).max(100).describe('部門內員工提交率(%)')
});

export const DashboardResponseSchema = z.object({
  companyOverview: CompanyOverviewSchema.describe('公司概況統計'),
  departmentDistribution: z.array(DepartmentDistributionSchema).describe('部門摘要分佈')
});

export type CompanyOverview = z.infer<typeof CompanyOverviewSchema>;
export type DepartmentDistribution = z.infer<typeof DepartmentDistributionSchema>;
export type DashboardResponse = z.infer<typeof DashboardResponseSchema>;