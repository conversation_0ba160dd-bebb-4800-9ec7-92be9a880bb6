import { WrsDepartmentReportProps } from '../domain.schema';
import { StatisticsDepartmentReportResponse } from '../response.schema';
import { WrsDepartmentReport } from '../WrsDepartmentReport'
import { Prisma } from "@prisma/client";

export class WrsDepartmentReportMapper {
    static toDomain(raw: any): WrsDepartmentReport {
        return new WrsDepartmentReport({
            id: raw.id,
            orgId: raw.org_id,
            weekId: raw.week_id,
            summaryEmployeeId: raw.summary_employee_id,
            summary: raw.summary,
            highlight: raw.highlight,
            plan: raw.plan,
            submittedAt: raw.submitted_at ? new Date(raw.submitted_at).toISOString() : null,
            status: raw.status,
            reviewedBySupervisor: raw.reviewed_by_supervisor,
        })
    }

    static toPersistence(props: WrsDepartmentReportProps): Prisma.wrs_department_reportCreateInput {
        console.log('before toPersistence', props);
        return {
            id: props.id,
            org_id: props.orgId,
            week: { connect: { id: props.weekId } },
            summary_employee_id: props.summaryEmployeeId,
            summary: props.summary,
            highlight: props.highlight,
            plan: props.plan,
            submitted_at: props.submittedAt ? new Date(props.submittedAt) : null,
            status: props.status,
            reviewed_by_supervisor: props.reviewedBySupervisor,
        }
    }

    static toStatistics(props: any): StatisticsDepartmentReportResponse{
        return {
            id: props.id,
            orgId: props.org_id,
            weekId: props.week_id,
            summaryEmployeeId: props.summary_employee_id,
            summary: props.summary,
            highlight: props.highlight,
            plan: props.plan,
            submittedAt: props.submitted_at ? new Date(props.submitted_at).toISOString() : null,
            status: props.status,
            reviewedBySupervisor: props.reviewed_by_supervisor,
            statistics: props.statistics
        }
    }
}
