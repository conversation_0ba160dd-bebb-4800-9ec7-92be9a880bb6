import { WrsWorkItem } from '../WrsWorkItem'
import { WrsWorkItemProps } from '../domain.schema'
import { Prisma } from "@prisma/client";

export class WrsWorkItemMapper {
    static toDomain(raw: any): WrsWorkItem {
        return new WrsWorkItem({
            id: raw.id,
            employeeReportId: raw.employee_report_id,
            employeeId: raw.employee_id,
            employeeTitle: raw.employee_title,
            orgId: raw.org_id,
            weekId: raw.week_id,
            type: raw.type,
            name: raw.name,
            progressPercent: raw.progress_percent,
            result: raw.result,
            suggestion: raw.suggestion,
            nextWeekGoal: raw.next_week_goal,
            quantitativeMetric: raw.quantitative_metric,
            historyRefId: raw.history_ref_id,
            submittedAt: raw.submitted_at,
            doneAt: raw.done_at,
            status: raw.status,
            editorEmployeeId: raw.editor_employee_id,
        })
    }

    static toPersistence(props: WrsWorkItemProps): Prisma.wrs_work_itemCreateInput {
        return {
            id: props.id,
            employee_report: { connect: { id: props.employeeReportId } },
            employee_id: props.employeeId,
            employee_title: props.employeeTitle,
            org_id: props.orgId,
            week: { connect: { id: props.weekId } },
            type: props.type,
            name: props.name,
            progress_percent: props.progressPercent,
            result: props.result,
            suggestion: props.suggestion,
            next_week_goal: props.nextWeekGoal,
            quantitative_metric: props.quantitativeMetric,
            history_ref_id: props.historyRefId,
            submitted_at: props.submittedAt,
            done_at: props.doneAt,
            status: props.status,
            editor_employee_id: props.editorEmployeeId,
        }
    }
}
