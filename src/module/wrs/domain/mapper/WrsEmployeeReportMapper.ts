import { WrsEmployeeReport } from '../WrsEmployeeReport'
import { WrsEmployeeReportProps } from '../domain.schema'
import { Prisma } from "@prisma/client";
import { EmployeeAggregation, StatisticsEmployeeReportResponse } from '../response.schema';

export class WrsEmployeeReportMapper {
    static toDomain(raw: any): WrsEmployeeReport {
        return new WrsEmployeeReport({
            id: raw.id,
            orgId: raw.org_id,
            employeeId: raw.employee_id,
            employeeTitle: raw.employee_title,
            weekId: raw.week_id,
            submittedAt: raw.submitted_at ? new Date(raw.submitted_at).toISOString() : null,
            status: raw.status,
        })
    }

    static toPersistence(props: WrsEmployeeReportProps): Prisma.wrs_employee_reportCreateInput {
        return {
            id: props.id,
            employee_id: props.employeeId,
            employee_title:props.employeeTitle,
            org_id: props.orgId,
            week: { connect: { id: props.weekId } },
            submitted_at: props.submittedAt ? new Date(props.submittedAt) : undefined,
            status: props.status,
        }
    }
    static toStatistics(props: any): StatisticsEmployeeReportResponse {
        const { statistics, ...rawDomain } = props

        return {
            id: rawDomain.id,
            status: rawDomain.status,
            orgId: rawDomain.org_id,
            employeeId: rawDomain.employee_id,
            weekId: rawDomain.week_id,
            submittedAt: rawDomain.submitted_at ? new Date(rawDomain.submitted_at).toISOString() : null,
            statistics: statistics
        }
    }
    static toAggregationResponse(props: any): EmployeeAggregation {
        return {
            id: props.id,
            orgId: props.org_id,
            employeeId: props.employee_id,
            employeeTitle: props.employee_title,
            weekId: props.week_id,
            submittedAt: props.submitted_at ? new Date(props.submitted_at).toISOString() : null,
            status: props.status,
            items: props.work_items ? props.work_items.map((item: any) => ({
                id: item.id,
                employeeReportId: item.employee_report_id,
                employee_title: item.employee_title,
                employeeId: item.employee_id,
                orgId: item.org_id,
                weekId: item.week_id,
                type: item.type,
                name: item.name,
                progressPercent: item.progress_percent,
                result: item.result,
                suggestion: item.suggestion,
                nextWeekGoal: item.next_week_goal,
                progressValue: item.progress_value,
                historyRefId: item.history_ref_id,
                status: item.status,
            })) : []
        }
    }
}

