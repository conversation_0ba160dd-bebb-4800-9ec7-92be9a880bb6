import { WrsWeek } from '../WrsWeek'
import { WrsWeekProps } from '../domain.schema'
import { Prisma } from "@prisma/client";

export class WrsWeekMapper {
    static toDomain(raw: any): WrsWeek {
        return new WrsWeek({
            id: raw.id,
            startDate: raw.start_date instanceof Date ? raw.start_date.toISOString().slice(0, 10) : raw.start_date,
            endDate: raw.end_date instanceof Date ? raw.end_date.toISOString().slice(0, 10) : raw.end_date,
        })
    }

    static toPersistence(props: WrsWeekProps): Prisma.wrs_weekCreateInput {
        return {
            id: props.id,
            start_date: new Date(props.startDate),
            end_date: new Date(props.endDate),
        }
    }
}
