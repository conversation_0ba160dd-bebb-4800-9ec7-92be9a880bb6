// WrsWorkItem domain entity for wrs module
// 根據 03_domain_model.md 與 domain.schema.ts props 實作
import { WrsWorkItemProps, CreateWrsWorkItemProps } from './domain.schema'

/**
 * WrsWorkItem
 * 員工週報中的具體工作項目，支援版本追蹤與修改記錄。
 * - 僅當所屬 WrsEmployeeReport.status = submitted 時，異動才需保留歷史版本。
 * - 修改需保留歷史版本，status != 'deleted' 標示當前版本。
 */
export class WrsWorkItem {
    /**
     * 建構子
     * @param props 工作項目屬性
     */
    constructor(
        private readonly props: WrsWorkItemProps
    ) {}

    /**
     * 取得所有屬性
     */
    getProps(): WrsWorkItemProps {
        return this.props;
    }

    newUpdate(newProps: Partial<CreateWrsWorkItemProps>) {
        Object.assign(this.props, newProps);
    }

    setAsDeleted() {
        this.props.status = 'deleted';
    }

    getUpdatedItem(updateProps: Partial<WrsWorkItemProps>): WrsWorkItem {
        return new WrsWorkItem({
            ...this.props,
            ...updateProps,
        });
    }

    /**
     * 修改內容並產生新版本（保留歷史）
     * @param newProps 新內容
     * @param keepHistory 是否保留歷史（由外部根據 employeeReport 狀態決定）
     * @returns 新的 WrsWorkItem 實例
     */
    update(newProps: Partial<WrsWorkItemProps>, keepHistory: boolean): WrsWorkItem {
        if (keepHistory) {
            return new WrsWorkItem({
                ...this.props,
                ...newProps,
                historyRefId: this.props.id,
                status: 'submitted',
            });
        } else {
            return new WrsWorkItem({
                ...this.props,
                ...newProps,
            });
        }
    }
}
