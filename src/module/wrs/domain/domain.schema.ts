import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi';
extendZodWithOpenApi(z);

export const WorkItemTypeEnum = z.enum([
  'project', 'routine', 'support', 'admin', 'training', 'other',
]).describe('工作項目類型，project=專案, routine=例行, support=支援, admin=行政, training=培訓, other=其他')
  .openapi({ example: 'project' });
export type WorkItemType = z.infer<typeof WorkItemTypeEnum>;

export enum ReportStatus {
  'draft' = 'draft',
  'submitted' = 'submitted'
}

export const WorkItemStatusEnum = z.enum([
  'draft', 'submitted', 'done', 'deleted'
]);
export type WorkItemStatus = z.infer<typeof WorkItemStatusEnum>;

export const EmployeeReportStatusEnum = z.enum([
  'draft', 'submitted',
]);
export type EmployeeReportStatus = z.infer<typeof EmployeeReportStatusEnum>;

export const DepartmentReportStatusEnum = z.enum([
  'draft', 'submitted',
]);
export type DepartmentReportStatus = z.infer<typeof DepartmentReportStatusEnum>;

export const WrsWeekPropsSchema = z.object({
  id: z.string()
    .describe('週期ID，格式為w_yyyymmdd_yyyymmdd，作為週報唯一標識')
    .openapi({ example: 'p_20240603_20240609' }),
  startDate: z.string().date()
    .describe('週開始日期，格式為YYYY-MM-DD')
    .openapi({ example: '2024-06-03' }),
  endDate: z.string().date()
    .describe('週結束日期，格式為YYYY-MM-DD')
    .openapi({ example: '2024-06-09' }),
});
export type WrsWeekProps = z.infer<typeof WrsWeekPropsSchema>;


export const WrsWorkItemPropsSchema = z.object({
  id: z.string()
    .describe('工作項目ID，唯一標識該工作項')
    .openapi({ example: 'wi_fdsvdqfeqvd' }),
  employeeReportId: z.string()
    .describe('所屬員工週報ID')
    .openapi({ example: 'empw_20240603_20240609_FC0065' }),
  employeeId: z.string()
    .describe('員工ID，對應填報人員')
    .openapi({ example: 'FC0065' }),
  employeeTitle: z.string()
    .describe('員工名稱')
    .openapi({ example: 'Mike' }),
  orgId: z.string()
    .describe('部門ID，對應組織單位')
    .openapi({ example: 'o_department_information' }),
  weekId: z.string()
    .describe('週期ID，對應WrsWeek')
    .openapi({ example: 'w_20240603_20240609' }),
  type: WorkItemTypeEnum
    .describe('工作項目類型，專案/例行/支援/行政/培訓/其他')
    .openapi({ example: 'project' }),
  name: z.string()
    .describe('工作項目名稱')
    .openapi({ example: '專案A開發' }),
  progressPercent: z.number().min(0).max(100)
    .describe('工作進度百分比，0~100')
    .openapi({ example: 80 }),
  result: z.string()
    .describe('本週工作成果說明')
    .openapi({ example: '完成模組設計與單元測試' }),
  suggestion: z.string()
    .describe('本週建議事項')
    .openapi({ example: '建議增加測試資源' }),
  nextWeekGoal: z.string()
    .describe('下週目標')
    .openapi({ example: '完成整合測試' }),
  quantitativeMetric: z.string()
    .describe('量化指標')
    .openapi({ example: '執行速度提升30%' }),
  historyRefId: z.string().optional().nullable()
    .describe('歷史版本參考ID，若為異動則指向原work_item')
    .openapi({ example: 'wi_20240603_001' }),
  submittedAt: z.string().datetime().nullable()
    .describe('個人視角送出時間，ISO 8601格式，僅在狀態為submitted/done時有值')
    .openapi({ example: '2025-06-05T12:00:00Z' }),
  doneAt: z.string().datetime().nullable()
    .describe('部門視角送出時間，ISO 8601格式，僅在狀態為done時有值')
    .openapi({ example: '2025-06-05T12:00:00Z' }),
  status: WorkItemStatusEnum
    .describe('週報狀態，draft為草稿，submitted為已提交部門，done為已送交HR，deleted為舊版本')
    .openapi({ example: 'draft' }),
  editorEmployeeId: z.string().nullable()
    .describe('修改或刪除資料者員工ID')
    .openapi({ example: 'FC0065' }),
});
export type WrsWorkItemProps = z.infer<typeof WrsWorkItemPropsSchema>;
export const CreateWrsWorkItemPropsSchema = WrsWorkItemPropsSchema.omit({
  id: true,
  employeeReportId: true,
  employeeId: true,
  employeeTitle: true,
  orgId: true,
  weekId: true,
  historyRefId: true,
  submittedAt: true,
  doneAt: true,
  status: true,
  editorEmployeeId: true
})
export type CreateWrsWorkItemProps = z.infer<typeof CreateWrsWorkItemPropsSchema>;


export const WrsEmployeeReportPropsSchema = z.object({
  id: z.string()
    .describe('員工週報ID，格式為empw_yyyymmdd_yyyymmdd_{empId}，唯一標識該份週報')
    .openapi({ example: 'empw_20240603_20240609_FC0065' }),
  employeeId: z.string()
    .describe('員工ID，對應填報人員')
    .openapi({ example: 'FC0065' }),
  employeeTitle: z.string()
    .describe('員工名稱')
    .openapi({ example: 'Mike' }),
  orgId: z.string()
    .describe('部門ID，對應組織單位')
    .openapi({ example: 'o_department_information' }),
  weekId: z.string()
    .describe('週期ID，對應WrsWeek')
    .openapi({ example: 'p_20240603_20240609' }),
  submittedAt: z.string().datetime().nullable()
    .describe('提交時間，ISO 8601格式，僅在狀態為submitted時有值')
    .openapi({ example: '2025-06-05T12:00:00Z' }),
  status: EmployeeReportStatusEnum
    .describe('週報狀態，draft為草稿，submitted為已提交')
    .openapi({ example: 'draft' }),
});
export type WrsEmployeeReportProps = z.infer<typeof WrsEmployeeReportPropsSchema>;

export const WrsDepartmentReportPropsSchema = z.object({
  id: z.string()
    .describe('部門週報ID，格式為 orgw_yyyymmdd_yyyymmdd_{orgId}，唯一標識該份部門週報')
    .openapi({ example: 'orgw_yyyymmdd_yyyymmdd_o_department_information' }),
  orgId: z.string()
    .describe('部門ID，對應組織單位')
    .openapi({ example: 'o_department_information' }),
  weekId: z.string()
    .describe('週期ID，對應WrsWeek')
    .openapi({ example: 'w_20240603_20240609' }),
  status: DepartmentReportStatusEnum
    .describe('部門週報狀態，draft為草稿，submitted為已提交')
    .openapi({ example: 'draft' }),
  summaryEmployeeId: z.string()
    .describe('彙整人員ID')
    .openapi({ example: 'FC0065' }),
  summary: z.string()
    .describe('部門本週摘要')
    .openapi({ example: '本週完成專案A與B的整合' }),
  highlight: z.string()
    .describe('本週部門重點')
    .openapi({ example: '專案A提前完成，專案B進度落後' }),
  plan: z.string()
    .describe('下週部門計劃')
    .openapi({ example: '推進專案B進度，啟動專案C' }),
  submittedAt: z.string().datetime().optional().nullable()
    .describe('提交時間，ISO 8601格式，僅在狀態為submitted時有值')
    .openapi({ example: '2025-06-05T12:00:00Z' }),
  reviewedBySupervisor: z.boolean()
    .describe('主管是否已審閱，true為已審閱')
    .openapi({ example: false }),
});
export type WrsDepartmentReportProps = z.infer<typeof WrsDepartmentReportPropsSchema>;
export const CreateWrsDepartmentReportPropsSchema = WrsDepartmentReportPropsSchema.omit({
  id: true,
  orgId: true,
  weekId: true,
  summaryEmployeeId: true,
})
export type CreateWrsDepartmentReportProps = z.infer<typeof CreateWrsDepartmentReportPropsSchema>;