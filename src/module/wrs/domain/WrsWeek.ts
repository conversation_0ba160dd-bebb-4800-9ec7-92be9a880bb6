// WrsWeek domain entity for wrs module
// 根據 03_domain_model.md 與 domain.schema.ts props 實作
import { WrsWeekProps } from './domain.schema'

/**
 * WrsWeek
 * 管理週期資訊，作為所有週報的時間基準。
 * - 週期唯一性，所有報告均依此週期關聯。
 */
export class WrsWeek {
    /**
     * 建構子
     * @param props 週期屬性
     */
    constructor(
        private readonly props: WrsWeekProps
    ) {}

    /**
     * 取得所有屬性
     */
    getProps(): WrsWeekProps {
        return this.props;
    }

    // TODO: 依需求補充方法，目前僅有 getProps
}
