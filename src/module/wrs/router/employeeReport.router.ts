import { Context, Hono } from 'hono'
import { validator } from "hono-openapi/zod";
import { jwtAuthMiddleware } from '@/middleware/auth'
import { WrsEmployeeReportService } from '../app/WrsEmployeeReport.service'
import { ItemResponse, ListResponse, PagingResponse } from '@/module/common/schema'
import { CreateWrsWorkItemPropsSchema, WrsEmployeeReportPropsSchema, WrsWorkItemPropsSchema } from '../domain/domain.schema'
import { openApiTag } from '@/middleware/openapi'
import { EmployeeReportPaginationQuerySchema, ListEmpReportQuerySchema } from '../domain/query.schema'
import { EmployeeAggregationSchema, StatisticsEmployeeReportResponseSchema } from '../domain/response.schema';
import { z } from '@hono/zod-openapi';
import { checkPermission } from '@/middleware/checkPermission';
import { JwtPayload } from '@/module/auth/auth.schema';

export function createWrsEmployeeReportRouter(
    employeeReportService: WrsEmployeeReportService,
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const openApiMiddleware = openApiTag("EmployeeReport");

    /** TODO=> return aggregated data=>done
     * GET  /v0/wrs/employee-reports/statistics/:orgId    列出「所有人」週報與統計(可透過query指定部門或員工與週報狀態)=> done
     * GET  /v0/wrs/employee-reports         查詢個人週報列表
     * GET  /v0/wrs/employee-reports/my-last    查詢"上次"的個人週報(如有draft會回傳draft) => done
     * POST /v0/wrs/employee-reports         儲存個人週報（草稿）, 包含所有item=>done
     * GET  /v0/wrs/employee-reports/:id   查詢單一個人週報
     * (deprecated) PATCH /v0/wrs/employee-reports/:id   更新個人週報（草稿)
     * POST /v0/wrs/employee-reports/:id/submit 提交個人週報
     */

    router.get('/statistics/my-org',
        openApiMiddleware({
            description: "部門視角, 列出自己部門中「所有人」週報統計(可透過query指定員工與週報狀態)",
            responsesSchema: ListResponse(StatisticsEmployeeReportResponseSchema)
        }),
        checkPermission(`department-report:read:department`),
        validator('query', ListEmpReportQuerySchema.omit({ orgId: true })),
        async (c: Context) => {
            const query = c.req.query();
            const user = c.get('user') as JwtPayload;
            const reports = await employeeReportService.statistics(user, query);
            return c.json({
                success: true,
                total: reports.length,
                data: reports
            });
        }
    )

    /**
     * GET /v0/wrs/employee-reports/statistics
     * 列出「所有人」週報統計(可透過query指定部門或員工)
     */
    /** 
    router.get('/statistics/:orgId',
        openApiMiddleware({
            description: "列出某組織「所有人」週報統計(可透過query指定部門或員工與週報狀態)",
            responsesSchema: ListResponse(StatisticsEmployeeReportResponseSchema)
        }),
        validator('query', ListEmpReportQuerySchema),
        async (c: Context) => {
            const query = c.req.query();
            const orgId = c.req.param('orgId');

            const reports = await employeeReportService.statistics(orgId, query)
            return c.json({
                success: true,
                total: reports.length,
                data: reports
            });
        }
    )
        */

    /**
     * GET /v0/wrs/employee-reports
     * 查詢個人週報列表
     */
    /**
    router.get('/',
        openApiMiddleware({
            description: "查詢個人週報列表",
            responsesSchema: PagingResponse(EmployeeAggregationSchema)
        }),
        validator('query', EmployeeReportPaginationQuerySchema),
        async (c: Context) => {
            const query = c.req.query();
            const user = c.get('user');

            const reports = await employeeReportService.paging(query)
            return c.json({
                success: true,
                total: reports.total,
                data: reports.data
            });
        }
    )
        */

    /**
     * POST /v0/wrs/employee-reports
     * 儲存個人週報（草稿）包含工作項目
     */
    // router.post('/',
    //     openApiMiddleware({
    //         description: "儲存個人週報（草稿）包含工作項目",
    //         responsesSchema: ItemResponse(WrsEmployeeReportPropsSchema)
    //     }),
    //     validator('json', z.array(CreateWrsWorkItemPropsSchema)),
    //     async (c:Context) => {
    //         const items = await c.req.json();
    //         const user = c.get('user');
    //         const result = await employeeReportService.createDraftWithWorkItems(user.id,items); // Updated service call
    //         return c.json({ success: true, data: result.getProps() });
    //     }
    // )

    /**
     * GET  /v0/wrs/employee-reports/my-last
     * 查詢個人上次週報
     */
    // router.get('/my-last',
    //     openApiMiddleware({
    //         description: "查詢個人上次/草稿週報",
    //         responsesSchema: ItemResponse(EmployeeAggregationSchema)
    //     }),
    //     async (c: Context) => {
    //         const user = c.get('user');

    //         const result = await employeeReportService.getLast(user.id)
    //         return c.json({ success: true, data: result })
    //     }
    // )
    /**
     * GET /v0/wrs/employee-reports/:id
     * 查詢單一個人週報
     */
    // router.get('/:id',
    //     openApiMiddleware({
    //         description: "查詢單一個人週報",
    //         responsesSchema: ItemResponse(EmployeeAggregationSchema)
    //     }),
    //     async (c) => {
    //         const id = c.req.param('id')
    //         const result = await employeeReportService.getById(id)
    //         return c.json({ success: true, data: result })
    //     }
    // )

    /**
     * POST /v0/wrs/employee-reports/:id/submit
     * 提交個人週報
     */
    // router.post('/:id/submit',
    //     openApiMiddleware({
    //         description: "提交個人週報",
    //         responsesSchema: ItemResponse(WrsEmployeeReportPropsSchema)
    //     }),
    //     async (c) => {
    //         const id = c.req.param('id')
    //         const result = await employeeReportService.submit(id)
    //         return c.json({ success: true, data: result.getProps() })
    //     }
    // )

    return router
}