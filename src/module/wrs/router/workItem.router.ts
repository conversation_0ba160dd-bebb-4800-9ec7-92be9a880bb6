import { Hono } from 'hono'
import { validator } from "hono-openapi/zod";
import { Context } from 'hono'
import { WrsWorkItemService } from '../app/WrsWorkItem.service'
import { WrsWorkItemPropsSchema, CreateWrsWorkItemPropsSchema } from '../domain/domain.schema'
import { ItemResponse, PagingResponse, ListResponse, SuccessResponseSchema } from '@/module/common/schema'
import { jwtAuthMiddleware } from '@/middleware/auth';
import { openApiTag } from '@/middleware/openapi';
import { WorkItemListQuerySchema, MyWorkItemListQuerySchema, MyOrgWorkItemListQuerySchema } from '../domain/query.schema';
import { checkPermission } from '@/middleware/checkPermission';
import { JwtPayload } from '@/module/auth/auth.schema';

export function createWrsWorkItemRouter(
    wrsWorkItemService: WrsWorkItemService,
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const openApiMiddleware = openApiTag("WrsWorkItem");

    /**
     * TODO:
     * GET `/v0/wrs/work-items`：查詢工作項目列表
     * POST `/v0/wrs/work-items`：新增工作項目
     * GET `/v0/wrs/work-items/{id}`：查詢單一工作項目
     * PATCH `/v0/wrs/work-items/{id}`：更新工作項目（草稿）
     * POST `/v0/wrs/work-items/{id}/update-version`：提交後異動，產生新版本
     */

    // v 個人視角, 查詢摘要列表
    router.get('/my-work-item',
        openApiMiddleware({
            description: "個人視角, 查詢個人摘要列表",
            responsesSchema: ListResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:read:self`),
        validator("query", MyWorkItemListQuerySchema),
        async (c: Context) => {
            const body = await c.req.query();
            const user = c.get('user') as JwtPayload;
            const workItems = await wrsWorkItemService.listMyReport(user, body);
            return c.json({
                success: true,
                total: workItems.total,
                data: workItems.data.map(item => item.getProps())
            });
        }
    );

    // v 個人視角, 複製上週工作項目
    router.post('/copy-last-submitted',
        openApiMiddleware({
            description: "個人視角, 複製前一次有提交至部門的摘要列表至本週並改為草稿狀態, 呼叫時機為當呼叫了 /my-work-item 且本週摘要列表為空時",
            responsesSchema: ListResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:create:self`),
        async (c: Context) => {
            const user = c.get('user') as JwtPayload;
            const result = await wrsWorkItemService.copyLastWeekWorkItems(user);
            return c.json({ 
                success: true, 
                total: result.length,
                data: result.map(item => item.getProps())
            });
        }
    );

    // v 部門視角, 查詢部門中單一成員摘要列表
    router.get('/my-org-work-item',
        openApiMiddleware({
            description: "部門視角, 查詢自己部門的工作項目列表",
            responsesSchema: ListResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:read:department`),
        validator("query", MyOrgWorkItemListQuerySchema),
        async (c: Context) => {
            const body = await c.req.query();
            const user = c.get('user') as JwtPayload;
            const workItems = await wrsWorkItemService.pagingMyOrgReport(user, body);
            return c.json({
                success: true,
                total: workItems.total,
                data: workItems.data.map(item => item.getProps())
            });
        }
    );

    // v 個人視角, 新增摘要
    // POST `/v0/wrs/work-items`：新增工作項目
    router.post('/',
        openApiMiddleware({
            description: "個人視角, 新增摘要",
            responsesSchema: ItemResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:create:self`),
        validator("json", CreateWrsWorkItemPropsSchema),
        async (c: Context) => {
            const body = await c.req.json();
            const user = c.get('user') as JwtPayload;
            const workItem = await wrsWorkItemService.create(user,body);
            return c.json({ success: true, data: workItem.getProps() });
        }
    );

    // v 個人視角, 查詢id={id}的摘要
    // GET `/v0/wrs/work-items/{id}`：查詢單一工作項目
    router.get('/:id',
        openApiMiddleware({
            description: "個人視角, 查詢id={id}的摘要",
            responsesSchema: ItemResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission([`work-item:read:self`, `work-item:read:department`, `work-item:read:all`]),
        async (c: Context) => {
            const id = c.req.param('id');
            const user = c.get('user');
            const workItem = await wrsWorkItemService.getById(id, user.id);
            return c.json({ success: true, data: workItem.getProps() });
        }
    );

    // v 個人視角, 編輯摘要
    // PATCH `/v0/wrs/work-items/{id}`：更新工作項目（草稿）
    router.patch('/:id',
        openApiMiddleware({
            description: "個人視角, 編輯摘要",
            responsesSchema: ItemResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:update:self`),
        validator("json", CreateWrsWorkItemPropsSchema.partial()),
        async (c: Context) => {
            const id = c.req.param('id');
            const body = await c.req.json();
            const user = c.get('user');
            const workItem = await wrsWorkItemService.updateDraft(id, user.id, body);
            return c.json({ success: true, data: workItem.getProps() });
        }
    );

    // v 個人視角, 刪除摘要
    // DELETE `/v0/wrs/work-items/{id}`：刪除工作項目
    router.delete('/:id',
        openApiMiddleware({
            description: "個人視角, 刪除摘要",
            responsesSchema: ItemResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:update:self`),
        async (c: Context) => {
            const id = c.req.param('id');
            const user = c.get('user');
            const workItem = await wrsWorkItemService.delete(id, user.id);
            return c.json({ success: true, data: workItem.getProps() });
        }
    );

    // v 個人視角, 送出當週摘要
    router.post('/submit',
        openApiMiddleware({
            description: "個人視角, 送出當週摘要",
            responsesSchema: SuccessResponseSchema
        }),
        checkPermission(`employee-report:submit:self`),
        async (c: Context) => {
            const id = c.req.param('id');
            const user = c.get('user') as JwtPayload;
            await wrsWorkItemService.submit(user);
            return c.json({ success: true });
        }
    );

    // POST `/v0/wrs/work-items/{id}/update-version`：提交後異動，產生新版本
    router.post('/:id/update-version',
        openApiMiddleware({
            description: "部門視角, 更新已經被提交到部門(submitted)的摘要，產生新版本",
            responsesSchema: ItemResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:update:department`),
        validator("json", WrsWorkItemPropsSchema.partial()),
        async (c: Context) => {
            const id = c.req.param('id');
            const body = await c.req.json();
            const user = c.get('user')
            const workItem = await wrsWorkItemService.updateVersion(id, user.id, body);
            return c.json({ success: true, data: workItem.getProps() });
        }
    );

    // GET `/v0/wrs/work-items`：查詢工作項目列表
    router.get('/',
        openApiMiddleware({
            description: "HR/執行長視角, 查詢指定部門摘要列表或指定種類(專案)摘要列表",
            responsesSchema: PagingResponse(WrsWorkItemPropsSchema)
        }),
        checkPermission(`work-item:read:all`),
        validator("query", WorkItemListQuerySchema),
        async (c: Context) => {
            const body = await c.req.query();
            const workItems = await wrsWorkItemService.list(body);
            return c.json({
                success: true,
                total: workItems.total,
                data: workItems.data.map(item => item.getProps())
            });
        }
    );

    return router
}