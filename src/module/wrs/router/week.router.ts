import { Hono } from 'hono'
import { WrsWeekService } from '../app/WrsWeek.service'
import { WrsWeekPropsSchema } from '../domain/domain.schema'
import { Context } from 'hono'
import { validator } from "hono-openapi/zod";
import { ItemResponse, PagingResponse } from '@/module/common/schema'
import { jwtAuthMiddleware } from '@/middleware/auth';
import { openApiTag } from '@/middleware/openapi';
import { WeekPaginationQuerySchema } from '../domain/query.schema'

export function createWrsWeekRouter(
    weekService: WrsWeekService,
) {
    const router = new Hono()
    router.use('*', jwtAuthMiddleware)
    const openApiMiddleware = openApiTag("WrsWeek");

    /**
     * GET /v0/wrs/weeks：查詢週期列表
     */
    router.get('/',
        openApiMiddleware({
            description: "查詢週期列表",
            responsesSchema: PagingResponse(WrsWeekPropsSchema)
        }),
        validator('query', WeekPaginationQuerySchema),
        async (c: Context) => {
            const query =c.req.query();
                        const user = c.get('user');

            const weeks = await weekService.paging(query);
            return c.json({
                success: true,
                total: weeks.total,
                data: weeks.data.map(item => item.getProps())
            });
        }
    );
    
    /**
     * GET /v0/wrs/weeks/{id}：查詢單一週期
     */
    router.get('/:id',
        openApiMiddleware({
            description: "查詢單一週期",
            responsesSchema: ItemResponse(WrsWeekPropsSchema)
        }),
        async (c: Context) => {
            const id = c.req.param('id')
            const week = await weekService.getById(id);
            return c.json({ success: true, data: week.getProps() });
        }
    );

    return router
}