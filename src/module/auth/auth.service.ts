import { sign } from 'hono/jwt'
import { AuthRepository } from './auth.repository'
import { env } from '../../env'
import bcrypt from 'bcryptjs'
import { getPrisma } from '../../infra/db'
import type { z } from 'zod'
import {
  EditProfile,
  LocalRegisterRequestSchema,
  LoginResponse,
  MeResponse,
} from './auth.schema'
import { AuthError } from '../../errors/app.error'
import { AuthAzaureRepository } from './azaure.repository'
import { Prisma } from '@prisma/client'

type EmployeeStatus = 'active' | 'inactive'
type Role = 'employee' | 'admin'

export interface Employee {
  id: string
  username: string
  password?: string
  name: string
  email: string
  phone: string
  departmentId: string
  positionIds: string[]
  status: EmployeeStatus
  roles: Role[]
  createdAt: string
  updatedAt: string
}

export class AuthService {

  private jwtSecret: string

  constructor(
    private authRepo: AuthRepository,
    private azaureRepo: AuthAzaureRepository,
  ) {
    this.jwtSecret = env.JWT_SECRET
  }

  async authAzure(callbackCode: string): Promise<LoginResponse> {
    const accessToken = await this.azaureRepo.getTokenByCode(callbackCode)
    const account = await this.azaureRepo.getMe(accessToken)
    const prisma = getPrisma()
    const employee = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      const employee = await this.azaureRepo.updateEmployeeFromAzaureInfo(account, tx)
      if (!employee) {
        throw new AuthError('USER_NOT_FOUND', '找不到使用者, 非員工不得登入')
      }
      await this.authRepo.upsertLoginAccountFromAzaureInfo(employee.id, account, tx)
      return employee
    })
    const token = await this.signJwt(employee.id)

    return {
      token,
      user: this.toMe(employee)
    }
  }

  async register(
    body: z.infer<typeof LocalRegisterRequestSchema>
  ): Promise<LoginResponse> {
    const exists = await this.authRepo.findLoginAccountByUsername(body.username)
    if (exists) {
      throw new AuthError('USER_EXISTS', '使用者已存在')
    }

    const employeeId = await this.authRepo.findEmployeeByEmail(body.email)
    if (!employeeId) {
      throw new AuthError('USER_NOT_FOUND', '找不到使用者, 非員工不得登入')
    }

    const hashedPassword = await bcrypt.hash(body.password, 10)
    await this.authRepo.createLoginAccountFromLocalInfo(employeeId, {
      username: body.username,
      password: hashedPassword,
      nick_name: body.nick_name,
      email: body.email,
    })
    const loginResponse = await this.login(body.username, body.password)
    return loginResponse
  }

  async login(
    username: string,
    password: string
  ): Promise<LoginResponse> {
    const account = await this.authRepo.findLoginAccountByUsername(username)
    const isValid = account && account.password && await bcrypt.compare(password, account.password)
    if (!isValid || !account.employee) {
      throw new AuthError('INVALID_CREDENTIALS', '帳號或密碼錯誤')
    }
    const employee = account.employee
    const token = await this.signJwt(employee.id)

    return {
      token,
      user: this.toMe(employee),
    }
  }

  async getMeById(id: string): Promise<MeResponse> {

    const employee = await this.authRepo.findEmployeeById(id)
    if (!employee) {
      throw new AuthError('USER_NOT_FOUND', '找不到使用者')
    }
    return this.toMe(employee)
  }

  public async editProfile(id: string,data:EditProfile):Promise<MeResponse>{
    const employee=await this.authRepo.updateProfile(id,data)
    return this.toMe(employee)
  }

  private async signJwt(empId: string): Promise<string> {
    const payload=await this.authRepo.getJwtPayload(empId)
    return await sign(
      {
        ...payload,
        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24
      },
      this.jwtSecret
    )
  }

  private toMe(employee:any):MeResponse{
    return {
      id: employee.id,
      nick_name: employee.nick_name,
      email: employee.email,
      status: employee.status,
      language:employee.language
    }
  }
}
