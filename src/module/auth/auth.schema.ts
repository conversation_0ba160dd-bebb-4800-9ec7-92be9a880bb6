import { z } from 'zod'
import { extendZod<PERSON>ith<PERSON>penApi } from 'zod-openapi';

extendZodWithOpenApi(z);

export const LocalRegisterRequestSchema = z.object({
  username: z.string().min(1).openapi({ example: 'mike' }),
  password: z.string().min(1).openapi({ example: 'mike' }),
  nick_name: z.string().min(1).openapi({ example: 'Mike' }),
  email: z.string().email().openapi({ example: '<EMAIL>' }),
})
export type LocalRegisterRequest = z.infer<typeof LocalRegisterRequestSchema>

export interface AuthAzaureMeDto {
  id: string
  display_name: string,
  mail: string,
  surname: string,
  given_name: string,
}
export const AuthAzaureCallbackSchema = z.object({
  callbackCode: z.string().min(1).openapi({ example: 'auth-code-123' }),
})

export const LocalLoginRequestSchema = z.object({
  username: z.string().min(1).openapi({ example: 'mike' }),
  password: z.string().min(1).openapi({ example: 'mike' }),
})
export type LocalLoginRequest = z.infer<typeof LocalLoginRequestSchema>

export const MeResponseSchema = z.object({
  id: z.string().openapi({ example: 'user-123' }),
  nick_name: z.string().openapi({ example: 'Mike' }),
  email: z.string().openapi({ example: '<EMAIL>' }),
  status: z.enum(['active', 'left']).openapi({ example: 'active' }),
  language: z.string().openapi({ example: 'zh-TW' }),
})
export type MeResponse = z.infer<typeof MeResponseSchema>

export const LoginResponseSchema = z.object({
  token: z.string().openapi({ example: 'jwt-token-xyz' }),
  user: MeResponseSchema,
})
export type LoginResponse = z.infer<typeof LoginResponseSchema>


export const EditProfileSchema = z.object({
  title: z.string().optional().openapi({ example: '經理' }),
  imageUrl: z.string().optional().openapi({ example: 'https://www.full-chain.com/wp-content/uploads/2023/02/FullChain-LOGO-cht.svg' }),
  language: z.string().optional().openapi({ example: 'zh-TW' }),
})
export type EditProfile = z.infer<typeof EditProfileSchema>


export enum EmployeeRole {
  manager = 'manager',       //主管
  member = 'member',        //組員
  intern = 'intern',        //實習
  contractor = 'contractor',    //約聘
}

export enum AssignmentType {
  primary = 'primary',     //主要
  concurrent = 'concurrent',  //兼任
  acting = 'acting',      //代理
}
export enum EmployeeStatus {
  active = 'active',       // 啟用
  inactive = 'inactive',   // 禁用(e.g. 長假)
  left = 'left',           // 離職
}

export enum OrganizationType {
  company = 'company',        // 公司
  division = 'division',      // 處
  department = 'department',  // 部門
  office = 'office',          // 辦公室
}

export const EmployeeRoleSchema = z.nativeEnum(EmployeeRole)
  .describe('職務角色性質, manager=主管, member=組員, intern=實習, contractor=約聘')
  .openapi({ example: EmployeeRole.manager });

export const AssignmentTypeSchema = z.nativeEnum(AssignmentType)
  .describe('職務身分類別, primary=主要, concurrent=兼任, acting=代理')
  .openapi({ example: AssignmentType.primary });

const EmployeeStatusSchema = z.nativeEnum(EmployeeStatus)
  .describe('員工狀態, active=啟用,inactive=禁用(e.g. 長假),left=離職')
  .openapi({ example: EmployeeStatus.active });

export const OrganizationTypeSchema = z.nativeEnum(OrganizationType)
  .describe('部門類別, company=公司,division=處,department=部門,office=辦公室')
  .openapi({ example: OrganizationType.company });

export const JwtPayloadSchema = z.object({
  id: z.string().optional().openapi({ example: 'FC0065' }),
  name: z.string().optional().openapi({ example: 'Mike' }),
  language: z.string().optional().openapi({ example: 'en-US' }),
  timezone: z.string().optional().openapi({ example: 'Asia/Taipei' }),
  assignments: z.array(z.object({
    orgId: z.string().openapi({ example: 'o_department_information' }),
    orgName: z.string().openapi({ example: '資訊部' }),
    orgType: OrganizationTypeSchema,
    role: EmployeeRoleSchema,
    assignmentType: AssignmentTypeSchema,
  }))
})
export type JwtPayload=z.infer<typeof JwtPayloadSchema>
