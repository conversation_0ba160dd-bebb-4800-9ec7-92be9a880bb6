import { Hono, Context } from 'hono'
import {
  LocalRegisterRequestSchema,
  LocalLoginRequestSchema,
  LoginResponseSchema,
  MeResponseSchema,
  AuthAzaureCallbackSchema,
  EditProfileSchema,
} from './auth.schema'
import { validator } from 'hono-openapi/zod'
import { jwtAuthMiddleware } from '../../middleware/auth'
import { AuthError } from '../../errors/app.error'
import { AuthService } from './auth.service'
import { openApiTag } from '@/middleware/openapi'


export function createAuthRouter(
  service: AuthService
) {
  const router = new Hono()
  const openApiMiddleware = openApiTag("Auth", []);

  router.post('/register',
    openApiMiddleware({
      description: "Register",
      responsesSchema: LoginResponseSchema
    }),
    validator("json", LocalRegisterRequestSchema),
    async (c: Context) => {
      const body = await c.req.json()
      const result = await service.register(body)
      return c.json(result)
    })

  router.post('/login',
    openApiMiddleware({
      description: "Login",
      responsesSchema: LoginResponseSchema
    }),
    validator('json', LocalLoginRequestSchema),
    async (c: Context) => {
      const { username, password } = await c.req.json()
      const result = await service.login(username, password)
      return c.json(result)
    }
  )

  router.post('/azure',
    openApiMiddleware({
      description: "Auth Azure",
      responsesSchema: LoginResponseSchema
    }),
    validator('json', AuthAzaureCallbackSchema),
    async (c: Context) => {
      const { callbackCode } = await c.req.json()
      const result = await service.authAzure(callbackCode)
      return c.json(result)
    })

  router.get('/me',
    jwtAuthMiddleware,
    openApiMiddleware({
      description: "Get current user",
      responsesSchema: MeResponseSchema,
      authKey: ['JwtKeyAuth']
    }),
    async (c: Context) => {
      const user = c.get('user')
      if (!user || !user.id) {
        throw new AuthError('INVALID_TOKEN', 'Invalid JWT token', 401)
      }
      const result = await service.getMeById(user.id)
      return c.json(result)
    }
  )

  router.patch('/edit',
    jwtAuthMiddleware,
    openApiMiddleware({
      description: "Edit Profile",
      responsesSchema: MeResponseSchema,
      authKey: ['JwtKeyAuth']
    }),
    validator('json', EditProfileSchema),
    async (c: Context) => {
      const user = c.get('user')
      if (!user || !user.id) {
        throw new AuthError('INVALID_TOKEN', 'Invalid JWT token', 401)
      }
      const data = await c.req.json()
      const result = await service.editProfile(user.id, data)
      return c.json(result)
    }
  )
  return router
}
