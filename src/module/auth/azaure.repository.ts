import { env } from "@/env";
import axios from "axios";
import { AuthAzaureMeDto, MeResponse } from "./auth.schema";
import { AuthError } from "@/errors/app.error";
import { Prisma, PrismaClient } from "@prisma/client";
import { CatchRepositoryError } from "@/utils/repoDecorator";

export interface AuthAzaureMeResponse {
    id: string
    displayName: string,
    mail: string,
    surname: string,
    givenName: string,
    "@odata.context": string,
    businessPhones: string[],
    jobTitle: string | null,
    mobilePhone: string | null,
    officeLocation: string | null,
    preferredLanguage: string | null,
    userPrincipalName: string,
}

export class AuthAzaureRepository {
    constructor(private prisma: PrismaClient) { }
    async getTokenByCode(code: string): Promise<string> {
        try{
            const result = await axios({
                url: `https://login.microsoftonline.com/${env.TENANT_ID}/oauth2/v2.0/token`,
                method: 'POST',
                headers: {
                    'content-type': 'application/x-www-form-urlencoded',
                },
                data: new URLSearchParams({
                    client_id: env.CLIENT_ID,
                    client_secret: env.CLIENT_SECRET,
                    code: code,
                    redirect_uri: env.CALLBACK_URL,
                    grant_type: 'authorization_code'
                } as Record<string, string>).toString()
            })
            return result.data.access_token
        }catch(err){
            if(axios.isAxiosError(err)){
                throw new AuthError('INVALID_CALLBACK_CODE', err.response?.data.error_description, 401, err)
            }
            throw err
        }
    }

    async getMe(accessToken: string): Promise<AuthAzaureMeDto> {
        const { data } = await axios({
            url: 'https://graph.microsoft.com/v1.0/me',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
        })
        return this.toDto(data)
    }

    @CatchRepositoryError()
    async updateEmployeeFromAzaureInfo(data: AuthAzaureMeDto, tx?: Prisma.TransactionClient) {
        const db = tx ?? this.prisma
        const employeeData=await db.employee.update({
          where: { email: data.mail },
          data: {
            first_name: data.surname,
            last_name: data.given_name,
            email: data.mail,
            status: 'active'
          }
        })
        return employeeData
    } 

    private toDto(raw: AuthAzaureMeResponse): AuthAzaureMeDto {
        return {
            display_name: raw.displayName,
            given_name: raw.givenName,
            mail: raw.mail,
            surname: raw.surname,
            id: raw.id
        }
    }
}