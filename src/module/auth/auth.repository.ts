import type { PrismaClient, Prisma } from '@prisma/client'
import { AuthAzaureMeDto, EditProfile, JwtPayload } from './auth.schema'
import { LocalRegisterRequest } from './auth.schema'
import { CatchRepositoryError } from '@/utils/repoDecorator'

export class AuthRepository {
  constructor(private readonly prisma: PrismaClient) { }
  @CatchRepositoryError()
  async upsertLoginAccountFromAzaureInfo(
    employee_id: string,
    account: AuthAzaureMeDto,
    tx?: Prisma.TransactionClient
  ): Promise<void> {
    const db = tx ?? this.prisma
    const provider = 'azure'
    const id = this.genId(employee_id, provider)

    await db.login_account.upsert({
      where: {
        id: id,
        provider: provider,
        external_id: account.id
      },
      create: {
        id: id,
        provider: provider,
        username: account.mail,
        external_id: account.id,
        employee_id: employee_id
      },
      update: {
        provider: provider,
        username: account.mail,
        external_id: account.id,
      },
    })
  }
  @CatchRepositoryError()
  async createLoginAccountFromLocalInfo(
    employee_id: string,
    account: LocalRegisterRequest,
    tx?: Prisma.TransactionClient
  ): Promise<void> {
    const db = tx ?? this.prisma
    const provider = 'local'
    const id = this.genId(employee_id, provider)
    await db.login_account.create({
      data: {
        id: id,
        provider: provider,
        username: account.username,
        password: account.password,
        employee_id: employee_id
      }
    })
  }
  @CatchRepositoryError()
  async findLoginAccountByExternalId(id: string, username: string, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    return await db.login_account.findFirst({
      where: {
        provider: 'azure',
        username: username,
        external_id: id
      },
      include: {
        employee: {
          include: {
            assignments: true,
          },
        },
      },
    })
  }

  @CatchRepositoryError()
  async findLoginAccountByUsername(username: string, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    return await db.login_account.findFirst({
      where: {
        provider: 'local',
        username: username,
      },
      include: {
        employee: true,
      },
    })
  }

  @CatchRepositoryError()
  async createLoginAccount(data: any, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    return await db.login_account.create({ data })
  }

  @CatchRepositoryError()
  async createEmployeeDepartmentPosition(data: any, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    return await db.login_account.create({ data })
  }

  @CatchRepositoryError()
  async findEmployeeById(id: string, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    const where = {
      where: { id: id }
    }
    return await db.employee.findUnique(where)
  }

  private genId(employee_id: string, provider: string): string {
    return `login_${employee_id}_${provider}`
  }

  @CatchRepositoryError()
  async findEmployeeByEmail(email: string, tx?: Prisma.TransactionClient): Promise<string | null> {
    const db = tx ?? this.prisma
    const employeeData = await db.employee.findUnique({
      where: { email: email },
    })
    return employeeData ? employeeData.id : null
  }

  @CatchRepositoryError()
  async updateProfile(id: string, data: EditProfile, tx?: Prisma.TransactionClient) {
    const db = tx ?? this.prisma
    const { title, imageUrl, language } = data
    const raw = {
      title, language,
      image_url: imageUrl ? imageUrl : undefined
    }
    const result = await db.employee.update({
      where: { id },
      data: raw
    });
    return result
  }

  @CatchRepositoryError()
  async getJwtPayload(empId: string, tx?: Prisma.TransactionClient): Promise<JwtPayload> {
    const db = tx ?? this.prisma
    const assignments = await db.employee_assignment.findMany({
      where: {
        employee_id: empId,
        end_date: null
      },
      include: {
        employee: true,
        organization: true
      },
    })
    console.log(assignments)
    return this.toJwtPayload(assignments)
  }

  toJwtPayload(assignments: any[]): JwtPayload {
    let primaryAssigment = assignments.filter(assign => assign.assignment_type === 'primary')[0]

    if (assignments.length <= 0)
      throw new Error('xxx')

    return {
      id: primaryAssigment.employee.id,
      name: primaryAssigment.employee.nick_name,
      language: primaryAssigment.employee.language,
      timezone: primaryAssigment.employee.timezone,
      assignments: assignments.map((assign) => ({
        orgId: assign.organization.id,
        orgName: assign.organization.org_name,
        orgType: assign.organization.type,
        role: assign.role,
        assignmentType: assign.assignment_type
      }))
    }
  }
}