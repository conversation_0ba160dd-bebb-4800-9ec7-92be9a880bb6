import { getPrisma } from "@/infra/db";
import { AuthRepository } from "./auth.repository";
import { AuthAzaureRepository } from "./azaure.repository";
import { AuthService } from "./auth.service";
import { createAuthRouter } from "./auth.router";
import { Hono } from "hono";
import { env } from "@/env";

export function authModule() {
    // repo
    const prisma = getPrisma()
    // auth
    const authRepository = new AuthRepository(prisma)
    const authAzaureRepository = new AuthAzaureRepository(prisma)
    const authService = new AuthService(authRepository, authAzaureRepository)
    // router
    const authRouter = createAuthRouter(authService)

    // Import the health router from the health module
    const router = new Hono()
    const moduleName = 'auth';
    router.route(`/`, authRouter)
    
    console.log(`* [Module] Auth module initialized 
    - ${env.HOST_URL}/v0/${moduleName}`);

    return router
}