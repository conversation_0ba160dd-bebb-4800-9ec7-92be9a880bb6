import { z } from "zod";
import { extendZodWith<PERSON>penApi } from "@hono/zod-openapi";

extendZodWithOpenApi(z); // ⬅️ 必須呼叫一次！
export const SuccessResponseSchema = z.object({
  success: z.preprocess(
    (val) => {
      if (typeof val === 'boolean') return val;
      if (val === 'true') return true;
      if (val === 'false') return false;
      return val;
    },
    z.boolean(),
  ).openapi({ example: true }),
});
export const successBase = <T extends z.ZodTypeAny>(itemSchema: T) => {
  return SuccessResponseSchema.merge(z.object({ data: itemSchema }))
}

export const createListResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) => {
  return SuccessResponseSchema.merge(z.object({
    total: z.number().openapi({ example: 10 }),
    data: z.array(itemSchema),
  }));
};
export const createImportResultResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) => {
  return SuccessResponseSchema.merge(z.object({
    data: z.array(itemSchema),
    errors: z.array(z.string()).openapi({ example: ["錯誤1", "錯誤2"] }),
  }));
};

export const ErrorResponseSchema = z.object({
  success: SuccessResponseSchema.merge(z.object({
    message: z.string().openapi({ example: "發生錯誤" })
  })),
});


export const SuccessResponse = SuccessResponseSchema
export const ItemResponse = successBase
export const ListResponse = createListResponseSchema
export const ImportResultResponse = createImportResultResponseSchema
export const ErrorResponse = ErrorResponseSchema

export const PagingResponse = <T extends z.ZodTypeAny>(itemSchema: T) => {
  return SuccessResponseSchema.merge(z.object({
    total: z.number().openapi({ example: 10 }),
    skip: z.number().int().min(0).default(0).openapi({
      example: 0,
      description: '跳過幾筆，分頁用'
    }),
    take: z.number().int().min(1).max(100).default(20).openapi({
      example: 20,
      description: '取幾筆，分頁用，最大100'
    }),
    data: z.array(itemSchema),
  }));
};