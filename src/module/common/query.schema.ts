import { z } from "zod";
import { extendZodWithOpenApi } from "@hono/zod-openapi";
/**
 * 通用分頁查詢參數
 * - skip: 跳過幾筆 (預設0)
 * - take: 取幾筆 (預設20)
 */
export const PaginationQuerySchema = z.object({
  skip: z.preprocess(
    (val) => (val === undefined ? 0 : Number(val)),
    z.number().int().min(0).default(0)
      .describe('跳過幾筆，分頁用').openapi({ example: 0 })
  ).optional().transform(v => v === undefined ? 0 : v),
  take: z.preprocess(
    (val) => (val === undefined ? 20 : Number(val)),
    z.number().int().min(1).max(100).default(20)
      .describe('取幾筆，分頁用，最大100').openapi({ example: 20 })
  ).optional().transform(v => v === undefined ? 20 : v),
});

export const PaginationListQuery = <T extends z.AnyZodObject>(queryDomainSchema: T) => {
  return PaginationQuerySchema.merge(queryDomainSchema.partial());
};
export const ListQuery= <T extends z.AnyZodObject>(queryDomainSchema: T) => {
  return queryDomainSchema.partial();
};