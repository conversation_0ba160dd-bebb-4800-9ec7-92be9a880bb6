import { nanoid } from "nanoid";

export const genDocSpecId = (): string => `ds_${nanoid()}`
export const genDocSpecFormId = (): string => `dsdf_${nanoid()}`
export const genDocSpecFlowId = (): string => `dsdfw_${nanoid()}`
export const genTaskSignId = (): string => `tss_${nanoid()}`
export const genTaskCommentId = (): string => `tsc_${nanoid()}`
export const genDocHistoryId = (): string => `dh_${nanoid()}`
export const genAssignmentId = (orgId:string,empId:string): string => `as_${orgId}_${empId}`
export const genOrgId = (): string => `o_${nanoid()}`