import { DuplicateKeyError, NotFoundError, RepositoryError } from "@/errors/repo.error"
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

export function CatchRepositoryError(): MethodDecorator {
  return (_target, _propertyKey, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args)
      } catch (err: any) {
        console.error('Repository error:', err)
        if (err instanceof PrismaClientKnownRequestError && err.code && err.meta) {
          switch (err.code) {
            case 'P2002':
              throw new DuplicateKeyError('Duplicate key', err.meta?.cause as string)
            case 'P2025':
              throw new NotFoundError('Not found', err.meta?.cause as string )
            default:
              throw new RepositoryError('Repository method failed', err.meta?.cause as string)
          }
        }else{
          throw err
        }
      }
    }
  }
}