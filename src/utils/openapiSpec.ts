import { env } from '@/env'
import { Hono } from 'hono'
import { openAPISpecs } from 'hono-openapi'

export function swaggerSpec(router:Hono) {
    return openAPISpecs(router, {
        documentation: {
            info: {
                title: 'Hono API',
                version: '1.0.0',
                description: 'Greeting API',
            },
            servers: [
                { url: env.HOST_URL, description: 'Server' },
            ],
            security: [{ ApiKeyAuth: [] }, { JwtKeyAuth: [] }], // 👈 加這行
            components: {
                securitySchemes: {
                    JwtKeyAuth: {
                        type: 'http',
                        scheme: 'bearer',
                    },
                    ApiKeyAuth: {
                        type: 'apiKey',
                        in: 'header',
                        name: 'Authorization',
                        scheme: 'Apikey',
                    },
                },
            },
        },
    })
}