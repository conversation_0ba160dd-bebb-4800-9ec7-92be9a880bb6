import { env } from "@/env";
import { GoogleGenAI } from "@google/genai";

export class LlmService {
    private readonly llm: GoogleGenAI
    private readonly model: string
    constructor() {
        this.llm = new GoogleGenAI({ apiKey: env.LLM_API_KEY });
        this.model = env.LLM_MODEL
    }

    static instance: LlmService | null = null;
    static getInstance() {
        // 如果沒有被初始化過，就初始化一個
        if (LlmService.instance === null) {
            LlmService.instance = new LlmService();
        }
        return LlmService.instance;
    }

    async gen(systemPrompts: string, userInput: string, args?: any): Promise<string> {
        try {
            const response = await this.llm.models.generateContent({
                model: this.model,
                contents: userInput,
                config: {
                    systemInstruction: systemPrompts,
                    maxOutputTokens: args?.maxOutputTokens ?? 1000,
                    temperature: args?.temperature ?? 0.1,
                },
            });
            return response.text || '{}';
        } catch (error) {
            console.error('LLM generation error:', error);
            throw new Error('Failed to generate text from LLM');
        }
    }

    public async summaryReport(orgName: string, report: any): Promise<{ summary: string, highlight: string, plan: string }> {
        const systemPrompts = `
        你是一位經驗豐富的部門主管特助，你的任務是根據提供的 JSON 格式的週報資料，為我整理一份給高層看的部門週報摘要。
        如果沒有做事，務必在各個項目中回答 "無" ，不要生成其他資料。
        **你的職責:**
        1.  閱讀所有團隊成員提交的週報項目。
        2.  根據這些項目，撰寫一份清晰、專業、客觀的摘要。
        3.  嚴格按照指定的 JSON 格式輸出結果，不得有任何多餘的文字。

        **規則**
        1. 請依據「輸出格式」來回答。
        2. 如果會議記錄中沒有提到任何週報資料，請在「summary、highlight、plan」欄位中明確回答「無」。
        3. 不要編造任何記錄中未提及的資訊。

        **輸入資料格式說明:**
        你將會收到一個 JSON 字串，它是一個物件陣列，每個物件代表一項工作項目，包含以下欄位：
        - "name": 任務的標題。
        - "type": 任務的類型。工作項目類型，project=專案, routine=例行, support=支援, admin=行政, training=培訓, other=其他
        - "progress_percent": 任務的完成百分比。
        - "result": 本週完成工作的具體描述。
        - "suggestion": 遇到的困難或提出的建議。
        - "next_week_goal": 下一週的計畫。
        - "employee_title": 任務的實施者。

        **輸出要求:**
        請生成一個包含以下三個鍵(key)的 JSON 物件：
        1.  **summary (string)**: 部門週報總結。用大約 150-200 字總結本週部門整體的進展、完成的任務和關鍵成果。
        2.  **highlight (string)**: 本週重點。根據以下標準，列出 1-3 個最重要的成就，每個要點用換行符號"\\n"分隔，並以 "1. "、"2. " 開頭。
            - 優先選擇進度為 "100%" 或接近完成的重要專案。
            - 選擇對團隊或公司有顯著貢獻的項目（例如：完成UAT、取得認證、解決關鍵阻礙）。
        3.  **plan (string)**: 下週計劃。根據所有項目的 "下週目標" 欄位，總結出 1-3 個部門下週的核心工作計劃。每個要點同樣用換行符號"\\n"分隔，並以 "1. "、"2. " 開頭。

        **輸出格式範例:**
        {
            "summary": "本週研發部在BPM系統開發上取得重大進展，已完成UAT測試並準備上線。同時，API整合與資料支援工作也穩定推進。團隊成員王小傑成功取得AWS架構師認證，提升了團隊的雲端技術能力。",
            "highlight": "1. BPM系統開發V1版完成UAT測試，即將進入上線階段。\n2. 團隊成員完成AWS架構師進階訓練並取得認證。",
            "plan": "1. 完成BPM系統的正式環境部署與上線。\n2. 繼續API的全面整合測試。\n3. 完成剩餘的業務部資料清洗工作。"
        }

        **重要提醒：你的整個回覆必須是一個單一且格式完全正確的 JSON 物件。我會直接使用 JSON.parse() 解析，所以不要包含任何 JSON 以外的文字、註解或 Markdown 標記。**
        `;
        const userInput = `部門名稱: ${orgName}\n週報資料: ${report??'無'}`;
        try {
            const llm = LlmService.getInstance()
            console.log({ userInput })
            const summaryResult = await llm.gen(systemPrompts, userInput);
            const cleanedJsonString = this.outputClean(summaryResult)
            const parsedSummary = JSON.parse(cleanedJsonString);
            console.log({ parsedSummary })

            return {
                summary: parsedSummary.summary,
                highlight: parsedSummary.highlight,
                plan: parsedSummary.plan
            };
        }
        catch (error) {
            console.error('AI Summary Error:', error);
            throw new Error('AI Summary failed, please try again later.');
        }
    }

    private outputClean(rawOutput: string): string {
        // 使用正規表示式尋找被 ```json ... ``` 或 ``` ... ``` 包裹的內容
        // [\s\S]* 是一個技巧，可以匹配包含換行符在內的任何字元
        const jsonMatch = rawOutput.match(/\{[\s\S]*\}/);

        if (!jsonMatch) {
            console.error("Failed to find valid JSON in LLM output:", rawOutput);
            throw new Error('AI did not return a valid JSON object.');
        }

        const cleanedJsonString = jsonMatch[0]; // 取得匹配到的純 JSON 字串
        // --- 清理步驟結束 ---
        return cleanedJsonString
    }
}