import { LlmService } from "../llm"

async function main() {
    const instance = LlmService.getInstance()
    const result=await instance.summaryReport('研發部',
        JSON.stringify([
            {
                name: "BPM系統開發V1版完成UAT",
                progress_percent: "85%",
                type: "專案",
                result: "完成UAT測試，修正所有回報的問題，準備下週上線。",
                suggestion: "測試環境與正式環境設定不同，需要調整部署流程。",
                next_week_goal: "完成正式環境部署，進行上線前最後確認。",
                employee_title: "陳小明"
            },
            {
                name: "API整合開發",
                progress_percent: "60%",
                type: "專案",
                result: "完成主要API端點開發，進行初步整合測試。",
                suggestion: "第三方API文檔不完整，需要更多溝通。",
                next_week_goal: "完成剩餘API開發，進行全面整合測試。",
                employee_title: "陳小明"
            },
            {
                name: "每日系統備份檢查",
                progress_percent: "100%",
                type: "例行",
                result: "完成所有系統的每日備份檢查，確認備份完整性。",
                suggestion: "無",
                next_week_goal: "持續進行每日備份檢查。",
                employee_title: "陳小明"
            },
            {
                name: "支援業務部資料整理",
                progress_percent: "50%",
                type: "支援",
                result: "協助業務部整理客戶資料，完成一半的資料清洗工作。",
                suggestion: "資料格式不一致，建議統一資料輸入格式。",
                next_week_goal: "完成剩餘資料清洗，並提供資料輸入範本。",
                employee_title: "林小華"
            },
            {
                name: "完成AWS架構師進階訓練",
                progress_percent: "100%",
                type: "培訓",
                result: "完成AWS架構師進階訓練課程，取得認證。",
                suggestion: "建議團隊其他成員也參加相關培訓，提升團隊整體雲端技能。",
                next_week_goal: "將學到的知識應用於現有專案，優化雲端架構。",
                employee_title: "王小傑"
            }
        ]))
    console.log(result)
}
main()