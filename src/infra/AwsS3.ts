import { S3<PERSON>lient, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { env } from "@/env";

export class AwsS3 {
  private s3: S3Client
  private bucket = `${env.S3_BUCKET_NAME}`

  constructor() {
    this.s3 = new S3Client({
      region: env.AWS_REGION,//us-east-2成本最小化
      credentials: {
        accessKeyId: env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: env.AWS_SECRET_ACCESS_KEY!,
      },
    })
  }

  public genS3key(docId: string, fileName: string): string {
    return `${docId}/${fileName}`
  }

  public genS3Prefix(docId: string): string {
    return `${docId}/`
  }

  public async generatePresignedUrl(key: string, fileType: string): Promise<string> {

    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      ContentType: fileType,
    })

    const url = await getSignedUrl(this.s3, command, { expiresIn: 60 })
    return url
  }


  public async delete(key: string): Promise<void> {
    try{
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      })
      await this.s3.send(command)
    }catch(err){
      console.error(err)
    }
   
  }

  public async list(prefix: string): Promise<{
    key: string | undefined;
    lastModified: Date | undefined;
    size: number | undefined;
  }[]> {
    const command = new ListObjectsV2Command({
      Bucket: this.bucket,
      Prefix: prefix,//'uploads/',
    })
    const result = await this.s3.send(command)
    const files = (result.Contents ?? []).map(obj => ({
      key: obj.Key,
      lastModified: obj.LastModified,
      size: obj.Size,
    }))
    return files
  }

  public async get(key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    })

    const url = await getSignedUrl(this.s3, command, { expiresIn: 60 })
    return url
  }
  /**
   * 
{
  "$metadata": {
    "httpStatusCode": 200,
    "requestId": "148TMZ2QWBNTT1EW",
    "extendedRequestId": "n0i+NB0Hn3q1CAstHiO96Q0qcpVGS33Wa/cP/me7G01fMXBAHMAKxL5/VncCRpCZehFXmhJVy8nEN6eHyEVgnQ==",
    "attempts": 1,
    "totalRetryDelay": 0
  },
  "Contents": [
    {
      "Key": "d_001/",
      "LastModified": "2025-05-04T17:39:48.000Z",
      "ETag": "\"d41d8cd98f00b204e9800998ecf8427e\"",
      "ChecksumAlgorithm": [
        "CRC64NVME"
      ],
      "ChecksumType": "FULL_OBJECT",
      "Size": 0,
      "StorageClass": "STANDARD"
    },
    {
      "Key": "d_001/487894.jpg",
      "LastModified": "2025-05-04T17:40:31.000Z",
      "ETag": "\"eb59a013669113d639ed03bda16ba9d8\"",
      "ChecksumAlgorithm": [
        "CRC64NVME"
      ],
      "ChecksumType": "FULL_OBJECT",
      "Size": 71310,
      "StorageClass": "STANDARD"
    },
    {
      "Key": "d_001/approval.json",
      "LastModified": "2025-05-05T00:12:38.000Z",
      "ETag": "\"acc6542ef2c3510b970fc8aa5107a902\"",
      "ChecksumAlgorithm": [
        "CRC64NVME"
      ],
      "ChecksumType": "FULL_OBJECT",
      "Size": 11340,
      "StorageClass": "STANDARD"
    }
  ],
  "IsTruncated": false,
  "KeyCount": 3,
  "MaxKeys": 1000,
  "Name": "bpm-files-development",
  "Prefix": "d_001/"
}
   */
}