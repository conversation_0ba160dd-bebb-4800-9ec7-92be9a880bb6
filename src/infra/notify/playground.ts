import { MicrosoftGraphAPI } from "./email"

async function main() {
  try {
    const api = new MicrosoftGraphAPI()
    const mail = await api.sendEmail({
      to: ['<EMAIL>'],
      subject: 'Test',
      content: 'Hello World'
    })
    console.log('done',mail.statusText)
  } catch (err) {

    console.log({ err })

    // if(err instanceof AxiosError)
    // AxiosError: Request failed with status code 403
  }
}
main()