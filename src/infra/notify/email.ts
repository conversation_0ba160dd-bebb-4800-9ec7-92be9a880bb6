import { env } from '@/env';
import { InternalError } from '@/errors/app.error';
import axios from 'axios';
import qs from 'qs';
export interface EmailProps {
  to: string[]
  subject: string
  content: string
}

/**
 * 新申請APP請給管理員同意
 * https://login.microsoftonline.com/{TENANT_ID}/adminconsent?client_id={CLIENT_ID}
 */
export class MicrosoftGraphAPI {
  private token = ''
  private sender = env.SENDER_EMAIL
  private maxRetries = 3

  constructor() {
    console.log(`MicrosoftGraphAPI: ${this.sender}`)
  }

  static instance: MicrosoftGraphAPI | null = null;
  static getInstance() {
    // 如果沒有被初始化過，就初始化一個
    if (MicrosoftGraphAPI.instance === null) {
      MicrosoftGraphAPI.instance = new MicrosoftGraphAPI();
    }
    return MicrosoftGraphAPI.instance;
  }

  private async getAccessToken() {
    const scope = 'https://graph.microsoft.com/.default';
    const response = await axios({
      url: `https://login.microsoftonline.com/${env.TENANT_ID}/oauth2/v2.0/token`,
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: qs.stringify({
        grant_type: 'client_credentials',
        client_id: env.CLIENT_ID,
        client_secret: env.CLIENT_SECRET,
        scope: scope,
      }),
    })
    return response.data.access_token;
  }

  public async sendEmail(props: EmailProps): Promise<any> {
    if (this.token === '') {
      this.token = await this.getAccessToken()
    }

    for (let i = 0; i < this.maxRetries; i++) {
      try {
        const response = await axios({
          url: `https://graph.microsoft.com/v1.0/users/${this.sender}/sendMail`,
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.token}`,
            'Content-Type': 'application/json',
          },
          data: {
            message: {
              subject: props.subject,
              body: {
                contentType: "Text",
                content: props.content,
              },
              toRecipients: this.toRecipients(props.to),
            },
          }
        })
        return response
      } catch (e) {
        console.log(e)
        this.token = await this.getAccessToken()
      }
    }
    throw new InternalError('Retry failed', 'send mail failed after max retries')
  }

  private toRecipients(to: string[]) {
    if (env.NODE_ENV === 'development') {
      console.log(`[DEV] Use ${env.SENDER_EMAIL} to simulate sending a letter to ${to}.`)
      return [{ emailAddress: { address: env.SENDER_EMAIL } }]
    } else {
      return to.map((to) => ({ emailAddress: { address: to } }))
    }
  }
}