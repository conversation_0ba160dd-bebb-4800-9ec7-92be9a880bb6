import { env } from '@/env'
import { Hono } from 'hono'
import { openAPISpecs } from 'hono-openapi'
// 選項介面現在需要知道模組的路徑前綴
export interface ModuleSwaggerOptions {
    title: string;
    description: string;
    version?: string;
    modulePath: string; // e.g., '/api/users'
}

// 通用模組的介面
export interface CommonModule {
    router: Hono;
    path: string; // e.g., '/api/auth'
}

export function createModuleSwagger(
    moduleRouter: Hono,
    options: ModuleSwaggerOptions,
    // 將通用模組作為可選參數傳入
    commonModules: CommonModule[] = []
) {
    // 關鍵：建立一個僅用於產生 spec 的臨時 Hono 實例
    const specRouter = new Hono();

    // 1. 將所有通用模組的路由註冊到臨時 router 上
    for (const common of commonModules) {
        specRouter.route(common.path, common.router);
    }

    // 2. 將當前模組的路由也註冊上去
    specRouter.route(options.modulePath, moduleRouter);

    // 3. 使用這個組合後的 `specRouter` 來產生 OpenAPI spec
    return openAPISpecs(specRouter, {
        documentation: {
            info: {
                title: options.title,
                version: options.version || '1.0.0',
                description: options.description,
            },
            servers: [
                { url: env.HOST_URL, description: 'Server' },
            ],
            security: [{ ApiKeyAuth: [] }, { JwtKeyAuth: [] }], // 👈 加這行
            components: {
                securitySchemes: {
                    JwtKeyAuth: {
                        type: 'http',
                        scheme: 'bearer',
                    },
                    ApiKeyAuth: {
                        type: 'apiKey',
                        in: 'header',
                        name: 'Authorization',
                        scheme: 'Apikey',
                    },
                },
            },
        },
    })
}