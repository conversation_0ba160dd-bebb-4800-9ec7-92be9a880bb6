import { env } from "@/env";
import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";

export interface EmailProps {
    to: string[]
    subject: string
    content: string
}

export class AwsSes {
    private sender = env.SENDER_EMAIL
    constructor() {
        console.log(`Aws Ses: ${this.sender}`)
    }

    static instance: AwsSes | null = null;
    static getInstance() {
        // 如果沒有被初始化過，就初始化一個
        if (AwsSes.instance === null) {
            AwsSes.instance = new AwsSes();
        }
        return AwsSes.instance;
    }

    private sesClient = new SESClient({
        region: "ap-northeast-1", // SES 支援的區域，如 sandbox 預設 us-east-1
        credentials: {
            accessKeyId: env.AWS_SES_ACCESS_KEY,
            secretAccessKey: env.AWS_SES_SECRET_KEY,
        },
    });

    public async sendEmail(props: EmailProps): Promise<void> {
        const to = this.toRecipients(props.to)
        const subject = props.subject
        const body = `<html><body>${props.content}</body></html>`

        const params = {
            Destination: {
                ToAddresses: to,
            },
            Message: {
                Body: {
                    Html: {
                        Charset: "UTF-8",
                        Data: body,
                    },
                },
                Subject: {
                    Charset: "UTF-8",
                    Data: subject,
                },
            },
            Source: this.sender
        };

        const command = new SendEmailCommand(params);
        await this.sesClient.send(command);
    }

    private toRecipients(to: string[]) {
        if (env.NODE_ENV === 'development') {
            console.log(`[DEV] Use ${env.SENDER_EMAIL} to simulate sending a letter to ${to}.`)
            return ['<EMAIL>']
        } else {
            return to.map((to) => to)
        }
    }
}
