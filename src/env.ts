import dotenv from 'dotenv';

dotenv.config();

function requireEnv(key: string): string {
  const value = process.env[key];
  if (!value) {
    console.error(`Missing required environment variable: ${key}`);
  }
  return value||'';
}

export const env = {
  PORT: Number(process.env.PORT) || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development',
  DATABASE_URL: requireEnv('DATABASE_URL'),
  JWT_SECRET: requireEnv('JWT_SECRET'),
  API_KEY: requireEnv('API_KEY'),
  VERSION:requireEnv('VERSION'),
  HOST_URL:requireEnv('HOST_URL'),
  FRONTEND_URL:requireEnv('FRONTEND_URL'),
  ENABLE_MAIL:requireEnv('ENABLE_MAIL')==='true'?true:false,
  // azure
  TENANT_ID:requireEnv('TENANT_ID'),
  CLIENT_ID:requireEnv('CLIENT_ID'),
  CLIENT_SECRET:requireEnv('CLIENT_SECRET'),
  CALLBACK_URL:requireEnv('CALLBACK_URL'),
  SENDER_EMAIL:requireEnv('SENDER_EMAIL'),
  //aws
  AWS_REGION:requireEnv('AWS_REGION'),
  AWS_ACCESS_KEY_ID:requireEnv('AWS_ACCESS_KEY_ID'),
  AWS_SECRET_ACCESS_KEY:requireEnv('AWS_SECRET_ACCESS_KEY'),
  S3_BUCKET_NAME:requireEnv('S3_BUCKET_NAME'),
  // llm
  LLM_API_KEY: requireEnv('LLM_API_KEY'),
  LLM_MODEL: requireEnv('LLM_MODEL'),
  
  AWS_SES_ACCESS_KEY:requireEnv('AWS_SES_ACCESS_KEY'),
  AWS_SES_SECRET_KEY:requireEnv('AWS_SES_SECRET_KEY'),
};

export type Env = typeof env;
