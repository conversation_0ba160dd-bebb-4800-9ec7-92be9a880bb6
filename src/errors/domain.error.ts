import { BaseError } from './base.error'
//(商業規則違反)
export class DomainError extends BaseError {
    constructor(code: string, message: string, status = 401, cause?: unknown) {
        super({ code, message, status, cause })
    }
}
export class BusinessRuleViolationError extends DomainError {
    constructor(code: string, message: string, status = 401, cause?: unknown) {
        super(code, message, status, cause)
    }
}