export type BaseErrorOptions = {
    code: string
    message: string
    status?: number
    cause?: unknown
}

export class BaseError extends Error {
    code: string
    status: number
    cause?: unknown

    constructor({ code, message, status = 400, cause }: BaseErrorOptions) {
        super(message)
        this.code = code
        this.status = status
        this.cause = cause
        Error.captureStackTrace(this, this.constructor);
    }

    toJSON() {
        return {
            code: this.code,
            message: this.message,
            status: this.status,
        }
    }
}