import { BaseError } from "./base.error"

export class AppError extends BaseError{}

// 範例：可擴充不同業務錯誤(Service 層、應用邏輯)
export class AuthError extends AppError {
  constructor(code: string, message: string, status = 401, cause?: unknown) {
    super({ code, message, status, cause })
  }
}
export class ValidationError extends AppError {
  constructor(code: string, message: string, status = 400, cause?: unknown) {
    super({ code, message, status, cause })
  }
}
export class PermissionError extends AppError {
  constructor(code: string, message: string, status = 403, cause?: unknown) {
    super({ code, message, status, cause })
  }
}
export class NotFoundError extends AppError {
  constructor(code: string, message: string, status = 404, cause?: unknown) {
    super({ code, message, status, cause })
  }
}
export class InternalError extends AppError {
  constructor(code: string, message: string, status = 500, cause?: unknown) {
    super({ code, message, status, cause })
  }
}


