import { BaseError } from './base.error'

export class RepositoryError extends BaseError {
    constructor(code: string, message: string, status = 500, cause?: unknown) {
        super({ code, message, status, cause })
    }
}
export class DuplicateKeyError extends RepositoryError {
    constructor(code: string, message: string, status = 422, cause?: unknown) {
        super(code, message, status, cause)
    }
}
export class ConnectionFailedError extends RepositoryError {
    constructor(code: string, message: string, status = 500, cause?: unknown) {
        super(code, message, status, cause)
    }
}
export class NotFoundError extends RepositoryError {
    constructor(code: string, message: string, status = 403, cause?: unknown) {
        super(code, message, status, cause)
    }
}