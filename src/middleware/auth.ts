import { env } from '@/env'
import { Context, Next } from 'hono'
import { verify } from 'hono/jwt'
import { AuthError } from '../errors/app.error'

export async function apiKeyAuthMiddleware(c: Context, next: Next) {
    const auth = c.req.header('authorization')
    if (!auth?.startsWith('Apikey ') || auth !== `Apikey ${env.API_KEY}`) {
        throw new AuthError('INVALID_API_KEY', 'Invalid API key', 401)
    }
    await next()
}

export async function jwtAuthMiddleware(c: Context, next: Next) {
    const auth = c.req.header('authorization')
    if (!auth || !auth.startsWith('Bearer ')) {
        throw new AuthError('UNAUTHORIZED', 'Missing or invalid Authorization header', 401)
    }
    const token = auth.slice(7)
    try {
        const payload = await verify(token, env.JWT_SECRET)
        if (!payload || typeof payload !== 'object' || !payload.id) {
            throw new AuthError('INVALID_TOKEN', 'Invalid JWT token', 401)
        }
        c.set('user', payload)
        await next()
    } catch (err) {
        throw new AuthError('INVALID_TOKEN', 'Invalid JWT token', 401)
    }
}
