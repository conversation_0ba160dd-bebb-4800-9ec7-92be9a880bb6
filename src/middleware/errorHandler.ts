import { Context } from 'hono'
import { AppError } from '../errors/app.error'
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

export const errorHandler = async (err: any, c: Context) => {
  // AppError 或自訂錯誤 (duck typing)
  if (
    err instanceof AppError ||
    err?.name === 'AppError' ||
    (err && typeof err.code === 'string' && typeof err.status === 'number')
  ) {
    return new Response(
      JSON.stringify({
        code: err.code,
        message: err.message,
        status: err.status,
      }),
      {
        status: err.status,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }

  if(err instanceof PrismaClientKnownRequestError){
    if(err.meta && err.code === "P2025"){
      return new Response(
        JSON.stringify({
          code: 'NOT_FOUND',
          message: err.meta.cause,
          status: 404,
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }
  }
  // if(err instanceof z.ZodError){
  //   return new Response(
  //     JSON.stringify({
  //       code: 'VALIDATION_ERROR',
  //       message: err.errors[0].message,
  //       status: 400,
  //     }),
  //     {
  //       status: 400,
  //       headers: { 'Content-Type': 'application/json' },
  //     }
  //   )
  // }

  // 500 類錯誤
  if (err && err.status && err.status >= 500) {
    console.error('[INTERNAL ERROR]', err)
    return new Response(
      JSON.stringify({
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
        status: 500,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }

  // 其他未知錯誤
  console.error('[UNHANDLED ERROR]', err)
  return new Response(
    JSON.stringify({
      code: 'INTERNAL_ERROR',
      message: 'Internal server error',
      status: 500,
    }),
    {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    }
  )

}
