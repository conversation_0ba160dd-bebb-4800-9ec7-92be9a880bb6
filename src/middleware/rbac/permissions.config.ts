export enum PermissionSubject {
    "work-item" = "work-item",
    "employee-report" = "employee-report",
    "department-report" = "department-report",
    "company-statistics" = "company-statistics",
    "company-dashboard" = "company-dashboard",
}
export enum PermissionAction {
    read = 'read',
    create = 'create',
    update = 'update',
    submit = 'submit',
}
export enum PermissionViewAngles {
    self = 'self',// 自己撰寫的
    department = 'department',// 部門所有人
    all = 'all',// 公司所有人
}
export type PermissionKey = `${PermissionSubject}:${PermissionAction}:${PermissionViewAngles}`
