import { AssignmentType, EmployeeRole, OrganizationType } from "@/module/auth/auth.schema"
import { PermissionSubject as ps, PermissionAction as pa, PermissionViewAngles as pv, PermissionKey } from "./permissions.config"

export interface ParsedUserRoleArgs {
    orgId: string
    orgType: OrganizationType,
    role: EmployeeRole
    assignmentType: AssignmentType
}

type RolePatternPermissionEntry = {
    view: string
    match: (parsed: ParsedUserRoleArgs) => boolean
    permissions: PermissionKey[]
}

export const EmployeePermission: RolePatternPermissionEntry = // ✅ 一般員工
{
    view: '員工視角',
    match: ({ orgType }) =>
        orgType === OrganizationType.department || orgType === OrganizationType.office,
    permissions: [
        `${ps["work-item"]}:${pa.read}:${pv.self}`,
        `${ps["work-item"]}:${pa.create}:${pv.self}`,
        `${ps["work-item"]}:${pa.update}:${pv.self}`,
        `${ps["work-item"]}:${pa.update}:${pv.department}`,
        `${ps["work-item"]}:${pa.read}:${pv.department}`,

        `${ps["employee-report"]}:${pa.read}:${pv.self}`, //employee-report是虛的東西
        `${ps["employee-report"]}:${pa.submit}:${pv.self}`,// 不能幫別人送出
        `${ps["employee-report"]}:${pa.read}:${pv.department}`,

        `${ps["department-report"]}:${pa.read}:${pv.department}`,
        `${ps["department-report"]}:${pa.create}:${pv.department}`,
        `${ps["department-report"]}:${pa.update}:${pv.department}`,
        `${ps["department-report"]}:${pa.submit}:${pv.department}`,
    ],
}

export const CEOPermission: RolePatternPermissionEntry = {
    view: 'CEO視角',
    match: ({ orgId, orgType, role }) =>
        (orgId === 'o_company' && role === 'manager') ||
        (orgType === 'division' && role === 'manager'),
    permissions: [
        `${ps["work-item"]}:${pa.read}:${pv.all}`,
        `${ps["employee-report"]}:${pa.read}:${pv.all}`,
        `${ps["department-report"]}:${pa.read}:${pv.all}`,
        `${ps["company-statistics"]}:${pa.read}:${pv.all}`,
        `${ps["company-dashboard"]}:${pa.read}:${pv.all}`,
    ],
}

export const HrPermission: RolePatternPermissionEntry =     // ✅ HR 組織內任何角色
{
    view: 'HR視角',
    match: ({ orgId }) => orgId === 'o_department_hr',
    permissions: [
        ...(EmployeePermission.permissions as PermissionKey[]),
        ...(CEOPermission.permissions as PermissionKey[]),
    ],
}

export const rolePatternPermissionMap: RolePatternPermissionEntry[] = [
    HrPermission,
    CEOPermission,
    EmployeePermission,
]
