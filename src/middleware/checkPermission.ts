import { JwtPayload } from '@/module/auth/auth.schema';
import { createMiddleware } from 'hono/factory';
import { PermissionKey } from './rbac/permissions.config';
import { ParsedUserRoleArgs, rolePatternPermissionMap } from './rbac/role-permission';
import { PermissionError } from '@/errors/app.error';

// 彙整所有權限成 Set
export function resolveUserPermissionSet(args: ParsedUserRoleArgs[]): Set<PermissionKey> {
  const permissions = new Set<PermissionKey>()
  for (const arg of args) {
    for (const rule of rolePatternPermissionMap) {
      if (rule.match(arg)) {
        rule.permissions.forEach(p => permissions.add(p))
      }
    }
  }
  return permissions
}

/**
 * 權限檢查中介層工廠函式
 * @param requiredPermissions - 需要的權限代碼，可以是單一字串或字串陣列 (滿足任一即可) 
 * e.g. checkPermission(['summary:read:self', 'summary:read:department']
 */
export const checkPermission = (requiredPermissions: PermissionKey | PermissionKey[]) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user') as JwtPayload;

    if(!user.assignments)  throw new PermissionError('PermissionError', 'Forbidden: User role not found in context')

    const permissionsToCheck = Array.isArray(requiredPermissions)
      ? requiredPermissions
      : [requiredPermissions];

    const permissionSet = resolveUserPermissionSet(user.assignments)

    // 檢查使用者是否擁有其中一個限定權限
    const isAllowed = permissionsToCheck.some(permission => permissionSet.has(permission));

    if (!isAllowed) {
      throw new PermissionError('PermissionError', `Forbidden: You do not have permission for this action. Required: ${permissionsToCheck.join(' OR ')}`)
    }

    // 權限檢查通過，繼續執行下一個中介層或路由處理器
    await next();
  });
};