
# WRS
## 02_PlanAgent_ERD
- context: 
    1. 01_prd.md
    2. 02_erd.md(空的狀態)
    3. 02_PlanAgent_ERD.md
- prompt: 
    1. 請幫我生成erd到 #file:02_erd.md
    2. acl為 employee/organization/employee_assigemnt
- open: 
    1. 02_erd.md(空的狀態)

## 03_PlanAgent_Domain
- context: 
    1. 01_prd.md
    2. 02_erd.md
    3. 03_domain_model.md (空的狀態)
    3. 03_PlanAgent_Domain.md
- prompt: 
    1. 請幫我依照03_PlanAgent_Domain的方法生成domain model到 #file:03_domain.md
- open: 
    1. 03_domain_model.md (空的狀態)
## 04_PlanAgent_API
- context: 
    1. 01_prd.md
    2. 03_domain_model.md 
    3. 04_PlanAgent_API.md
- prompt: 
    1. 請幫我依照04_PlanAgent_API的方法生成api spec到 #file:04_api_spec
    2. (optional)#file:04_api_spec 會依照 #file:04_PlanAgent_API.md 做出各別api規劃,但是,點進去每個檔案時,沒有看到`文件類型規格 API 詳細規格`這個子標題
    3. (Q) 請幫我在文件最下面說明一下為什麼設這個順序
    4. (Q) 一個個人週報,會有多個該週的工作項目, 為什麼不是先工作項目後做個人週報
    5. (Crash) 產出所有的 API 項目
    6. (Crash) 重新檢查 #file:04_api_spec,是否每個response都有參考到basic-schema.md
- open: 
    1. 04_api_spec/index.md (空的狀態)

## 05_ActAgent_TaskManager
- context: 
    3. 03_domain_model.md
    3. 04_api_spec/
    3. 05_ActAgent_TaskManager.md
- prompt: 
    1. 請幫我依照05_ActAgent_TaskManager的方法生成todo list 到 #file:task_manager.md
- open: 
    1. task_manager.md (空的狀態)

## 06_ActAgent_DomainProps
- context: 
    3. 03_domain_model.md
    3. wrs/domain/ 用拖曳的
    3. 06_ActAgent_DomainProps.md
- prompt: 
    1. 請幫我依照06_ActAgent_DomainProps的方法生成domain props
    2. enum 請先自動幫我建立
    3. Zod schema 欄位，皆必須包含 description 與 example，無例外。欄位說明請務求具體、避免曖昧不清或重複原名稱。
- open: 
    1. 不開

## 07_ActAgent_DomainModel
- context: 
    3. 03_domain_model.md
    3. wrs/domain/ 用拖曳的
    3. 07_ActAgent_DomainModel.md
- prompt: 
    1. 請幫我依照07_ActAgent_DomainModel的方法建立domain,建立順序可以參考04_api_spec/index.md的建議順序
    2. (optional) 這次要撰寫的domain是: `WrsWorkItem`
    3. 補充 ts docs並且撰寫單元測試
- open: 
    1. 不開

## 08_ActAgent_Prisma
- context: 
    3. 03_domain_model.md
    3. domain.schema.ts
    3. 08_ActAgent_Prisma.md
- prompt: 
    1. 請幫我依照08_ActAgent_Prisma的方法建立schema.prisma
- open: 
    1. schema.prisma

## 09_ActAgent_DomainMapper
- context: 
    3. 03_domain_model.md
    3. wrs/domain/ 用拖曳的
    3. 09_ActAgent_DomainMapper.md
- prompt: 
    1. 請幫我依照09_ActAgent_DomainMapper的方法透過domain entity建立domain mapper
    2. (optional) 這次要撰寫的domain是: `WrsWorkItem`
    3. 補充 ts docs並且撰寫單元測試
- open: 
    1. 不開


----
FIX Step: 
1. mark router todo => api spec
2. write router and mark service todo
3. write service and mark repo tod
4. write repo

FIX Schema and router example(body query param, paging query)
FIX add example(crud,paging)
FIX Service di repo
FIX Repo di prisma


## 10_ActAgent_Function =>失敗, 需要調整或分拆工作
- context: 
    3. 03_domain_model.md
    3. wrs/domain/ 用拖曳的
    3. 10_ActAgent_Function.md
    3. wrs/_docs/04_api_spec 用拖曳的
- prompt: 
    1. 請幫我依照10_ActAgent_Function的方法建立service與repository
    2. (optional) 這次要撰寫的service是api中的: `work-item.md`
    3. (Crash) 完成所有todo `context:infra`
    4. 在`infra/__tests__`資料夾中撰寫jest單元測試, 如果沒有, 必須建立該資料夾
    5. (Crash) 完成所有todo `context:app`
    6. 在`app/__tests__`資料夾中撰寫jest單元測試, 如果沒有, 必須建立該資料夾 `context:app`
    7. (Crash) 務必完成所有TODO: implement
    8. (Crash) Implement all methods per API spec and domain model
- open: 
    1. 不開

## 11_ActAgent_Router
- context: 
    3. 03_domain_model.md
    3. wrs/domain/ 用拖曳的
    3. 11_ActAgent_Router.md
    3. wrs/_docs/04_api_spec/xxx.md
- prompt: 
    1. 請幫我依照11_ActAgent_Router的方法建立router
- open: 
    1. 不開