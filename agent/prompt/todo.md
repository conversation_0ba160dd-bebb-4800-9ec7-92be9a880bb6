# 架構設計
ACL使用FDW

# 資料表欄位調整
1. 移除欄位: org table: supervisor_assigment_id, 使用employee_assigment的positionStatus='primary'
2. 移除欄位: employee table: 中的primary_org_id
2. 增加欄位: employee_assigment: 加上positionStatus 'primary' | 'acting' | 'concurrent'
# 資料的增加
3. 增加資料: org: 建立國內/海外 office

# 開案規劃
規劃階段        |    開發階段
PO claude ->      /> story -> B2E -> github coplit    \>
UX figma  -> PRD  -> story -> F2E -> cursor           -> release
TL claude ->      \> story -> QA  -> cursor           />

重點
1. ai做到80分, 幫忙處理重複且明確的任務, 工程師專注更關鍵的事情
2. 建立ai可獨得格式: 像PRD, ARC42技術SPEC, 用結構化的方式寫
3. Template化: 有用的Prompt, doc做成模板, 讓知識能快速傳承與套用

注意: 如果ai還沒達到80分, 修改prompt, 砍掉重作