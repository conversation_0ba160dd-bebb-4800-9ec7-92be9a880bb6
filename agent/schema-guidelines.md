# Zod Schema Guideline

- 此Zod Schema Guideline，為了讓程式碼更清晰且易於維護，將 Zod Schema 的撰寫規範獨立成專門的 Markdown
- 此規範檔案將統一管理所有 Zod schema 的書寫細節與慣例，便於團隊共用及更新。
-  Zod schema 欄位，皆必須包含 description 與 example，無例外。欄位說明請務求具體、避免曖昧不清或重複原名稱。
- 即使是最簡單的欄位（如 id），也必須說明其用途與格式。

---

## Domain Schema 規範 (放在 `/src/module/{context}/domain/schema/domain.schema.ts`)
1. 所有 Domain Schema 必須放在 `/src/module/{context}/domain` 目錄下。
2. 使用純 `zod` Domain Props
3. 所有欄位需加上 `.describe()` 與 `.openapi({ example: '...' })`，強化文件品質與可讀性。

## 其他 Schema 規範 (放在 `/src/module/{context}/domain/schema/query.schema.ts`)
1. 以 Domain Schema 作為基底，擴展或裁剪成 API 專用的驗證結構。如使用 `.extend()`、`.pick()`、`.partial()` 等方法組合及重用，避免重複定義。
3. Query 參數欄位須明確轉換型別，對陣列欄位使用 `.transform()` 轉換成陣列，並且只允許定義欄位。
5. optional 欄位需使用 `.optional()` 且透過 `.transform()` 過濾掉 `undefined`。

### 3.4 不建議的做法
- 不要在多處重複定義相同 schema，應優先透過 Domain Schema 組合或擴展。

---

### 3.5 範例

```ts
// domain/domain.schema.ts
import { z } from 'zod';

export const UserDomainProps = z.object({
  id: z.string().uuid().describe('使用者ID').openapi({ example: 'uuid-string' }),
  name: z.string().describe('使用者名稱').openapi({ example: 'Alice' }),
  email: z.string().email().describe('使用者電子郵件').openapi({ example: '<EMAIL>' }),
});

import { UserDomainProps } from '../../domain/user';
import { z } from 'zod';

export const UserCreateRequestSchema = UserDomainProps.pick({
  name: true,
  email: true,
}).extend({
  password: z.string().min(8).describe('密碼').openapi({ example: 'password123!' }),
});

import { UserDomainProps } from '../../domain/user';

export const UserResponseSchema = UserDomainProps.extend({
  createdAt: z.string().describe('建立時間').openapi({ example: '2025-05-29T12:00:00Z' }),
});

// domain/query.schema.ts
const userQuerySchema = domainUserSchema.pick({ id: true }).extend({
  tags: z.array(z.string()).optional()
}).transform(data => {
  // Query transform，確保tags是陣列或空陣列
  return {
    ...data,
    tags: data.tags ?? []
  };
});
```