# 專案 Coding Guideline
- My project's programming language is  Node.js + TypeScript
- Use early returns when possible
- Always add documentation when creating new functions and classes

---

## 1. 技術棧與開發流程
- 使用 Node.js + TypeScript，框架為 Hono.js  
- API 文件生成使用 `hono-openapi`、`@hono/swagger-ui`、`@hono/zod-openapi`  
- 採用測試驅動開發（TDD），測試覆蓋所有核心邏輯與異常流程  
- 輸入輸出驗證皆使用 `zod`，並搭配 `validator` 進行 request body、query、parameter 驗證

---

## 2. 程式碼撰寫規範

### 2.1 函式與類別

- 使用清楚且描述性的命名  
- 加入完整 JSDoc 註解（功能、參數、回傳值）  
- 儘量使用早期 return，避免深層巢狀  
- 處理錯誤時給出明確訊息，避免直接丟出不明錯誤

### 2.2 API 路由與中介件

- 必須使用 `describeRoute` middleware，帶入以下參數：  
  ```ts
  {
    tag: string;
    description: string;
    security?: string; // 預設 JwtKeyAuth
    security?: any; // 預設 JwtKeyAuth
    responseSchema200: ZodSchema; // 必填
    responsesSchema?: Record<string, any>; // 額外回應可選填（404、400 等）
  }
- response 200 狀態必須有明確且完整的 schema
- 其他非 200 狀態碼可另外覆寫 schema

--

## 3. Zod Schema 撰寫規範
Zod Schema 詳細規範獨立拆分成專門文件管理

Domain Schema 定義核心資料結構，使用 .extend(), .pick(), .partial() 等方法重用與組合

Request / Response Schema 基於 Domain Schema 擴展或裁剪

欄位必須具備 description 與 example，並使用 openapi() 補充 Swagger 文件

optional 欄位在解析時，若無值則不出現在結果物件中

不要拆 scalar schema 到獨立檔案，直接寫在相關 schema 裡

## 4. 文件與文件生成
充分利用 @hono/swagger-ui 自動生成 API 文件

所有欄位均需有 example 和 description，確保文件完整且可讀

## 5. 測試規範
採用 TDD，先寫測試再寫功能

覆蓋正常流程與錯誤流程

使用可重複且獨立的測試案例

測試結構依照功能模組拆分

## 6. 其他建議
善用 TypeScript 型別優勢，避免使用 any

保持程式碼簡潔，避免過度抽象

定期重構並持續改進程式碼品質

## 附錄
如需詳細 Zod Schema 撰寫規範，請參考專門的 schema-guideline.md 文件
專案中所有 middleware、route、schema 等均需有完整註解
