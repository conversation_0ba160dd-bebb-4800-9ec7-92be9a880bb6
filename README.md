# 專案啟動說明（How to Start the Project）
## 前置作業: 設定環境變數（Set up environment variables）

將 `.env.example` 複製為 `.env`，並根據需求修改內容：

```bash
cp .env.example .env
```

`.env` 需設定的主要變數如下：

- `PORT`：應用程式啟動的 port（預設 3000）
- `NODE_ENV`：環境（development 或 production）
- `DATABASE_URL`：Prisma 資料庫連線字串（預設為 Postgres 檔案）
- `JWT_SECRET`：JWT 驗證用的密鑰（請自行設一組安全字串）
- `VERSION`：版本號

## 方法1: 使用Docker Compose啟動(開發環境)
```bash
npm run dev:compose
```
###### note: 
如有變動不在src底下, 請先重啟docker-compose
```bash
docker-compose down 
docker-compose up --build
```

## 方法2: 使用本機啟動(開發環境)
```bash
npm run dev:local
```
###### note: 
1. 請修改.env
```dotenv
DATABASE_URL = "postgres://user:ps@localhost:2345/dev"
```
2. 如有變更db位置, 請進入`scripts/local.js`修改
```bash
npm run dev
```

伺服器預設會在 <http://localhost:3000> 啟動。

---

## Swagger UI

Swagger UI: [http://localhost:3000/swagger](http://localhost:3000/swagger)

---

如需進一步操作（如開啟 Prisma Studio），可使用：

```bash
npm run prisma:studio
```
