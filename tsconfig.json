{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": ".", "strict": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"]}, "jsx": "react-jsx", "jsxImportSource": "hono/jsx"}, "include": ["src", "prisma", "tests"], "exclude": ["node_modules"]}