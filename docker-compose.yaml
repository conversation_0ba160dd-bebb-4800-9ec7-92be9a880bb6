version: '3.8'
services:
  db:
    image: postgres:14.2-alpine
    environment:
      POSTGRES_DB: dev
      POSTGRES_USER: user
      POSTGRES_PASSWORD: ps
    ports:
      - "2345:5432"
    # volumes:
    #   - pgdata:/var/lib/postgresql/data

  backend-bpm:
    depends_on:
      - db
    build:
      context: .
      dockerfile: dev.Dockerfile
    image: backend-bpm
    volumes:
      - ./src:/app/src
    env_file:
      - ./.env
    environment:
      DATABASE_URL: **************************/dev
    ports:
      - "3000:3000"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  

  ##########################
  # 測試production環境時使用 #
  ##########################
  # 
  # backend-bpm-production:
  #   depends_on:
  #     - db
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   image: backend-bpm
  #   volumes:
  #     - ./src:/app/src
  #   env_file:
  #     - ./.env
  #   environment: # production
  #     DATABASE_URL: **************************/dev
  #   ports:
  #     - "3000:3000"

# volumes:
#   pgdata: