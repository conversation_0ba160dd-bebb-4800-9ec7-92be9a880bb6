import { PrismaClient } from '@prisma/client'
import fs from 'fs'

const prisma = new PrismaClient()

async function main() {

  const sql1 = fs.readFileSync('./prisma/sql/org.sql', 'utf-8');
  const sql2 = fs.readFileSync('./prisma/sql/emp.sql', 'utf-8');
  const sql3 = fs.readFileSync('./prisma/sql/assi.sql', 'utf-8');
  await prisma.$executeRawUnsafe(sql1);
  await prisma.$executeRawUnsafe(sql2);
  await prisma.$executeRawUnsafe(sql3);
  await prisma.login_account.createMany({
    data: [
      {
        id: 'login_FC0065_local',
        employee_id: 'FC0065',
        provider: 'local',
        username: 'mike',
        password: '$2b$10$f7Q6rSPcYN7spf6UbBvXAuBAuSYI6JpGab0ziysptPsxcgfgqFKNW'
      },
      {
        id: 'login_FC0056_local',
        employee_id: 'FC0056',
        provider: 'local',
        username: 'johnny',
        password: '$2b$10$f7Q6rSPcYN7spf6UbBvXAuBAuSYI6JpGab0ziysptPsxcgfgqFKNW'
      }
    ]
  })

  await prisma.doc_spec.createMany({
    data: [
      {
        "id": "ds_001",
        "label": "簽呈",
        "priority": "normal",
        "category": "一般類",
        "is_active": true,
        "doc_form": [
          {
            "key": "budgetType",
            "label": "類別",
            "values": [
              {
                "key": "procurement",
                "label": "採購/付款"
              },
              {
                "key": "administration",
                "label": "管理"
              },
              {
                "key": "business_memo",
                "label": "業務備忘"
              },
              {
                "key": "others",
                "label": "其他",
                "withInput": true
              }
            ],
            "validate": true,
            "hideLabel": false,
            "inputType": "radio",
            "conditional": ""
          },
          {
            "key": "topic",
            "label": "主旨",
            "isTopic": true,
            "validate": true,
            "hideLabel": false,
            "inputType": "textarea",
            "conditional": ""
          },
          {
            "key": "description",
            "label": "內容說明",
            "validate": true,
            "hideLabel": false,
            "inputType": "textarea",
            "conditional": ""
          },
          {
            "key": "file",
            "label": "上傳附件",
            "validate": false,
            "hideLabel": false,
            "inputType": "file",
            "conditional": ""
          }
        ],
        "doc_flow": [
          {
            "mode": "role",
            "type": "department"
          },
          {
            "mode": "role",
            "type": "division"
          },
          {
            "mode": "role",
            "type": "company"
          }
        ]
      },
      {
        "id": "ds_002",
        "label": "用印申請單",
        "priority": "normal",
        "category": "一般類",
        "is_active": true,
        "doc_form": [
          {
            "key": "topic",
            "label": "主旨",
            "isTopic": true,
            "validate": true,
            "hideLabel": false,
            "inputType": "textarea",
            "conditional": ""
          },
          {
            "key": "description",
            "label": "用途說明",
            "validate": true,
            "hideLabel": false,
            "inputType": "textarea",
            "conditional": ""
          },
          {
            "key": "document_name",
            "label": "文件名稱",
            "validate": true,
            "hideLabel": false,
            "inputType": "text",
            "conditional": ""
          },
          {
            "key": "stamp_type",
            "label": "用印別",
            "values": [
              {
                "key": "first",
                "label": "一級印鑑(經濟部登記章)",
                "stamp": {
                  "mode": "org",
                  "orgId": "o_department_finance",
                  "isManager": true
                }
              },
              {
                "key": "second",
                "label": "二級印鑑(銀行章)",
                "stamp": {
                  "mode": "org",
                  "orgId": "o_department_finance",
                  "isManager": true
                }
              },
              {
                "key": "third",
                "label": "三級印鑑(合約、對外文件等)",
                "stamp": {
                  "mode": "org",
                  "orgId": "o_department_legal",
                  "isManager": true
                }
              }
            ],
            "validate": true,
            "hideLabel": false,
            "inputType": "checkbox_group",
            "conditional": ""
          },
          {
            "key": "count",
            "label": "份數",
            "validate": true,
            "hideLabel": false,
            "inputType": "text",
            "conditional": ""
          },
          {
            "key": "file",
            "label": "上傳附件",
            "validate": false,
            "hideLabel": false,
            "inputType": "file",
            "conditional": ""
          }
        ],
        "doc_flow": [
          {
            "mode": "org",
            "orgId": "o_department_legal",
            "isManager": true
          },
          {
            "mode": "role",
            "type": "department"
          },
          {
            "mode": "role",
            "type": "division"
          },
          {
            "mode": "role",
            "type": "company"
          }
        ]
      }
    ]
  })

  await prisma.doc.createMany({
    data: [
      {
        id: 'd_001',
        status: 'in_progress',
        name: '年度預算申請',
        topic: '2025年度預算申請',
        spec_id: 'ds_001',
        form_data: { topic: '2025年度預算申請', budget: 10000 },
        author_id: 'FC0065',
        author_title: '資訊部 Mike職員',
        priority: 'low',
        category: '預算類',
        current_position: 0,
        flow_data: [
          { "mode": "role", "type": "department" },
          { "mode": "role", "type": "division" },
          { "mode": "role", "type": "company" }
        ],
        stamps:[
          {
            "mode": "org",
            "orgId": "o_department_finance",
            "isManager": true
          }
        ],
        created_at: new Date(1745611781535),
        updated_at: new Date(1745611781535)
      }
    ]
  })
  await prisma.task_sign.createMany({
    data: [
      {
        task_id: 'tss_001',
        doc_id: 'd_001',
        mode: 'sign',
        operator_id: 'FC0065',
        operator_title: 'Mike',
        status: 'signed',
        action: 'agree',
        position: 0,
        created_at: new Date(1745611781535),
        updated_at: new Date(1745611781535),
        completed_at: new Date(1745611781535)
      },
      {
        task_id: 'tss_002',
        doc_id: 'd_001',
        mode: 'sign',
        operator_id: 'FC0056',
        operator_title: 'Johnny',
        status: 'ready',
        action: 'null',
        position: 1,
        created_at: new Date(1745611781535),
        updated_at: new Date(1745611781535)
      },
      {
        task_id: 'tss_003',
        doc_id: 'd_001',
        mode: 'inform',
        operator_id: 'FC0002',
        operator_title: 'Lynn',
        status: 'pending',
        action: 'null',
        position: 2,
        created_at: new Date(1745611781535),
        updated_at: new Date(1745611781535)
      }
    ]
  })
}

main()
  .then(() => console.log('✅ Seed completed'))
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(() => prisma.$disconnect())
