generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ENUMS
enum DocPriority {
	high
	normal
	low
}
enum Provider {
  local
  google
  facebook
  sso
  azure
}

enum DocStatus {
  draft
  in_progress
  approved
  rejected
  canceled
}

enum SignTaskMode {
  inform
  sign
  added
}

enum SignStatus {
  pending
  ready
  signed
  disabled
}

enum SignAction{
  null
  agree
  disagree
  backed
}

enum HistoryAction {
  null
  agree
  disagree
  cancel
  undo
  disable
  submit
  add
  back
  comment
  edit
  // result
  canceled
  approved
  rejected
}

enum EmployeeStatus {
  active
  inactive
  left
}

enum EmployeeRole {
  manager       //主管
  member        //組員
  intern        //實習
  contractor    //約聘
}

enum AssignmentType{
  primary     //主要
	concurrent  //兼任
	acting      //代理
}


enum OrganizationType{
  company     // 公司
  division    // 處
  department  // 部門
  office      // 辦公室
}

// MODELS

model organization {
  id          String      @id
  name        String      @unique
  label     String  // 為多國語系
  type        OrganizationType
  parent_id   String?

  assignments employee_assignment[] 
  @@index([name])
}

model employee {
  id          String       @id
  nick_name   String  @unique
  email       String  @unique
  last_name     String?
  first_name   String?
  title       String?
  english_name String?
  image_url   String?
  status      EmployeeStatus  @default(active)
  language    String  @default("en-US") //IETF BCP 47 語言標籤
  timezone    String  @default("Asia/Taipei") // IANA 時區名稱

  assignments employee_assignment[]
  login_accounts login_account[]
}


model employee_assignment {
  id            String     @id
  employee_id   String
  org_id        String
  assignment_type     AssignmentType @default(primary)
  role          EmployeeRole
  start_date    DateTime @db.Timestamptz(3)
  end_date      DateTime? @db.Timestamptz(3)
  change_reason String?

  
  // 關聯到員工
  employee   employee @relation(fields: [employee_id], references: [id])
  // 所屬單位
  organization organization @relation(fields: [org_id], references: [id])
 

  @@unique([employee_id, org_id])
  @@index([employee_id])
  @@index([org_id])
}

model login_account {
  id          String     @id
  employee_id String
  provider    Provider
  username    String
  password    String?
  external_id String?

  employee    employee   @relation(fields: [employee_id], references: [id])

  @@unique([provider, username])
  @@unique([provider, external_id])
  @@index([employee_id])
}

model doc_spec {
  id        String     @id
  label     String?  // 為多國語系
  priority  DocPriority
  category  String
  is_active Boolean

  doc_form  Json
  doc_flow  Json    //id, preId, orgId, isManager
}

model doc_daily_counter {
 id  String   @id // e.g. ********
 count Int    // counter + OCC version
}

model doc {
  id                   String     @id
  status               DocStatus
  name                 String
  topic                String?
  author_id            String
  author_title         String
  priority             String    // from spec
  category             String    // from spec
  current_position     Float?    // 該文件目前準備簽署位置: ready position
  spec_id              String
  form_data            Json
  flow_data            Json      // 建立草稿時該文件的簽署流程 implement form spec
  stamps               Json      // 用印

  created_at        DateTime  @db.Timestamptz(3)
  updated_at        DateTime  @db.Timestamptz(3)
  submitted_at      DateTime? @db.Timestamptz(3)
  completed_at      DateTime? @db.Timestamptz(3)

  histories            doc_history[]
  task_signs           task_sign[]
  task_comments        task_comment[]
  files                doc_file[]

  @@index([author_id])
}

model doc_file {
  id          String   @id
  doc_id      String
  task_id     String?
  file_name   String
  file_type   String
  file_size   Int
  uploader_id String
  uploader_title  String
  uploaded_at  DateTime @db.Timestamptz(3)
  doc         doc      @relation(fields: [doc_id], references: [id])
}

model task_sign {
  task_id     String     @id
  doc_id      String
  operator_id String
  operator_title String
  mode        SignTaskMode  @default(sign)
  status      SignStatus
  action      SignAction
  
  position    Float       // 該簽署位置 main 整數, children 浮點數

  created_at  DateTime    @db.Timestamptz(3)
  updated_at  DateTime    @db.Timestamptz(3)
  completed_at DateTime?  @db.Timestamptz(3)
  doc         doc        @relation(fields: [doc_id], references: [id])
}

model task_comment {
  task_id        String   @id
  doc_id         String
  operator_id    String
  operator_title  String
  comment        String
  created_at     DateTime   @db.Timestamptz(3)
  doc            doc      @relation(fields: [doc_id], references: [id])
}

model doc_history {
  id          String   @id
  doc_id      String
  operator_id String
  operator_title String?
  message     String
  action        HistoryAction
  timestamp   DateTime    @db.Timestamptz(3)
  is_success  Boolean
  doc         doc      @relation(fields: [doc_id], references: [id])
  error       String?
}


// WRS
// TODO: split this
// generator client {
//   provider = "prisma-client-js"
//   output   = "../../node_modules/.prisma/wrs"
// }
// 
// datasource db {
//   provider = "postgresql"
//   url      = env("WRS_DATABASE_URL")
// }

enum EmployeeReportStatus {
  draft
  submitted
}
enum DepartmentReportStatus {
  draft
  submitted
}

enum WorkItemType {
  project
  routine
  support
  admin
  training
  other
}

enum WorkItemStatus {
  draft
  submitted
  done
  deleted
}

model wrs_week {
  id        String   @id @db.VarChar(32) // p_yyyymmdd_yyyymmdd
  start_date DateTime @db.Date
  end_date   DateTime @db.Date
  employee_reports wrs_employee_report[]
  department_reports wrs_department_report[]
  work_items wrs_work_item[]
}

model wrs_employee_report {
  id          String   @id @db.VarChar(32)
  org_id        String   @db.VarChar(32)
  employee_id  String   @db.VarChar(32)
  employee_title String @db.VarChar(32)
  week_id      String   @db.VarChar(32)
  submitted_at DateTime? @db.Timestamptz(3)
  status      EmployeeReportStatus
  work_items   wrs_work_item[]

  week        wrs_week  @relation(fields: [week_id], references: [id])
  // TODO: employee relation (外部ACL, 只查詢, 不建migration)
  // @@ignore 可用於外部表

  @@unique([week_id, employee_id])
}

model wrs_work_item {
  id               String   @id @db.VarChar(32)
  status             WorkItemStatus
  employee_report_id String   @db.VarChar(32)
  employee_id  String   @db.VarChar(32)
  employee_title  String  @db.VarChar(32)
  org_id            String   @db.VarChar(32)
  week_id           String   @db.VarChar(32)
  type             WorkItemType
  name             String   @db.Text
  progress_percent  Int      @db.SmallInt
  result           String   @db.Text
  suggestion       String   @db.Text
  next_week_goal     String   @db.Text
  quantitative_metric    String      @db.Text
  history_ref_id     String?  @db.VarChar(32)
  submitted_at DateTime? @db.Timestamptz(3)
  done_at DateTime? @db.Timestamptz(3)
  editor_employee_id  String?   @db.VarChar(32)

  employee_report   wrs_employee_report @relation(fields: [employee_report_id], references: [id])
  week             wrs_week  @relation(fields: [week_id], references: [id])
  // TODO: historyRef relation (self reference, 若需查詢歷史)
}



model wrs_department_report {
  id                  String   @id @db.VarChar(64)
  org_id               String   @db.VarChar(32)
  week_id              String   @db.VarChar(32)
  summary_employee_id   String   @db.VarChar(32)
  summary             String   @db.Text
  highlight           String   @db.Text
  plan                String   @db.Text
  submitted_at         DateTime? @db.Timestamptz(3)
  status              DepartmentReportStatus
  reviewed_by_supervisor Boolean

  week                wrs_week  @relation(fields: [week_id], references: [id])
  // TODO: org relation (外部ACL, 只查詢, 不建migration)
  // TODO: summaryEmployee relation (外部ACL, 只查詢, 不建migration)

  @@unique([org_id, week_id])
}

// ❗ Prisma 不知道這是 FDW，但你只在程式中只讀就好
// @@ignore      // 若你只需要查、但不生成 migration，可加這行
// TODO: ACL/Employee/Org 相關表，僅查詢不建表，請於程式層處理