//1. org/employee單純 2. assigment做關聯
//2. employee_assignment補全

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum EmployeeStatus {
  active
  inactive
  left
}

enum EmployeeRole {
  manager       //主管
  member        //組員
  intern        //實習
  contractor    //約聘
}

enum EmployeeRoleType{
  primary     //主要
	concurrent  //兼任
	acting      //代理
}

enum OrganizationType{
  company     // 公司
  division    // 處
  department  // 部門
  office      // 辦公室
}

// MODELS

model hr_org {
  id          String      @id
  name        String      @unique
  label     String  // 為多國語系
  type        OrganizationType
  parent_id   String?

  assignments hr_employee_assignment[] 
  @@index([name])
}

model hr_employee {
  id          String       @id
  nick_name   String  @unique
  email       String  @unique
  last_name     String?
  first_name   String?
  title       String?
  english_name String?
  image_url   String?
  status      EmployeeStatus  @default(active)
  language    String  @default("en-US") //IETF BCP 47 語言標籤
  timezone    String  @default("Asia/Taipei") // IANA 時區名稱

  assignments hr_employee_assignment[]
  login_accounts auth_login_account[]
}

model hr_employee_assignment {
  id            String     @id
  employee_id   String
  org_id        String
  assignment_type     AssignmentType @default(primary)
  role          EmployeeRole
  start_date    DateTime @db.Timestamptz(6)
  end_date      DateTime? @db.Timestamptz(6)
  change_reason String?

  
  // 關聯到員工
  employee   hr_employee @relation(fields: [employee_id], references: [id])

  // 所屬單位
  organization hr_org @relation(fields: [org_id], references: [id])
 

  @@unique([employee_id, org_id])
  @@index([employee_id])
  @@index([org_id])
}

model auth_login_account {
  id          String     @id
  employee_id String
  provider    Provider
  username    String
  password    String?
  external_id String?

  employee    employee   @relation(fields: [employee_id], references: [id])

  @@unique([provider, username])
  @@unique([provider, external_id])
  @@index([employee_id])
}
