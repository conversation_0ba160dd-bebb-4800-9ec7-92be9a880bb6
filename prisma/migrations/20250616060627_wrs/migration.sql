-- CreateEnum
CREATE TYPE "EmployeeReportStatus" AS ENUM ('draft', 'submitted');

-- CreateEnum
CREATE TYPE "DepartmentReportStatus" AS ENUM ('draft', 'submitted');

-- CreateEnum
CREATE TYPE "WorkItemType" AS ENUM ('project', 'routine', 'support', 'admin', 'training', 'other');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "WorkItemStatus" AS ENUM ('draft', 'submitted', 'done', 'deleted');

-- CreateTable
CREATE TABLE "wrs_week" (
    "id" VARCHAR(32) NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,

    CONSTRAINT "wrs_week_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "wrs_employee_report" (
    "id" VARCHAR(32) NOT NULL,
    "org_id" VARCHAR(32) NOT NULL,
    "employee_id" VARCHAR(32) NOT NULL,
    "employee_title" VARCHAR(32) NOT NULL,
    "week_id" VARCHAR(32) NOT NULL,
    "submitted_at" TIMESTAMPTZ(3),
    "status" "EmployeeReportStatus" NOT NULL,

    CONSTRAINT "wrs_employee_report_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "wrs_work_item" (
    "id" VARCHAR(32) NOT NULL,
    "status" "WorkItemStatus" NOT NULL,
    "employee_report_id" VARCHAR(32) NOT NULL,
    "employee_id" VARCHAR(32) NOT NULL,
    "employee_title" VARCHAR(32) NOT NULL,
    "org_id" VARCHAR(32) NOT NULL,
    "week_id" VARCHAR(32) NOT NULL,
    "type" "WorkItemType" NOT NULL,
    "name" TEXT NOT NULL,
    "progress_percent" SMALLINT NOT NULL,
    "result" TEXT NOT NULL,
    "suggestion" TEXT NOT NULL,
    "next_week_goal" TEXT NOT NULL,
    "quantitative_metric" TEXT NOT NULL,
    "history_ref_id" VARCHAR(32),
    "submitted_at" TIMESTAMPTZ(3),
    "done_at" TIMESTAMPTZ(3),

    CONSTRAINT "wrs_work_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "wrs_department_report" (
    "id" VARCHAR(64) NOT NULL,
    "org_id" VARCHAR(32) NOT NULL,
    "week_id" VARCHAR(32) NOT NULL,
    "summary_employee_id" VARCHAR(32) NOT NULL,
    "summary" TEXT NOT NULL,
    "highlight" TEXT NOT NULL,
    "plan" TEXT NOT NULL,
    "submitted_at" TIMESTAMPTZ(3),
    "status" "DepartmentReportStatus" NOT NULL,
    "reviewed_by_supervisor" BOOLEAN NOT NULL,

    CONSTRAINT "wrs_department_report_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "wrs_employee_report_week_id_employee_id_key" ON "wrs_employee_report"("week_id", "employee_id");

-- CreateIndex
CREATE UNIQUE INDEX "wrs_department_report_org_id_week_id_key" ON "wrs_department_report"("org_id", "week_id");

-- AddForeignKey
ALTER TABLE "wrs_employee_report" ADD CONSTRAINT "wrs_employee_report_week_id_fkey" FOREIGN KEY ("week_id") REFERENCES "wrs_week"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wrs_work_item" ADD CONSTRAINT "wrs_work_item_employee_report_id_fkey" FOREIGN KEY ("employee_report_id") REFERENCES "wrs_employee_report"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wrs_work_item" ADD CONSTRAINT "wrs_work_item_week_id_fkey" FOREIGN KEY ("week_id") REFERENCES "wrs_week"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "wrs_department_report" ADD CONSTRAINT "wrs_department_report_week_id_fkey" FOREIGN KEY ("week_id") REFERENCES "wrs_week"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
