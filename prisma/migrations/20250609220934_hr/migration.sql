/*
  Warnings:

  - You are about to drop the column `primary_org_id` on the `employee` table. All the data in the column will be lost.
  - You are about to drop the column `supervisor_id` on the `employee_assignment` table. All the data in the column will be lost.
  - You are about to drop the column `supervisor_assignment_id` on the `organization` table. All the data in the column will be lost.

*/
-- Create<PERSON>num
CREATE TYPE "AssignmentType" AS ENUM ('primary', 'concurrent', 'acting');

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "EmployeeRole" ADD VALUE 'intern';
ALTER TYPE "EmployeeRole" ADD VALUE 'contractor';

-- DropForeignKey
ALTER TABLE "employee_assignment" DROP CONSTRAINT "employee_assignment_supervisor_id_fkey";

-- DropForeignKey
ALTER TABLE "organization" DROP CONSTRAINT "organization_supervisor_assignment_id_fkey";

-- DropIndex
DROP INDEX "organization_supervisor_assignment_id_key";

-- AlterTable
ALTER TABLE "employee" DROP COLUMN "primary_org_id";

-- AlterTable
ALTER TABLE "employee_assignment" DROP COLUMN "supervisor_id",
ADD COLUMN  "assignment_type" "AssignmentType" NOT NULL DEFAULT 'primary';

-- AlterTable
ALTER TABLE "organization" DROP COLUMN "supervisor_assignment_id";
