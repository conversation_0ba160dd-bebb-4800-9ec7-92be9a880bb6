-- CreateEnum
CREATE TYPE "DocPriority" AS ENUM ('high', 'normal', 'low');

-- CreateEnum
CREATE TYPE "Provider" AS ENUM ('local', 'google', 'facebook', 'sso', 'azure');

-- CreateEnum
CREATE TYPE "DocStatus" AS ENUM ('draft', 'in_progress', 'approved', 'rejected', 'canceled');

-- CreateEnum
CREATE TYPE "SignTaskMode" AS ENUM ('inform', 'sign', 'added');

-- CreateEnum
CREATE TYPE "SignStatus" AS ENUM ('pending', 'ready', 'signed', 'disabled');

-- CreateEnum
CREATE TYPE "SignAction" AS ENUM ('null', 'agree', 'disagree', 'backed');

-- CreateEnum
CREATE TYPE "HistoryAction" AS ENUM ('null', 'agree', 'disagree', 'cancel', 'undo', 'disable', 'submit', 'add', 'back', 'comment', 'edit', 'canceled', 'approved', 'rejected');

-- CreateEnum
CREATE TYPE "EmployeeStatus" AS ENUM ('active', 'inactive', 'left');

-- CreateEnum
CREATE TYPE "EmployeeRole" AS ENUM ('manager', 'member');

-- CreateEnum
CREATE TYPE "OrganizationType" AS ENUM ('company', 'division', 'department', 'office');

-- CreateTable
CREATE TABLE "organization" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "type" "OrganizationType" NOT NULL,
    "parent_id" TEXT,
    "supervisor_assignment_id" TEXT,

    CONSTRAINT "organization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "employee" (
    "id" TEXT NOT NULL,
    "nick_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "last_name" TEXT,
    "first_name" TEXT,
    "title" TEXT,
    "english_name" TEXT,
    "image_url" TEXT,
    "status" "EmployeeStatus" NOT NULL DEFAULT 'active',
    "primary_org_id" TEXT,
    "language" TEXT NOT NULL DEFAULT 'en-US',
    "timezone" TEXT NOT NULL DEFAULT 'Asia/Taipei',

    CONSTRAINT "employee_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "employee_assignment" (
    "id" TEXT NOT NULL,
    "employee_id" TEXT NOT NULL,
    "org_id" TEXT NOT NULL,
    "role" "EmployeeRole" NOT NULL,
    "supervisor_id" TEXT,
    "start_date" TIMESTAMPTZ(3) NOT NULL,
    "end_date" TIMESTAMPTZ(3),
    "change_reason" TEXT,

    CONSTRAINT "employee_assignment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "login_account" (
    "id" TEXT NOT NULL,
    "employee_id" TEXT NOT NULL,
    "provider" "Provider" NOT NULL,
    "username" TEXT NOT NULL,
    "password" TEXT,
    "external_id" TEXT,

    CONSTRAINT "login_account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "doc_spec" (
    "id" TEXT NOT NULL,
    "label" TEXT,
    "priority" "DocPriority" NOT NULL,
    "category" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL,
    "doc_form" JSONB NOT NULL,
    "doc_flow" JSONB NOT NULL,

    CONSTRAINT "doc_spec_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "doc_daily_counter" (
    "id" TEXT NOT NULL,
    "count" INTEGER NOT NULL,

    CONSTRAINT "doc_daily_counter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "doc" (
    "id" TEXT NOT NULL,
    "status" "DocStatus" NOT NULL,
    "name" TEXT NOT NULL,
    "topic" TEXT,
    "author_id" TEXT NOT NULL,
    "author_title" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "current_position" DOUBLE PRECISION,
    "spec_id" TEXT NOT NULL,
    "form_data" JSONB NOT NULL,
    "flow_data" JSONB NOT NULL,
    "stamps" JSONB NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "submitted_at" TIMESTAMPTZ(3),
    "completed_at" TIMESTAMPTZ(3),

    CONSTRAINT "doc_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "doc_file" (
    "id" TEXT NOT NULL,
    "doc_id" TEXT NOT NULL,
    "task_id" TEXT,
    "file_name" TEXT NOT NULL,
    "file_type" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "uploader_id" TEXT NOT NULL,
    "uploader_title" TEXT NOT NULL,
    "uploaded_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "doc_file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "task_sign" (
    "task_id" TEXT NOT NULL,
    "doc_id" TEXT NOT NULL,
    "operator_id" TEXT NOT NULL,
    "operator_title" TEXT NOT NULL,
    "mode" "SignTaskMode" NOT NULL DEFAULT 'sign',
    "status" "SignStatus" NOT NULL,
    "action" "SignAction" NOT NULL,
    "position" DOUBLE PRECISION NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "completed_at" TIMESTAMPTZ(3),

    CONSTRAINT "task_sign_pkey" PRIMARY KEY ("task_id")
);

-- CreateTable
CREATE TABLE "task_comment" (
    "task_id" TEXT NOT NULL,
    "doc_id" TEXT NOT NULL,
    "operator_id" TEXT NOT NULL,
    "operator_title" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "task_comment_pkey" PRIMARY KEY ("task_id")
);

-- CreateTable
CREATE TABLE "doc_history" (
    "id" TEXT NOT NULL,
    "doc_id" TEXT NOT NULL,
    "operator_id" TEXT NOT NULL,
    "operator_title" TEXT,
    "message" TEXT NOT NULL,
    "action" "HistoryAction" NOT NULL,
    "timestamp" TIMESTAMPTZ(3) NOT NULL,
    "is_success" BOOLEAN NOT NULL,
    "error" TEXT,

    CONSTRAINT "doc_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "organization_name_key" ON "organization"("name");

-- CreateIndex
CREATE UNIQUE INDEX "organization_supervisor_assignment_id_key" ON "organization"("supervisor_assignment_id");

-- CreateIndex
CREATE INDEX "organization_name_idx" ON "organization"("name");

-- CreateIndex
CREATE UNIQUE INDEX "employee_nick_name_key" ON "employee"("nick_name");

-- CreateIndex
CREATE UNIQUE INDEX "employee_email_key" ON "employee"("email");

-- CreateIndex
CREATE INDEX "employee_assignment_employee_id_idx" ON "employee_assignment"("employee_id");

-- CreateIndex
CREATE INDEX "employee_assignment_org_id_idx" ON "employee_assignment"("org_id");

-- CreateIndex
CREATE UNIQUE INDEX "employee_assignment_employee_id_org_id_key" ON "employee_assignment"("employee_id", "org_id");

-- CreateIndex
CREATE INDEX "login_account_employee_id_idx" ON "login_account"("employee_id");

-- CreateIndex
CREATE UNIQUE INDEX "login_account_provider_username_key" ON "login_account"("provider", "username");

-- CreateIndex
CREATE UNIQUE INDEX "login_account_provider_external_id_key" ON "login_account"("provider", "external_id");

-- CreateIndex
CREATE INDEX "doc_author_id_idx" ON "doc"("author_id");

-- AddForeignKey
ALTER TABLE "organization" ADD CONSTRAINT "organization_supervisor_assignment_id_fkey" FOREIGN KEY ("supervisor_assignment_id") REFERENCES "employee_assignment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_assignment" ADD CONSTRAINT "employee_assignment_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_assignment" ADD CONSTRAINT "employee_assignment_supervisor_id_fkey" FOREIGN KEY ("supervisor_id") REFERENCES "employee"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "employee_assignment" ADD CONSTRAINT "employee_assignment_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "login_account" ADD CONSTRAINT "login_account_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "employee"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doc_file" ADD CONSTRAINT "doc_file_doc_id_fkey" FOREIGN KEY ("doc_id") REFERENCES "doc"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_sign" ADD CONSTRAINT "task_sign_doc_id_fkey" FOREIGN KEY ("doc_id") REFERENCES "doc"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "task_comment" ADD CONSTRAINT "task_comment_doc_id_fkey" FOREIGN KEY ("doc_id") REFERENCES "doc"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "doc_history" ADD CONSTRAINT "doc_history_doc_id_fkey" FOREIGN KEY ("doc_id") REFERENCES "doc"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
