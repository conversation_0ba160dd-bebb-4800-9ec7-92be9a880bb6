# 採用技術
framework: hono.js

# 資源
啟動點: src/app.ts
di container: src/container.ts
env: env.ts
db connection: src/infra/db.ts
errors: src/errors/
auth middleware: src/middleware/

# 設計
完整實做可參考 `src/module/auth`
## router: 
### 基本格式
```typescript
import { validator } from 'hono-openapi/zod'

const router = new Hono() // <-特別注意
const spec=openApiMiddleware('auth')
export function createAuthRouter(service: AuthService) {
  router.post('/register', 
    spec( <<ResponseSchema>>),// <-特別注意
    validator("json", <<RequestSchema>>),// <-特別注意
    async (c: Context) => {
      ....
    }
  )
  return router
}
```
### 注意事項
1. 請使用`src/errors/appError.ts`, 如有需求, 可最下方新增 
```typescript
export class AuthError extends AppError {
  constructor(code: string, message: string, status = 401, cause?: unknown) {
    super({ code, message, status, cause })
  }
}
```
2. 每個router都需要使用 `spec` 與 `validator` middleware, 為了要配合做`swagger`
```typescript
const router = new Hono()
const spec=openApiMiddleware('auth')// <-特別注意

router.post('/register', 
  spec( <<ResponseSchema>>),// <-特別注意
  validator("json", <<RequestSchema>>),// <-特別注意
  ...
```
3. router建立完成後, 請到`src/app.ts`中註冊route
```typescript
function initRoute(app: Hono) {
    app.use('*', apiLog);
    app.get('/hello', (c) => c.text('Hello, Hono!'));
    // 請把新的route掛載在這裡此行底下
    // ... 
    app.route('/api/auth', authRouter);
    app.route('/api/<<new path>>', <<newRouter>>);// <-特別注意
}
```

## service: 
### 基本格式
```typescript
export class Service {
  private repo: AuthRepository

  constructor(repo: AuthRepository) {// <-特別注意
    this.repo = repo
  }
  ...
}
```
### 注意事項
1. 需要到`src/container.ts`做DI
## repository:
### 基本格式
```typescript
export class AuthRepository {
  constructor(private readonly prisma: PrismaClient) {}

  async createEmployee(data: any, tx?: Prisma.TransactionClient) {// <-特別注意
    const db = tx ?? this.prisma// <-特別注意
    return db.employee.create({ data })// <-特別注意
  }

}
```
### 注意事項
1. 每個db操作都可能需要使用tx, 需要保留參數 `tx?: Prisma.TransactionClient`
```typescript
async createEmployee(data: any, tx?: Prisma.TransactionClient) {// <-特別注意
    const db = tx ?? this.prisma// <-特別注意
    return db.employee.create({ data })// <-特別注意
}
```
2. 有關於資料庫的table_name與attribute_name都會是使用snake_case
3. 需要到`src/container.ts`做DI

