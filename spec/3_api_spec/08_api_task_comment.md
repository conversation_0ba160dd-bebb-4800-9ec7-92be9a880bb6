## API 目錄

### 評論任務（TaskComment）
- GET `/api/task-comments`：查詢評論列表（分頁/條件）
- POST `/api/task-comments`：新增評論（write）
- GET `/api/task-comments/{id}`：查詢單一評論
- POST `/api/task-comments/{id}/files`：評論新增附件（addFile）
- DELETE `/api/task-comments/{id}/files/{fileId}`：評論移除附件（removeFile，軟刪除）

---

## TaskComment API 詳細規格

### GET `/api/task-comments`
- **功能說明**：查詢評論列表，支援分頁與條件篩選（依 docId、taskId、operatorId）。
- **Request Schema**：[TaskCommentListQuery](./schema.md#taskcommentlistquery)
- **Response Schema**：[TaskCommentListResponse](./schema.md#taskcommentlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "taskId": "t001",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 1,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "c001",
      "docId": "d001",
      "taskId": "t001",
      "operatorId": "e001",
      "comment": "請補充說明資料",
      "files": [
        {
          "id": "f001",
          "docId": "d001",
          "taskId": "t001",
          "fileName": "附件1.pdf",
          "fileType": "application/pdf",
          "fileSize": 123456,
          "url": "/files/f001",
          "uploadedBy": "e001",
          "createdAt": "2025-04-14T09:00:00Z",
          "updatedAt": "2025-04-14T09:00:00Z",
          "deletedAt": null
        }
      ],
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [TaskComment](./schema.md#taskcomment)
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-comments`
- **功能說明**：新增評論（write），可夾帶多個附件。
- **Request Schema**：[CreateTaskCommentRequest](./schema.md#createtaskcommentrequest)
- **Response Schema**：[TaskComment](./schema.md#taskcomment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "taskId": "t001",
  "operatorId": "e001",
  "comment": "請補充說明資料",
  "files": [
    {
      "fileName": "附件1.pdf",
      "fileType": "application/pdf",
      "fileSize": 123456,
      "content": "base64string"
    }
  ]
}
```

**Response 範例**
```json
{
  "id": "c001",
  "docId": "d001",
  "taskId": "t001",
  "operatorId": "e001",
  "comment": "請補充說明資料",
  "files": [
    {
      "id": "f001",
      "docId": "d001",
      "taskId": "t001",
      "fileName": "附件1.pdf",
      "fileType": "application/pdf",
      "fileSize": 123456,
      "url": "/files/f001",
      "uploadedBy": "e001",
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z",
      "deletedAt": null
    }
  ],
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "INVALID_COMMENT",
  "message": "評論內容不得為空"
}
```

**本 API 使用的 schema**  
- [TaskComment](./schema.md#taskcomment)
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/task-comments/{id}`
- **功能說明**：查詢單一評論詳細資料。
- **Response Schema**：[TaskComment](./schema.md#taskcomment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "c001",
  "docId": "d001",
  "taskId": "t001",
  "operatorId": "e001",
  "comment": "請補充說明資料",
  "files": [
    {
      "id": "f001",
      "docId": "d001",
      "taskId": "t001",
      "fileName": "附件1.pdf",
      "fileType": "application/pdf",
      "fileSize": 123456,
      "url": "/files/f001",
      "uploadedBy": "e001",
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z",
      "deletedAt": null
    }
  ],
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此評論"
}
```

**本 API 使用的 schema**  
- [TaskComment](./schema.md#taskcomment)
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-comments/{id}/files`
- **功能說明**：為評論新增附件（addFile）。
- **Request Schema**：[AddTaskCommentFileRequest](./schema.md#addtaskcommentfilerequest)
- **Response Schema**：[FileAttachment](./schema.md#fileattachment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "fileName": "補充資料.png",
  "fileType": "image/png",
  "fileSize": 23456,
  "content": "base64string"
}
```

**Response 範例**
```json
{
  "id": "f002",
  "docId": "d001",
  "taskId": "t001",
  "fileName": "補充資料.png",
  "fileType": "image/png",
  "fileSize": 23456,
  "url": "/files/f002",
  "uploadedBy": "e001",
  "createdAt": "2025-04-14T10:00:00Z",
  "updatedAt": "2025-04-14T10:00:00Z",
  "deletedAt": null
}
```

**錯誤範例**
```json
{
  "code": "INVALID_FILE",
  "message": "檔案格式不支援"
}
```

**本 API 使用的 schema**  
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/task-comments/{id}/files/{fileId}`
- **功能說明**：移除評論附件（removeFile，軟刪除）。
- **Response Schema**：[RemoveTaskCommentFileResponse](./schema.md#removetaskcommentfileresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此附件"
}
```

**本 API 使用的 schema**  
- [RemoveTaskCommentFileResponse](./schema.md#removetaskcommentfileresponse)
- [ErrorResponse](./schema.md#errorresponse)
