## API 目錄

### 文件管理
- GET `/api/docs`：查詢文件列表（分頁/篩選）
- POST `/api/docs`：建立新文件
- GET `/api/docs/{id}`：查詢單一文件
- PATCH `/api/docs/{id}`：更新文件（僅限草稿）
- DELETE `/api/docs/{id}`：刪除文件（僅限草稿）

---

## 文件管理 API 詳細規格

### GET `/api/docs`
- **功能說明**：查詢文件列表，支援分頁與條件篩選（如狀態、作者、文件類型）。
- **Request Schema**：[DocListQuery](./schema.md#doclistquery)
- **Response Schema**：[DocListResponse](./schema.md#doclistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "page": 1,
  "pageSize": 20,
  "status": "InProgress",
  "authorId": "e001",
  "docSpecId": "spec01"
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "doc001",
      "docSpecId": "spec01",
      "formData": {
        "leaveType": "事假",
        "days": 2
      },
      "authorId": "e001",
      "status": "InProgress",
      "currentSignTaskId": "task101",
      "firstSignTaskId": "task100",
      "files": [],
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [Doc](./schema.md#doc)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/docs`
- **功能說明**：建立新文件（草稿）。
- **Request Schema**：[CreateDocRequest](./schema.md#createdocrequest)
- **Response Schema**：[Doc](./schema.md#doc)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docSpecId": "spec01",
  "formData": {
    "leaveType": "事假",
    "days": 2
  },
  "files": []
}
```

**Response 範例**
```json
{
  "id": "doc002",
  "docSpecId": "spec01",
  "formData": {
    "leaveType": "事假",
    "days": 2
  },
  "authorId": "e001",
  "status": "Draft",
  "currentSignTaskId": null,
  "firstSignTaskId": null,
  "files": [],
  "createdAt": "2025-04-14T09:10:00Z",
  "updatedAt": "2025-04-14T09:10:00Z"
}
```

**錯誤範例**
```json
{
  "code": "INVALID_FORM",
  "message": "表單資料不正確"
}
```

**本 API 使用的 schema**  
- [Doc](./schema.md#doc)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/docs/{id}`
- **功能說明**：查詢單一文件詳細資料。
- **Response Schema**：[Doc](./schema.md#doc)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "doc001",
  "docSpecId": "spec01",
  "formData": {
    "leaveType": "事假",
    "days": 2
  },
  "authorId": "e001",
  "status": "InProgress",
  "currentSignTaskId": "task101",
  "firstSignTaskId": "task100",
  "files": [],
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此文件"
}
```

**本 API 使用的 schema**  
- [Doc](./schema.md#doc)
- [ErrorResponse](./schema.md#errorresponse)

---

### PATCH `/api/docs/{id}`
- **功能說明**：更新文件內容（僅限草稿狀態）。
- **Request Schema**：[UpdateDocRequest](./schema.md#updatedocrequest)
- **Response Schema**：[Doc](./schema.md#doc)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "formData": {
    "leaveType": "病假",
    "days": 1
  },
  "files": []
}
```

**Response 範例**
```json
{
  "id": "doc002",
  "docSpecId": "spec01",
  "formData": {
    "leaveType": "病假",
    "days": 1
  },
  "authorId": "e001",
  "status": "Draft",
  "currentSignTaskId": null,
  "firstSignTaskId": null,
  "files": [],
  "createdAt": "2025-04-14T09:10:00Z",
  "updatedAt": "2025-04-14T09:15:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_DRAFT",
  "message": "僅草稿可編輯"
}
```

**本 API 使用的 schema**  
- [Doc](./schema.md#doc)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/docs/{id}`
- **功能說明**：刪除文件（僅限草稿）。
- **Response Schema**：[DeleteDocResponse](./schema.md#deletedocresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_DRAFT",
  "message": "僅草稿可刪除"
}
```

**本 API 使用的 schema**  
- [DeleteDocResponse](./schema.md#deletedocresponse)
- [ErrorResponse](./schema.md#errorresponse)
