## API 目錄

### 文件簽核流程
- GET `/api/docs/pending`：查詢「我的待簽」文件
- GET `/api/docs/signed`：查詢「我的已簽」文件
- GET `/api/docs/created?status={status}`：查詢「我發起的」文件（依狀態）

---

## 文件簽核流程 API 詳細規格

### GET `/api/docs/pending`
- **功能說明**：查詢目前登入者「待簽」的文件（即尚未簽核、狀態為 Pending 的 TaskSign 所屬文件）。
- **Request Schema**：[TaskSignListQuery](./schema.md#tasksignlistquery)
- **Response Schema**：[TaskSignListResponse](./schema.md#tasksignlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "operatorId": "e001",
  "state": "Pending",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 1,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "ts001",
      "docId": "d001",
      "operatorId": "e001",
      "isChild": false,
      "parentId": null,
      "isDone": false,
      "state": "Pending",
      "stageId": "s01",
      "createdAt": "2025-04-12T18:00:00Z",
      "updatedAt": "2025-04-12T18:00:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "UNAUTHORIZED",
  "message": "未登入"
}
```

**本 API 使用的 schema**  
- [TaskSign](./schema.md#tasksign)
- [TaskSignListQuery](./schema.md#tasksignlistquery)
- [TaskSignListResponse](./schema.md#tasksignlistresponse)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/docs/signed`
- **功能說明**：查詢目前登入者「已簽」的文件（即已完成簽核、狀態為 Agree/Disagree 的 TaskSign 所屬文件）。
- **Request Schema**：[TaskSignListQuery](./schema.md#tasksignlistquery)
- **Response Schema**：[TaskSignListResponse](./schema.md#tasksignlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "operatorId": "e001",
  "state": "Agree",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "ts002",
      "docId": "d002",
      "operatorId": "e001",
      "isChild": false,
      "parentId": null,
      "isDone": true,
      "state": "Agree",
      "stageId": "s02",
      "createdAt": "2025-04-13T10:00:00Z",
      "updatedAt": "2025-04-13T10:10:00Z"
    },
    {
      "id": "ts003",
      "docId": "d003",
      "operatorId": "e001",
      "isChild": false,
      "parentId": null,
      "isDone": true,
      "state": "Disagree",
      "stageId": "s03",
      "createdAt": "2025-04-13T11:00:00Z",
      "updatedAt": "2025-04-13T11:10:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "UNAUTHORIZED",
  "message": "未登入"
}
```

**本 API 使用的 schema**  
- [TaskSign](./schema.md#tasksign)
- [TaskSignListQuery](./schema.md#tasksignlistquery)
- [TaskSignListResponse](./schema.md#tasksignlistresponse)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/docs/created?status={status}`
- **功能說明**：查詢目前登入者「我發起的」文件，可依文件狀態（Draft, InProgress, Approved, Rejected, Canceled）篩選。
- **Request Schema**：[DocListQuery](./schema.md#doclistquery)
- **Response Schema**：[DocListResponse](./schema.md#doclistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "authorId": "e001",
  "status": "InProgress",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 1,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "d001",
      "docSpecId": "spec01",
      "formData": {
        "reason": "請假",
        "days": 3
      },
      "authorId": "e001",
      "status": "InProgress",
      "currentSignTaskId": "ts001",
      "firstSignTaskId": "ts001",
      "files": [],
      "createdAt": "2025-04-12T18:00:00Z",
      "updatedAt": "2025-04-12T18:00:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "UNAUTHORIZED",
  "message": "未登入"
}
```

**本 API 使用的 schema**  
- [Doc](./schema.md#doc)
- [DocListQuery](./schema.md#doclistquery)
- [DocListResponse](./schema.md#doclistresponse)
- [ErrorResponse](./schema.md#errorresponse)
