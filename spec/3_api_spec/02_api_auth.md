## API 目錄

### 認證/帳號管理
- POST `/api/auth/login`：登入（本地或第三方）
- POST `/api/auth/logout`：登出
- GET `/api/auth/me`：查詢目前登入狀態
- POST `/api/auth/register`：註冊

---

## 認證/帳號管理 API 詳細規格

### POST `/api/auth/login`
- **功能說明**：使用本地帳號或第三方 provider 進行登入。
- **Request Schema**：[LoginRequest](./schema.md#loginrequest)
- **Response Schema**：[LoginResponse](./schema.md#loginresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "provider": "local",
  "account": "user1",
  "password": "pass123"
}
```
或
```json
{
  "provider": "google",
  "account": "<EMAIL>",
  "externalId": "google-oauth-id"
}
```

**Response 範例**
```json
{
  "token": "jwt-token-string",
  "employee": {
    "id": "e001",
    "name": "王小明",
    "assignments": [],
    "createdAt": "2025-04-12T18:00:00Z",
    "updatedAt": "2025-04-12T18:00:00Z"
  },
  "loginAccount": {
    "id": "la001",
    "employeeId": "e001",
    "provider": "local",
    "account": "user1"
  }
}
```

**錯誤範例**
```json
{
  "code": "INVALID_CREDENTIALS",
  "message": "帳號或密碼錯誤"
}
```

**本 API 使用的 schema**  
- [LoginRequest](./schema.md#loginrequest)
- [LoginResponse](./schema.md#loginresponse)
- [Employee](./schema.md#employee)
- [LoginAccount](./schema.md#loginaccount)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/auth/logout`
- **功能說明**：登出目前 session。
- **Request Schema**：無
- **Response Schema**：[LogoutResponse](./schema.md#logoutresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_LOGGED_IN",
  "message": "尚未登入"
}
```

**本 API 使用的 schema**  
- [LogoutResponse](./schema.md#logoutresponse)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/auth/me`
- **功能說明**：查詢目前登入的員工與帳號資訊。
- **Response Schema**：[GetCurrentUserResponse](./schema.md#getcurrentuserresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "employee": {
    "id": "e001",
    "name": "王小明",
    "assignments": [],
    "createdAt": "2025-04-12T18:00:00Z",
    "updatedAt": "2025-04-12T18:00:00Z"
  },
  "loginAccount": {
    "id": "la001",
    "employeeId": "e001",
    "provider": "local",
    "account": "user1"
  }
}
```

**錯誤範例**
```json
{
  "code": "NOT_LOGGED_IN",
  "message": "尚未登入"
}
```

**本 API 使用的 schema**  
- [GetCurrentUserResponse](./schema.md#getcurrentuserresponse)
- [Employee](./schema.md#employee)
- [LoginAccount](./schema.md#loginaccount)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/auth/register`
- **功能說明**：註冊新帳號（僅支援 local provider），可選擇綁定現有員工或建立新員工。
- **Request Schema**：[RegisterRequest](./schema.md#registerrequest)
- **Response Schema**：[RegisterResponse](./schema.md#registerresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "account": "newuser",
  "password": "newpass123",
  "employee": {
    "name": "林新生"
  }
}
```
或
```json
{
  "account": "newuser2",
  "password": "newpass456",
  "employeeId": "e002"
}
```

**Response 範例**
```json
{
  "employee": {
    "id": "e010",
    "name": "林新生",
    "assignments": [],
    "createdAt": "2025-04-14T09:55:00Z",
    "updatedAt": "2025-04-14T09:55:00Z"
  },
  "loginAccount": {
    "id": "la010",
    "employeeId": "e010",
    "provider": "local",
    "account": "newuser"
  }
}
```

**錯誤範例**
```json
{
  "code": "ACCOUNT_EXISTS",
  "message": "帳號已存在"
}
```

**本 API 使用的 schema**  
- [RegisterRequest](./schema.md#registerrequest)
- [RegisterResponse](./schema.md#registerresponse)
- [Employee](./schema.md#employee)
- [LoginAccount](./schema.md#loginaccount)
- [ErrorResponse](./schema.md#errorresponse)
