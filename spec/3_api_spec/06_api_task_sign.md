## API 目錄

### 簽核任務管理（TaskSign）
- GET `/api/task-signs`：查詢簽核任務列表（分頁/條件）
- GET `/api/task-signs/{id}`：查詢單一簽核任務
- POST `/api/task-signs/{id}/do`：執行簽核（同意/不同意）
- POST `/api/task-signs/{id}/done`：標記任務完成
- POST `/api/task-signs/{id}/disable`：標記任務無效
- POST `/api/task-signs/{id}/undo`：還原簽核狀態

---

## 簽核任務 API 詳細規格

### GET `/api/task-signs`
- **功能說明**：查詢簽核任務列表，支援分頁與條件（docId、operatorId、狀態、是否子任務）。
- **Request Schema**：[TaskSignListQuery](./schema.md#tasksignlistquery)
- **Response Schema**：[TaskSignListResponse](./schema.md#tasksignlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "state": "Pending",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "ts001",
      "docId": "d001",
      "operatorId": "e001",
      "isChild": false,
      "parentId": null,
      "isDone": false,
      "state": "Pending",
      "stageId": "s01",
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z"
    },
    {
      "id": "ts002",
      "docId": "d001",
      "operatorId": "e002",
      "isChild": true,
      "parentId": "ts001",
      "isDone": false,
      "state": "Pending",
      "stageId": "s01",
      "createdAt": "2025-04-14T09:01:00Z",
      "updatedAt": "2025-04-14T09:01:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/task-signs/{id}`
- **功能說明**：查詢單一簽核任務詳細資料。
- **Response Schema**：[TaskSign](./schema.md#tasksign)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "ts001",
  "docId": "d001",
  "operatorId": "e001",
  "isChild": false,
  "parentId": null,
  "isDone": false,
  "state": "Pending",
  "stageId": "s01",
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此簽核任務"
}
```

**本 API 使用的 schema**  
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-signs/{id}/do`
- **功能說明**：執行簽核動作（同意/不同意），將狀態由 Pending 轉為 Agree 或 Disagree。
- **Request Schema**：
  - `do(agree)`/`do(disagree)`：[TaskSignActionRequest](./schema.md#tasksignactionrequest)（action 僅允許 "agree" 或 "disagree"）
- **Response Schema**：[TaskSignActionResponse](./schema.md#tasksignactionresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例（同意）**
```json
{
  "action": "agree"
}
```
**Request 範例（不同意）**
```json
{
  "action": "disagree"
}
```

**Response 範例**
```json
{
  "success": true,
  "taskSign": {
    "id": "ts001",
    "docId": "d001",
    "operatorId": "e001",
    "isChild": false,
    "parentId": null,
    "isDone": true,
    "state": "Agree",
    "stageId": "s01",
    "createdAt": "2025-04-14T09:00:00Z",
    "updatedAt": "2025-04-14T10:10:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "INVALID_ACTION",
  "message": "無效的簽核動作"
}
```

**本 API 使用的 schema**  
- [TaskSignActionRequest](./schema.md#tasksignactionrequest)
- [TaskSignActionResponse](./schema.md#tasksignactionresponse)
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-signs/{id}/done`
- **功能說明**：標記任務完成（主線需所有子任務完成，子任務簽完即結束）。
- **Request Schema**：無
- **Response Schema**：[TaskSignActionResponse](./schema.md#tasksignactionresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true,
  "taskSign": {
    "id": "ts001",
    "docId": "d001",
    "operatorId": "e001",
    "isChild": false,
    "parentId": null,
    "isDone": true,
    "state": "Agree",
    "stageId": "s01",
    "createdAt": "2025-04-14T09:00:00Z",
    "updatedAt": "2025-04-14T10:15:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "NOT_ALLOWED",
  "message": "尚有子任務未完成"
}
```

**本 API 使用的 schema**  
- [TaskSignActionResponse](./schema.md#tasksignactionresponse)
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-signs/{id}/disable`
- **功能說明**：標記任務無效（如取消、退回後的任務）。
- **Request Schema**：無
- **Response Schema**：[TaskSignActionResponse](./schema.md#tasksignactionresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true,
  "taskSign": {
    "id": "ts001",
    "docId": "d001",
    "operatorId": "e001",
    "isChild": false,
    "parentId": null,
    "isDone": true,
    "state": "Disable",
    "stageId": "s01",
    "createdAt": "2025-04-14T09:00:00Z",
    "updatedAt": "2025-04-14T10:20:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "NOT_ALLOWED",
  "message": "當前狀態不可執行 disable"
}
```

**本 API 使用的 schema**  
- [TaskSignActionResponse](./schema.md#tasksignactionresponse)
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-signs/{id}/undo`
- **功能說明**：還原簽核狀態，將狀態還原為 Pending。
- **Request Schema**：無
- **Response Schema**：[TaskSignActionResponse](./schema.md#tasksignactionresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true,
  "taskSign": {
    "id": "ts001",
    "docId": "d001",
    "operatorId": "e001",
    "isChild": false,
    "parentId": null,
    "isDone": false,
    "state": "Pending",
    "stageId": "s01",
    "createdAt": "2025-04-14T09:00:00Z",
    "updatedAt": "2025-04-14T10:25:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "NOT_ALLOWED",
  "message": "當前狀態不可執行 undo"
}
```

**本 API 使用的 schema**  
- [TaskSignActionResponse](./schema.md#tasksignactionresponse)
- [TaskSign](./schema.md#tasksign)
- [ErrorResponse](./schema.md#errorresponse)

---
