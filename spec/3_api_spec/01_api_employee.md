## API 目錄

### 員工管理
- GET `/api/employees`：查詢員工列表（分頁/篩選）
- POST `/api/employees`：建立員工
- GET `/api/employees/{id}`：查詢單一員工
- PATCH `/api/employees/{id}`：更新員工
- DELETE `/api/employees/{id}`：刪除員工
- POST `/api/employees/{id}/assignments`：新增任職關聯
- DELETE `/api/employees/{id}/assignments/{assignmentId}`：移除任職關聯

---

## 員工管理 API 詳細規格

### GET `/api/employees`
- **功能說明**：查詢員工列表，支援分頁與條件篩選。
- **Request Schema**：[EmployeeListQuery](./schema.md#employeelistquery)
- **Response Schema**：[EmployeeListResponse](./schema.md#employeelistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "page": 1,
  "pageSize": 20,
  "name": "王"
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "e001",
      "name": "王小明",
      "assignments": [
        {
          "id": "a001",
          "department": { "id": "d01", "name": "人資部" },
          "position": { "id": "p01", "title": "專員" }
        }
      ],
      "createdAt": "2025-04-12T18:00:00Z",
      "updatedAt": "2025-04-12T18:00:00Z"
    },
    {
      "id": "e002",
      "name": "王小華",
      "assignments": [],
      "createdAt": "2025-04-12T18:00:00Z",
      "updatedAt": "2025-04-12T18:00:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [Employee](./schema.md#employee)
- [EmployeeAssignment](./schema.md#employeeassignment)
- [Department](./schema.md#department)
- [Position](./schema.md#position)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/employees`
- **功能說明**：建立新員工。
- **Request Schema**：[CreateEmployeeRequest](./schema.md#createemployeerequest)
- **Response Schema**：[Employee](./schema.md#employee)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "name": "李大仁",
  "assignments": [
    {
      "departmentId": "d02",
      "positionId": "p02"
    }
  ]
}
```

**Response 範例**
```json
{
  "id": "e003",
  "name": "李大仁",
  "assignments": [
    {
      "id": "a002",
      "department": { "id": "d02", "name": "財務部" },
      "position": { "id": "p02", "title": "經理" }
    }
  ],
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "DUPLICATE_EMPLOYEE",
  "message": "員工已存在"
}
```

**本 API 使用的 schema**  
- [Employee](./schema.md#employee)
- [EmployeeAssignment](./schema.md#employeeassignment)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/employees/{id}`
- **功能說明**：查詢單一員工詳細資料。
- **Response Schema**：[Employee](./schema.md#employee)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "e001",
  "name": "王小明",
  "assignments": [
    {
      "id": "a001",
      "department": { "id": "d01", "name": "人資部" },
      "position": { "id": "p01", "title": "專員" }
    }
  ],
  "createdAt": "2025-04-12T18:00:00Z",
  "updatedAt": "2025-04-12T18:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此員工"
}
```

**本 API 使用的 schema**  
- [Employee](./schema.md#employee)
- [EmployeeAssignment](./schema.md#employeeassignment)
- [ErrorResponse](./schema.md#errorresponse)

---

### PATCH `/api/employees/{id}`
- **功能說明**：更新員工資料。
- **Request Schema**：[UpdateEmployeeRequest](./schema.md#updateemployeerequest)
- **Response Schema**：[Employee](./schema.md#employee)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "name": "王小明（已改名）"
}
```

**Response 範例**
```json
{
  "id": "e001",
  "name": "王小明（已改名）",
  "assignments": [
    {
      "id": "a001",
      "department": { "id": "d01", "name": "人資部" },
      "position": { "id": "p01", "title": "專員" }
    }
  ],
  "createdAt": "2025-04-12T18:00:00Z",
  "updatedAt": "2025-04-14T09:01:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此員工"
}
```

**本 API 使用的 schema**  
- [Employee](./schema.md#employee)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/employees/{id}`
- **功能說明**：刪除員工。
- **Response Schema**：[DeleteEmployeeResponse](./schema.md#deleteemployeeresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此員工"
}
```

**本 API 使用的 schema**  
- [DeleteEmployeeResponse](./schema.md#deleteemployeeresponse)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/employees/{id}/assignments`
- **功能說明**：為員工新增任職關聯（部門/職位）。
- **Request Schema**：[AddAssignmentRequest](./schema.md#addassignmentrequest)
- **Response Schema**：[EmployeeAssignment](./schema.md#employeeassignment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "departmentId": "d03",
  "positionId": "p03"
}
```

**Response 範例**
```json
{
  "id": "a003",
  "department": { "id": "d03", "name": "研發部" },
  "position": { "id": "p03", "title": "工程師" }
}
```

**錯誤範例**
```json
{
  "code": "ASSIGNMENT_EXISTS",
  "message": "該任職關聯已存在"
}
```

**本 API 使用的 schema**  
- [EmployeeAssignment](./schema.md#employeeassignment)
- [Department](./schema.md#department)
- [Position](./schema.md#position)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/employees/{id}/assignments/{assignmentId}`
- **功能說明**：移除員工的任職關聯。
- **Response Schema**：[DeleteAssignmentResponse](./schema.md#deleteassignmentresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此任職關聯"
}
```

**本 API 使用的 schema**  
- [DeleteAssignmentResponse](./schema.md#deleteassignmentresponse)
- [ErrorResponse](./schema.md#errorresponse)



