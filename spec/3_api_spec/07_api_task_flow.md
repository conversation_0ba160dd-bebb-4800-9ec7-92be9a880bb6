## API 目錄

### 流程操作（TaskFlow）
- POST `/api/task-flows/add`：加簽
- POST `/api/task-flows/back`：退回
- POST `/api/task-flows/submit`：送出
- POST `/api/task-flows/cancel`：取消

---

## 流程操作 API 詳細規格

### POST `/api/task-flows/add`
- **功能說明**：於指定主線簽核任務下加簽一或多位簽核人，產生子 TaskSign。
- **Request Schema**：[TaskFlowAddRequest](./schema.md#taskflowaddrequest)
- **Response Schema**：[TaskFlowResponse](./schema.md#taskflowresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "doc001",
  "mainTaskSignId": "ts002",
  "assigneeIds": ["emp003", "emp004"],
  "reason": "需加簽主管"
}
```

**Response 範例**
```json
{
  "success": true,
  "taskFlow": {
    "id": "tf001",
    "docId": "doc001",
    "operatorId": "emp002",
    "action": "add",
    "targetEmployeeId": null,
    "reason": "需加簽主管",
    "createdAt": "2025-04-14T10:00:00Z",
    "updatedAt": "2025-04-14T10:00:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "INVALID_TASK_STATE",
  "message": "目前節點不可加簽"
}
```

**本 API 使用的 schema**  
- [TaskFlowAddRequest](./schema.md#taskflowaddrequest)
- [TaskFlowResponse](./schema.md#taskflowresponse)
- [TaskFlow](./schema.md#taskflow)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-flows/back`
- **功能說明**：將流程退回至指定主線簽核節點，並還原後續任務狀態。
- **Request Schema**：[TaskFlowBackRequest](./schema.md#taskflowbackrequest)
- **Response Schema**：[TaskFlowResponse](./schema.md#taskflowresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "doc001",
  "fromTaskId": "ts003",
  "toTaskId": "ts001",
  "reason": "資料需補充"
}
```

**Response 範例**
```json
{
  "success": true,
  "taskFlow": {
    "id": "tf002",
    "docId": "doc001",
    "operatorId": "emp003",
    "action": "back",
    "targetEmployeeId": null,
    "reason": "資料需補充",
    "createdAt": "2025-04-14T10:05:00Z",
    "updatedAt": "2025-04-14T10:05:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "INVALID_BACK_TARGET",
  "message": "退回目標節點無效"
}
```

**本 API 使用的 schema**  
- [TaskFlowBackRequest](./schema.md#taskflowbackrequest)
- [TaskFlowResponse](./schema.md#taskflowresponse)
- [TaskFlow](./schema.md#taskflow)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-flows/submit`
- **功能說明**：送出表單，啟動簽核流程，產生主線 TaskSign。
- **Request Schema**：[TaskFlowSubmitRequest](./schema.md#taskflowsubmitrequest)
- **Response Schema**：[TaskFlowResponse](./schema.md#taskflowresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "doc001",
  "reason": "表單填寫完成"
}
```

**Response 範例**
```json
{
  "success": true,
  "taskFlow": {
    "id": "tf003",
    "docId": "doc001",
    "operatorId": "emp001",
    "action": "submit",
    "targetEmployeeId": null,
    "reason": "表單填寫完成",
    "createdAt": "2025-04-14T10:10:00Z",
    "updatedAt": "2025-04-14T10:10:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "ALREADY_SUBMITTED",
  "message": "表單已送出"
}
```

**本 API 使用的 schema**  
- [TaskFlowSubmitRequest](./schema.md#taskflowsubmitrequest)
- [TaskFlowResponse](./schema.md#taskflowresponse)
- [TaskFlow](./schema.md#taskflow)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/task-flows/cancel`
- **功能說明**：取消尚未完成的簽核流程，將後續任務標記為 Disable。
- **Request Schema**：[TaskFlowCancelRequest](./schema.md#taskflowcancelrequest)
- **Response Schema**：[TaskFlowResponse](./schema.md#taskflowresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "doc001",
  "reason": "流程作廢"
}
```

**Response 範例**
```json
{
  "success": true,
  "taskFlow": {
    "id": "tf004",
    "docId": "doc001",
    "operatorId": "emp001",
    "action": "cancel",
    "targetEmployeeId": null,
    "reason": "流程作廢",
    "createdAt": "2025-04-14T10:15:00Z",
    "updatedAt": "2025-04-14T10:15:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "CANNOT_CANCEL",
  "message": "流程已完成，無法取消"
}
```

**本 API 使用的 schema**  
- [TaskFlowCancelRequest](./schema.md#taskflowcancelrequest)
- [TaskFlowResponse](./schema.md#taskflowresponse)
- [TaskFlow](./schema.md#taskflow)
- [ErrorResponse](./schema.md#errorresponse)
