## API 目錄

### 檔案附件管理
- GET `/api/file-attachments`：查詢附件列表（分頁/條件）
- GET `/api/file-attachments/{id}`：查詢單一附件
- POST `/api/file-attachments`：上傳附件
- DELETE `/api/file-attachments/{id}`：軟刪除附件

---

## 檔案附件 API 詳細規格

### GET `/api/file-attachments`
- **功能說明**：查詢檔案附件列表，支援依 docId、taskId 篩選，分頁查詢，可選擇是否包含已刪除（軟刪除）附件。
- **Request Schema**：[FileAttachmentListQuery](./schema.md#fileattachmentlistquery)
- **Response Schema**：[FileAttachmentListResponse](./schema.md#fileattachmentlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "page": 1,
  "pageSize": 20,
  "includeDeleted": false
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "f001",
      "docId": "d001",
      "taskId": "t001",
      "fileName": "附件1.pdf",
      "fileType": "application/pdf",
      "fileSize": 123456,
      "url": "/files/f001",
      "uploadedBy": "e001",
      "createdAt": "2025-04-14T09:00:00Z",
      "updatedAt": "2025-04-14T09:00:00Z",
      "deletedAt": null
    },
    {
      "id": "f002",
      "docId": "d001",
      "taskId": "t001",
      "fileName": "附件2.jpg",
      "fileType": "image/jpeg",
      "fileSize": 234567,
      "url": "/files/f002",
      "uploadedBy": "e002",
      "createdAt": "2025-04-14T09:10:00Z",
      "updatedAt": "2025-04-14T09:10:00Z",
      "deletedAt": null
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/file-attachments/{id}`
- **功能說明**：查詢單一檔案附件詳細資料。
- **Response Schema**：[FileAttachment](./schema.md#fileattachment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "f001",
  "docId": "d001",
  "taskId": "t001",
  "fileName": "附件1.pdf",
  "fileType": "application/pdf",
  "fileSize": 123456,
  "url": "/files/f001",
  "uploadedBy": "e001",
  "createdAt": "2025-04-14T09:00:00Z",
  "updatedAt": "2025-04-14T09:00:00Z",
  "deletedAt": null
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此附件"
}
```

**本 API 使用的 schema**  
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/file-attachments`
- **功能說明**：上傳檔案附件，需指定所屬文件（docId）與任務（taskId），檔案內容以 base64 傳遞。成功後回傳完整 FileAttachment 物件。
- **Request Schema**：[CreateFileAttachmentRequest](./schema.md#createfileattachmentrequest)
- **Response Schema**：[FileAttachment](./schema.md#fileattachment)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "taskId": "t001",
  "file": {
    "fileName": "附件3.png",
    "fileType": "image/png",
    "fileSize": 345678,
    "content": "iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

**Response 範例**
```json
{
  "id": "f003",
  "docId": "d001",
  "taskId": "t001",
  "fileName": "附件3.png",
  "fileType": "image/png",
  "fileSize": 345678,
  "url": "/files/f003",
  "uploadedBy": "e003",
  "createdAt": "2025-04-14T09:20:00Z",
  "updatedAt": "2025-04-14T09:20:00Z",
  "deletedAt": null
}
```

**錯誤範例**
```json
{
  "code": "INVALID_FILE",
  "message": "檔案格式或大小不符"
}
```

**本 API 使用的 schema**  
- [CreateFileAttachmentRequest](./schema.md#createfileattachmentrequest)
- [FileAttachment](./schema.md#fileattachment)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/file-attachments/{id}`
- **功能說明**：軟刪除檔案附件（僅標記 deletedAt，不物理刪除），成功回傳 success。
- **Response Schema**：[DeleteFileAttachmentResponse](./schema.md#deletefileattachmentresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此附件"
}
```

**本 API 使用的 schema**  
- [DeleteFileAttachmentResponse](./schema.md#deletefileattachmentresponse)
- [ErrorResponse](./schema.md#errorresponse)
