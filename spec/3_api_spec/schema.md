# Schema 目錄

- [通用型 Schema](#通用型-schema)
  - [ErrorResponse](#errorresponse)
  - [DeleteEmployeeResponse](#deleteemployeeresponse)
  - [DeleteAssignmentResponse](#deleteassignmentresponse)
- [Employee 相關](#employee-相關)
  - [Employee](#employee)
  - [EmployeeListQuery](#employeelistquery)
  - [EmployeeListResponse](#employeelistresponse)
  - [CreateEmployeeRequest](#createemployeerequest)
  - [UpdateEmployeeRequest](#updateemployeerequest)
- [Assignment 相關](#assignment-相關)
  - [EmployeeAssignment](#employeeassignment)
  - [AddAssignmentRequest](#addassignmentrequest)
- [部門/職位](#部門職位)
  - [Department](#department)
  - [Position](#position)
  - [DepartmentListResponse](#departmentlistresponse)
  - [PositionListResponse](#positionlistresponse)
- [Auth 相關](#auth-相關)
  - [LoginRequest](#loginrequest)
  - [LoginResponse](#loginresponse)
  - [LogoutResponse](#logoutresponse)
  - [GetCurrentUserResponse](#getcurrentuserresponse)
  - [LoginAccount](#loginaccount)
  - [RegisterRequest](#registerrequest)
  - [RegisterResponse](#registerresponse)
- [DocSpec 相關](#docspec-相關)
  - [DocSpecListResponse](#docspeclistresponse)
  - [CreateDocSpecRequest](#createdocspecrequest)
  - [UpdateDocSpecRequest](#updatedocspecrequest)
  - [ActivateDocSpecRequest](#activatedocspecrequest)
  - [DeleteDocSpecResponse](#deletedocspecresponse)
  - [DocSpec](#docspec)
  - [DocForm](#docform)
  - [FormItem](#formitem)
  - [DocFlow](#docflow)
  - [FlowItem](#flowitem)
- [Doc 相關](#doc-相關)
  - [Doc](#doc)
  - [DocListQuery](#doclistquery)
  - [DocListResponse](#doclistresponse)
  - [CreateDocRequest](#createdocrequest)
  - [UpdateDocRequest](#updatedocrequest)
  - [DeleteDocResponse](#deletedocresponse)
- [FileAttachment 相關](#fileattachment-相關)
  - [FileAttachment](#fileattachment)
  - [FileAttachmentListQuery](#fileattachmentlistquery)
  - [FileAttachmentListResponse](#fileattachmentlistresponse)
  - [CreateFileAttachmentRequest](#createfileattachmentrequest)
  - [DeleteFileAttachmentResponse](#deletefileattachmentresponse)
- [TaskSign 相關](#tasksign-相關)
  - [TaskSign](#tasksign)
  - [TaskSignListQuery](#tasksignlistquery)
  - [TaskSignListResponse](#tasksignlistresponse)
  - [CreateTaskSignRequest](#createtasksignrequest)
  - [UpdateTaskSignRequest](#updatetasksignrequest)
  - [TaskSignActionRequest](#tasksignactionrequest)
  - [TaskSignActionResponse](#tasksignactionresponse)
- [TaskFlow 相關](#taskflow-相關)
  - [TaskFlow](#taskflow)
  - [TaskFlowAction](#taskflowaction)
  - [TaskFlowAddRequest](#taskflowaddrequest)
  - [TaskFlowBackRequest](#taskflowbackrequest)
  - [TaskFlowSubmitRequest](#taskflowsubmitrequest)
  - [TaskFlowCancelRequest](#taskflowcancelrequest)
  - [TaskFlowResponse](#taskflowresponse)
  - [TaskFlowListQuery](#taskflowlistquery)
  - [TaskFlowListResponse](#taskflowlistresponse)
- [TaskComment 相關](#taskcomment-相關)
  - [TaskComment](#taskcomment)
  - [TaskCommentListQuery](#taskcommentlistquery)
  - [TaskCommentListResponse](#taskcommentlistresponse)
  - [CreateTaskCommentRequest](#createtaskcommentrequest)
  - [AddTaskCommentFileRequest](#addtaskcommentfilerequest)
  - [RemoveTaskCommentFileResponse](#removetaskcommentfileresponse)
- [HistoryRecord 相關](#historyrecord-相關)
  - [HistoryRecord](#historyrecord)
  - [RecordFunction](#recordfunction)
  - [HistoryRecordListQuery](#historyrecordlistquery)
  - [HistoryRecordListResponse](#historyrecordlistresponse)
  - [CreateHistoryRecordRequest](#createhistoryrecordrequest)
  - [CreateHistoryRecordResponse](#createhistoryrecordresponse)

## 2. Schema 定義

### DocSpec 相關

#### <a name="doc"></a>Doc
```ts
interface Doc {
  id: string;
  docSpecId: string;
  formData: Record<string, any>;
  authorId: string;
  status: 'Draft' | 'InProgress' | 'Approved' | 'Rejected' | 'Canceled';
  currentSignTaskId: string | null;
  firstSignTaskId: string | null;
  files: object[];
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
}
```

#### <a name="doclistquery"></a>DocListQuery
```ts
interface DocListQuery {
  page?: number; // 頁碼，預設 1
  pageSize?: number; // 單頁數量，預設 20，最大 100
  status?: 'Draft' | 'InProgress' | 'Approved' | 'Rejected' | 'Canceled';
  authorId?: string;
  docSpecId?: string;
}
```

#### <a name="doclistresponse"></a>DocListResponse
```ts
interface DocListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: Doc[];
}
```

#### <a name="createdocrequest"></a>CreateDocRequest
```ts
interface CreateDocRequest {
  docSpecId: string;
  formData: Record<string, any>;
  files: object[];
}
```

#### <a name="updatedocrequest"></a>UpdateDocRequest
```ts
interface UpdateDocRequest {
  formData?: Record<string, any>;
  files?: object[];
}
```

#### <a name="deletedocresponse"></a>DeleteDocResponse
```ts
interface DeleteDocResponse {
  success: boolean;
}
```

#### <a name="docspeclistresponse"></a>DocSpecListResponse
```ts
interface DocSpecListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: DocSpec[];
}
```

#### <a name="createdocspecrequest"></a>CreateDocSpecRequest
```ts
interface CreateDocSpecRequest {
  name: string;
  docForm: {
    items: FormItem[];
  };
  docFlow: {
    stages: FlowItem[];
  };
}
```

#### <a name="updatedocspecrequest"></a>UpdateDocSpecRequest
```ts
interface UpdateDocSpecRequest {
  name?: string;
  docForm?: {
    items?: FormItem[];
  };
  docFlow?: {
    stages?: FlowItem[];
  };
}
```

#### <a name="activatedocspecrequest"></a>ActivateDocSpecRequest
```ts
interface ActivateDocSpecRequest {
  isActive: boolean;
}
```

#### <a name="deletedocspecresponse"></a>DeleteDocSpecResponse
```ts
interface DeleteDocSpecResponse {
  success: boolean;
}
```

#### <a name="docspec"></a>DocSpec
```ts
interface DocSpec {
  id: string;
  name: string;
  docForm: DocForm;
  docFlow: DocFlow;
  isActive: boolean;
}
```

#### <a name="docform"></a>DocForm
```ts
interface DocForm {
  docSpecId: string;
  items: FormItem[];
}
```

#### <a name="formitem"></a>FormItem
```ts
interface FormItem {
  key: string;
  type: string;
  label: string;
  required: boolean;
}
```

#### <a name="docflow"></a>DocFlow
```ts
interface DocFlow {
  docSpecId: string;
  stages: FlowItem[];
}
```

#### <a name="flowitem"></a>FlowItem
```ts
interface FlowItem {
  stageId: string;
  positionId: string;
  defaultSignerId: string;
  preStage: string;
}
```

---

### <a name="historyrecord-相關"></a>HistoryRecord 相關

#### <a name="historyrecord"></a>HistoryRecord
```ts
interface HistoryRecord {
  id: string;
  docId: string;
  taskId: string;
  operatorId: string;
  message: string;
  func: RecordFunction;
  isSuccess: boolean;
  timestamp: string; // ISO date-time
}
```

#### <a name="recordfunction"></a>RecordFunction
```ts
type RecordFunction =
  | 'SignParent'   // 主 TaskSign 簽署
  | 'SignChild'    // 子 TaskSign 簽署
  | 'Undo'         // 簽署還原
  | 'Disable'      // 禁止簽署
  | 'Submit'       // 表單送出
  | 'Cancel'       // 表單取消
  | 'Add'          // 加簽
  | 'Back'         // 退回
  | 'Say';         // 留言
```

#### <a name="historyrecordlistquery"></a>HistoryRecordListQuery
```ts
interface HistoryRecordListQuery {
  docId?: string;
  taskId?: string;
  operatorId?: string;
  func?: RecordFunction;
  isSuccess?: boolean;
  page?: number;
  pageSize?: number;
}
```

#### <a name="historyrecordlistresponse"></a>HistoryRecordListResponse
```ts
interface HistoryRecordListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: HistoryRecord[];
}
```

#### <a name="createhistoryrecordrequest"></a>CreateHistoryRecordRequest
```ts
interface CreateHistoryRecordRequest {
  docId: string;
  taskId: string;
  operatorId: string;
  message: string;
  func: RecordFunction;
  isSuccess: boolean;
}
```

#### <a name="createhistoryrecordresponse"></a>CreateHistoryRecordResponse
```ts
interface CreateHistoryRecordResponse {
  success: boolean;
  historyRecord: HistoryRecord;
}
```

### TaskFlow 相關

#### <a name="taskflow"></a>TaskFlow
```ts
interface TaskFlow {
  id: string;
  docId: string;
  operatorId: string;
  action: TaskFlowAction;
  targetEmployeeId?: string | null;
  reason?: string | null;
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
}
```

#### <a name="taskflowaction"></a>TaskFlowAction
```ts
type TaskFlowAction = 'add' | 'back' | 'submit' | 'cancel';
```

#### <a name="taskflowaddrequest"></a>TaskFlowAddRequest
```ts
interface TaskFlowAddRequest {
  docId: string;
  mainTaskSignId: string;
  assigneeIds: string[]; // 可同時加簽多位
  reason?: string;
}
```

#### <a name="taskflowbackrequest"></a>TaskFlowBackRequest
```ts
interface TaskFlowBackRequest {
  docId: string;
  fromTaskId: string;
  toTaskId: string;
  reason?: string;
}
```

#### <a name="taskflowsubmitrequest"></a>TaskFlowSubmitRequest
```ts
interface TaskFlowSubmitRequest {
  docId: string;
  reason?: string;
}
```

#### <a name="taskflowcancelrequest"></a>TaskFlowCancelRequest
```ts
interface TaskFlowCancelRequest {
  docId: string;
  reason?: string;
}
```

#### <a name="taskflowresponse"></a>TaskFlowResponse
```ts
interface TaskFlowResponse {
  success: boolean;
  taskFlow: TaskFlow;
}
```

#### <a name="taskflowlistquery"></a>TaskFlowListQuery
```ts
interface TaskFlowListQuery {
  docId?: string;
  operatorId?: string;
  action?: TaskFlowAction;
  page?: number;
  pageSize?: number;
}
```

#### <a name="taskflowlistresponse"></a>TaskFlowListResponse
```ts
interface TaskFlowListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: TaskFlow[];
}
```

### TaskSign 相關

#### <a name="tasksign"></a>TaskSign
```ts
interface TaskSign {
  id: string;
  docId: string;
  operatorId: string;
  isChild: boolean;
  parentId?: string | null;
  isDone: boolean;
  state: 'Pending' | 'Agree' | 'Disagree' | 'Disable';
  stageId: string;
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
}
```

#### <a name="tasksignlistquery"></a>TaskSignListQuery
```ts
interface TaskSignListQuery {
  docId?: string;
  operatorId?: string;
  state?: 'Pending' | 'Agree' | 'Disagree' | 'Disable';
  isChild?: boolean;
  page?: number;
  pageSize?: number;
}
```

#### <a name="tasksignlistresponse"></a>TaskSignListResponse
```ts
interface TaskSignListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: TaskSign[];
}
```

#### <a name="createtasksignrequest"></a>CreateTaskSignRequest
```ts
interface CreateTaskSignRequest {
  docId: string;
  operatorId: string;
  isChild?: boolean;
  parentId?: string | null;
  stageId: string;
}
```

#### <a name="updatetasksignrequest"></a>UpdateTaskSignRequest
```ts
interface UpdateTaskSignRequest {
  operatorId?: string;
  isDone?: boolean;
  state?: 'Pending' | 'Agree' | 'Disagree' | 'Disable';
  stageId?: string;
}
```

#### <a name="tasksignactionrequest"></a>TaskSignActionRequest
```ts
interface TaskSignActionRequest {
  action: 'agree' | 'disagree' | 'undo' | 'disable';
  // 可擴充: 例如 reason, comment, etc.
}
```

#### <a name="tasksignactionresponse"></a>TaskSignActionResponse
```ts
interface TaskSignActionResponse {
  success: boolean;
  taskSign: TaskSign;
}
```

### FileAttachment 相關


#### <a name="fileattachment"></a>FileAttachment
```ts
interface FileAttachment {
  id: string;
  docId: string;
  taskId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  url: string;
  uploadedBy: string;
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
  deletedAt?: string | null; // ISO date-time, null 代表未刪除
}
```

#### <a name="fileattachmentlistquery"></a>FileAttachmentListQuery
```ts
interface FileAttachmentListQuery {
  docId?: string;
  taskId?: string;
  page?: number;
  pageSize?: number;
  includeDeleted?: boolean;
}
```

#### <a name="fileattachmentlistresponse"></a>FileAttachmentListResponse
```ts
interface FileAttachmentListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: FileAttachment[];
}
```

#### <a name="createfileattachmentrequest"></a>CreateFileAttachmentRequest
```ts
interface CreateFileAttachmentRequest {
  docId: string;
  taskId: string;
  file: {
    fileName: string;
    fileType: string;
    fileSize: number;
    content: string; // base64 encoded
  };
}
```

#### <a name="deletefileattachmentresponse"></a>DeleteFileAttachmentResponse
```ts
interface DeleteFileAttachmentResponse {
  success: boolean;
}
```

#### <a name="registerrequest"></a>RegisterRequest
```ts
interface RegisterRequest {
  account: string;
  password: string;
  employee?: {
    name: string;
  };
  employeeId?: string;
  // 至少要有 employee 或 employeeId 其中之一
}
```

#### <a name="registerresponse"></a>RegisterResponse
```ts
interface RegisterResponse {
  employee: Employee;
  loginAccount: LoginAccount;
}
```

---

### <a name="taskcomment-相關"></a>TaskComment 相關

#### <a name="taskcomment"></a>TaskComment
```ts
interface TaskComment {
  id: string;
  docId: string;
  taskId: string;
  operatorId: string;
  comment: string;
  files: FileAttachment[];
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
}
```

#### <a name="taskcommentlistquery"></a>TaskCommentListQuery
```ts
interface TaskCommentListQuery {
  docId?: string;
  taskId?: string;
  operatorId?: string;
  page?: number;
  pageSize?: number;
}
```

#### <a name="taskcommentlistresponse"></a>TaskCommentListResponse
```ts
interface TaskCommentListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: TaskComment[];
}
```

#### <a name="createtaskcommentrequest"></a>CreateTaskCommentRequest
```ts
interface CreateTaskCommentRequest {
  docId: string;
  taskId: string;
  operatorId: string;
  comment: string;
  files?: {
    fileName: string;
    fileType: string;
    fileSize: number;
    content: string; // base64 encoded
  }[];
}
```

#### <a name="addtaskcommentfilerequest"></a>AddTaskCommentFileRequest
```ts
interface AddTaskCommentFileRequest {
  fileName: string;
  fileType: string;
  fileSize: number;
  content: string; // base64 encoded
}
```

#### <a name="removetaskcommentfileresponse"></a>RemoveTaskCommentFileResponse
```ts
interface RemoveTaskCommentFileResponse {
  success: boolean;
}
```

## 通用型 Schema

#### <a name="errorresponse"></a>ErrorResponse
```ts
interface ErrorResponse {
  code: string;
  message: string;
}
```

#### <a name="deleteemployeeresponse"></a>DeleteEmployeeResponse
```ts
interface DeleteEmployeeResponse {
  success: boolean;
}
```

#### <a name="deleteassignmentresponse"></a>DeleteAssignmentResponse
```ts
interface DeleteAssignmentResponse {
  success: boolean;
}
```

---

## Auth 相關

#### <a name="loginrequest"></a>LoginRequest
```ts
interface LoginRequest {
  provider: 'local' | 'google' | 'facebook';
  account: string;
  password?: string;
  externalId?: string;
}
```

#### <a name="loginresponse"></a>LoginResponse
```ts
interface LoginResponse {
  token: string;
  employee: Employee;
  loginAccount: LoginAccount;
}
```

#### <a name="logoutresponse"></a>LogoutResponse
```ts
interface LogoutResponse {
  success: boolean;
}
```

#### <a name="getcurrentuserresponse"></a>GetCurrentUserResponse
```ts
interface GetCurrentUserResponse {
  employee: Employee;
  loginAccount: LoginAccount;
}
```

#### <a name="loginaccount"></a>LoginAccount
```ts
interface LoginAccount {
  id: string;
  employeeId: string;
  provider: string;
  account: string;
  externalId?: string;
}
```

---

## Employee 相關

#### <a name="employee"></a>Employee
```ts
interface Employee {
  id: string;
  name: string;
  assignments: EmployeeAssignment[];
  createdAt: string; // ISO date-time
  updatedAt: string; // ISO date-time
}
```

#### <a name="employeelistquery"></a>EmployeeListQuery
```ts
interface EmployeeListQuery {
  page?: number;
  pageSize?: number;
  name?: string;
}
```

#### <a name="employeelistresponse"></a>EmployeeListResponse
```ts
interface EmployeeListResponse {
  total: number;
  page: number;
  pageSize: number;
  items: Employee[];
}
```

#### <a name="createemployeerequest"></a>CreateEmployeeRequest
```ts
interface CreateEmployeeRequest {
  name: string;
  assignments?: {
    departmentId: string;
    positionId: string;
  }[];
}
```

#### <a name="updateemployeerequest"></a>UpdateEmployeeRequest
```ts
interface UpdateEmployeeRequest {
  name?: string;
  assignments?: {
    departmentId: string;
    positionId: string;
  }[];
}
```

---

## Assignment 相關

#### <a name="employeeassignment"></a>EmployeeAssignment
```ts
interface EmployeeAssignment {
  id: string;
  department: Department;
  position: Position;
}
```

#### <a name="addassignmentrequest"></a>AddAssignmentRequest
```ts
interface AddAssignmentRequest {
  departmentId: string;
  positionId: string;
}
```

---

## 部門/職位

#### <a name="department"></a>Department
```ts
interface Department {
  id: string;
  name: string;
}
```

#### <a name="position"></a>Position
```ts
interface Position {
  id: string;
  title: string;
}
```

#### <a name="departmentlistresponse"></a>DepartmentListResponse
```ts
interface DepartmentListResponse {
  departments: Department[];
}
```

#### <a name="positionlistresponse"></a>PositionListResponse
```ts
interface PositionListResponse {
  positions: Position[];
}
```
