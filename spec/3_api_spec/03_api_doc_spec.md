## API 目錄

### 文件類型規格（DocSpec）管理
- GET `/api/doc-specs`：查詢文件類型規格列表
- POST `/api/doc-specs`：建立文件類型規格
- GET `/api/doc-specs/{id}`：查詢單一文件類型規格
- PATCH `/api/doc-specs/{id}`：更新文件類型規格
- PATCH `/api/doc-specs/{id}/activate`：啟用/停用文件類型規格
- DELETE `/api/doc-specs/{id}`：刪除文件類型規格

---

## 文件類型規格 API 詳細規格

### GET `/api/doc-specs`
- **功能說明**：查詢所有文件類型規格，支援分頁與條件篩選（如名稱、啟用狀態）。
- **Request Schema**：無（可擴充查詢參數）
- **Response Schema**：[DocSpecListResponse](./schema.md#docspeclistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "spec01",
      "name": "請假單",
      "docForm": {
        "docSpecId": "spec01",
        "items": [
          { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
          { "key": "endDate", "type": "date", "label": "結束日期", "required": true },
          { "key": "reason", "type": "text", "label": "事由", "required": true }
        ]
      },
      "docFlow": {
        "docSpecId": "spec01",
        "stages": [
          { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
          { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
        ]
      },
      "isActive": true
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [DocSpec](./schema.md#docspec)
- [DocForm](./schema.md#docform)
- [FormItem](./schema.md#formitem)
- [DocFlow](./schema.md#docflow)
- [FlowItem](./schema.md#flowitem)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/doc-specs`
- **功能說明**：建立新的文件類型規格。
- **Request Schema**：[CreateDocSpecRequest](./schema.md#createdocspecrequest)
- **Response Schema**：[DocSpec](./schema.md#docspec)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "name": "出差申請單",
  "docForm": {
    "items": [
      { "key": "destination", "type": "text", "label": "目的地", "required": true },
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true }
    ]
  },
  "docFlow": {
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  }
}
```

**Response 範例**
```json
{
  "id": "spec02",
  "name": "出差申請單",
  "docForm": {
    "docSpecId": "spec02",
    "items": [
      { "key": "destination", "type": "text", "label": "目的地", "required": true },
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true }
    ]
  },
  "docFlow": {
    "docSpecId": "spec02",
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  },
  "isActive": true
}
```

**錯誤範例**
```json
{
  "code": "DUPLICATE_DOC_SPEC",
  "message": "文件類型名稱已存在"
}
```

**本 API 使用的 schema**  
- [DocSpec](./schema.md#docspec)
- [DocForm](./schema.md#docform)
- [FormItem](./schema.md#formitem)
- [DocFlow](./schema.md#docflow)
- [FlowItem](./schema.md#flowitem)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/doc-specs/{id}`
- **功能說明**：查詢單一文件類型規格詳細資料。
- **Response Schema**：[DocSpec](./schema.md#docspec)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "spec01",
  "name": "請假單",
  "docForm": {
    "docSpecId": "spec01",
    "items": [
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true },
      { "key": "reason", "type": "text", "label": "事由", "required": true }
    ]
  },
  "docFlow": {
    "docSpecId": "spec01",
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  },
  "isActive": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此文件類型規格"
}
```

**本 API 使用的 schema**  
- [DocSpec](./schema.md#docspec)
- [DocForm](./schema.md#docform)
- [FormItem](./schema.md#formitem)
- [DocFlow](./schema.md#docflow)
- [FlowItem](./schema.md#flowitem)
- [ErrorResponse](./schema.md#errorresponse)

---

### PATCH `/api/doc-specs/{id}`
- **功能說明**：更新文件類型規格（名稱、表單、流程）。
- **Request Schema**：[UpdateDocSpecRequest](./schema.md#updatedocspecrequest)
- **Response Schema**：[DocSpec](./schema.md#docspec)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "name": "請假單（新版）",
  "docForm": {
    "items": [
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true },
      { "key": "reason", "type": "text", "label": "事由", "required": true },
      { "key": "note", "type": "text", "label": "備註", "required": false }
    ]
  },
  "docFlow": {
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  }
}
```

**Response 範例**
```json
{
  "id": "spec01",
  "name": "請假單（新版）",
  "docForm": {
    "docSpecId": "spec01",
    "items": [
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true },
      { "key": "reason", "type": "text", "label": "事由", "required": true },
      { "key": "note", "type": "text", "label": "備註", "required": false }
    ]
  },
  "docFlow": {
    "docSpecId": "spec01",
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  },
  "isActive": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此文件類型規格"
}
```

**本 API 使用的 schema**  
- [DocSpec](./schema.md#docspec)
- [DocForm](./schema.md#docform)
- [FormItem](./schema.md#formitem)
- [DocFlow](./schema.md#docflow)
- [FlowItem](./schema.md#flowitem)
- [ErrorResponse](./schema.md#errorresponse)

---

### PATCH `/api/doc-specs/{id}/activate`
- **功能說明**：啟用或停用文件類型規格。
- **Request Schema**：[ActivateDocSpecRequest](./schema.md#activatedocspecrequest)
- **Response Schema**：[DocSpec](./schema.md#docspec)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "isActive": false
}
```

**Response 範例**
```json
{
  "id": "spec01",
  "name": "請假單",
  "docForm": {
    "docSpecId": "spec01",
    "items": [
      { "key": "startDate", "type": "date", "label": "開始日期", "required": true },
      { "key": "endDate", "type": "date", "label": "結束日期", "required": true },
      { "key": "reason", "type": "text", "label": "事由", "required": true }
    ]
  },
  "docFlow": {
    "docSpecId": "spec01",
    "stages": [
      { "stageId": "s1", "positionId": "p01", "defaultSignerId": "e001", "preStage": "" },
      { "stageId": "s2", "positionId": "p02", "defaultSignerId": "e002", "preStage": "s1" }
    ]
  },
  "isActive": false
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此文件類型規格"
}
```

**本 API 使用的 schema**  
- [DocSpec](./schema.md#docspec)
- [ErrorResponse](./schema.md#errorresponse)

---

### DELETE `/api/doc-specs/{id}`
- **功能說明**：刪除文件類型規格。
- **Response Schema**：[DeleteDocSpecResponse](./schema.md#deletedocspecresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "success": true
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此文件類型規格"
}
```

**本 API 使用的 schema**  
- [DeleteDocSpecResponse](./schema.md#deletedocspecresponse)
- [ErrorResponse](./schema.md#errorresponse)

---

## 需補充 schema

請於 schema.md 增加下列 schema：

### DocSpecListResponse

```json
{
  "type": "object",
  "properties": {
    "total": { "type": "integer" },
    "page": { "type": "integer" },
    "pageSize": { "type": "integer" },
    "items": {
      "type": "array",
      "items": { "$ref": "#/definitions/DocSpec" }
    }
  },
  "required": ["total", "page", "pageSize", "items"]
}
```

### CreateDocSpecRequest

```json
{
  "type": "object",
  "properties": {
    "name": { "type": "string" },
    "docForm": {
      "type": "object",
      "properties": {
        "items": {
          "type": "array",
          "items": { "$ref": "#/definitions/FormItem" }
        }
      },
      "required": ["items"]
    },
    "docFlow": {
      "type": "object",
      "properties": {
        "stages": {
          "type": "array",
          "items": { "$ref": "#/definitions/FlowItem" }
        }
      },
      "required": ["stages"]
    }
  },
  "required": ["name", "docForm", "docFlow"]
}
```

### UpdateDocSpecRequest

```json
{
  "type": "object",
  "properties": {
    "name": { "type": "string" },
    "docForm": {
      "type": "object",
      "properties": {
        "items": {
          "type": "array",
          "items": { "$ref": "#/definitions/FormItem" }
        }
      }
    },
    "docFlow": {
      "type": "object",
      "properties": {
        "stages": {
          "type": "array",
          "items": { "$ref": "#/definitions/FlowItem" }
        }
      }
    }
  }
}
```

### ActivateDocSpecRequest

```json
{
  "type": "object",
  "properties": {
    "isActive": { "type": "boolean" }
  },
  "required": ["isActive"]
}
```

### DeleteDocSpecResponse

```json
{
  "type": "object",
  "properties": {
    "success": { "type": "boolean" }
  },
  "required": ["success"]
}
