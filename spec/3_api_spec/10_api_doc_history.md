## API 目錄

### 歷程記錄管理
- GET `/api/doc-history`：查詢歷程記錄列表（分頁/篩選）
- POST `/api/doc-history`：建立歷程記錄
- GET `/api/doc-history/{id}`：查詢單一歷程記錄

---

## 歷程記錄 API 詳細規格

### GET `/api/doc-history`
- **功能說明**：查詢歷程記錄列表，支援分頁與多條件篩選（如 docId、taskId、operatorId、func、isSuccess）。
- **Request Schema**：[HistoryRecordListQuery](./schema.md#historyrecordlistquery)
- **Response Schema**：[HistoryRecordListResponse](./schema.md#historyrecordlistresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "func": "SignParent",
  "page": 1,
  "pageSize": 20
}
```

**Response 範例**
```json
{
  "total": 2,
  "page": 1,
  "pageSize": 20,
  "items": [
    {
      "id": "hr001",
      "docId": "d001",
      "taskId": "t001",
      "operatorId": "e001",
      "message": "主簽核同意",
      "func": "SignParent",
      "isSuccess": true,
      "timestamp": "2025-04-14T09:00:00Z"
    },
    {
      "id": "hr002",
      "docId": "d001",
      "taskId": "t002",
      "operatorId": "e002",
      "message": "加簽退回",
      "func": "Back",
      "isSuccess": true,
      "timestamp": "2025-04-14T09:05:00Z"
    }
  ]
}
```

**錯誤範例**
```json
{
  "code": "INVALID_QUERY",
  "message": "查詢參數錯誤"
}
```

**本 API 使用的 schema**  
- [HistoryRecord](./schema.md#historyrecord)
- [RecordFunction](./schema.md#recordfunction)
- [ErrorResponse](./schema.md#errorresponse)

---

### POST `/api/doc-history`
- **功能說明**：建立一筆歷程記錄。通常由系統於 TaskSign、TaskFlow、TaskComment 等操作時自動產生。
- **Request Schema**：[CreateHistoryRecordRequest](./schema.md#createhistoryrecordrequest)
- **Response Schema**：[CreateHistoryRecordResponse](./schema.md#createhistoryrecordresponse)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Request 範例**
```json
{
  "docId": "d001",
  "taskId": "t001",
  "operatorId": "e001",
  "message": "主簽核同意",
  "func": "SignParent",
  "isSuccess": true
}
```

**Response 範例**
```json
{
  "success": true,
  "historyRecord": {
    "id": "hr003",
    "docId": "d001",
    "taskId": "t001",
    "operatorId": "e001",
    "message": "主簽核同意",
    "func": "SignParent",
    "isSuccess": true,
    "timestamp": "2025-04-14T09:10:00Z"
  }
}
```

**錯誤範例**
```json
{
  "code": "INVALID_INPUT",
  "message": "缺少必要欄位"
}
```

**本 API 使用的 schema**  
- [CreateHistoryRecordRequest](./schema.md#createhistoryrecordrequest)
- [CreateHistoryRecordResponse](./schema.md#createhistoryrecordresponse)
- [RecordFunction](./schema.md#recordfunction)
- [ErrorResponse](./schema.md#errorresponse)

---

### GET `/api/doc-history/{id}`
- **功能說明**：查詢單一歷程記錄詳細資料。
- **Response Schema**：[HistoryRecord](./schema.md#historyrecord)
- **錯誤格式**：[ErrorResponse](./schema.md#errorresponse)

**Response 範例**
```json
{
  "id": "hr001",
  "docId": "d001",
  "taskId": "t001",
  "operatorId": "e001",
  "message": "主簽核同意",
  "func": "SignParent",
  "isSuccess": true,
  "timestamp": "2025-04-14T09:00:00Z"
}
```

**錯誤範例**
```json
{
  "code": "NOT_FOUND",
  "message": "查無此歷程記錄"
}
```

**本 API 使用的 schema**  
- [HistoryRecord](./schema.md#historyrecord)
- [RecordFunction](./schema.md#recordfunction)
- [ErrorResponse](./schema.md#errorresponse)

---

## RecordFunction 行為意義

| func         | 說明                         |
|--------------|------------------------------|
| SignParent   | 主 TaskSign 簽署             |
| SignChild    | 子 TaskSign 簽署             |
| Undo         | 簽署還原                     |
| Disable      | 禁止簽署                     |
| Submit       | 表單送出                     |
| Cancel       | 表單取消                     |
| Add          | 加簽                         |
| Back         | 退回                         |
| Say          | 留言（TaskComment 留言）     |

- 歷程記錄可追蹤 TaskSign、TaskFlow、TaskComment 等所有核心操作。
- 每筆歷程皆標示來源、操作人、行為、訊息、成功與否及時間戳記。
