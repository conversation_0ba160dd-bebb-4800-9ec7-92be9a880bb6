# 文件簽核系統 ERD 與資料表設計

## 一、ERD（Mermaid，僅表名與關聯，確保可顯示）

```mermaid
erDiagram
  company ||--o{ company_position : ""
  company_position ||--o{ employee_assignment : ""
  employee ||--o{ employee_assignment : ""
  company_position ||--o{ employee_assignment : ""
  employee ||--o{ login_account : ""
  employee ||--o{ task : "operated_tasks"
  employee ||--o{ doc_history : "operated_doc_historys"
  doc_spec ||--o{ doc : ""
  doc_spec ||--o{ doc_form : ""
  doc_spec ||--o{ doc_flow : ""
  doc ||--o{ doc_file : ""
  doc ||--o{ task : ""
  doc ||--o{ doc_history : ""
  doc ||--o{ task_sign : ""
  doc ||--o{ task_flow : ""
  doc ||--o{ task_comment : ""
  task ||--o{ doc_file : ""
  task ||--o{ task_sign : "TaskToTaskSign"
  task ||--o{ task_flow : "TaskToTaskFlow"
  task ||--o{ task_comment : "TaskToTaskComment"
  task ||--o{ doc_history : ""
  task ||--o{ task_comment : "parent_comments"
  task_sign }o--|| task_sign : "TaskSignParent"
  doc_history }o--|| task : ""
```

---

## 二、資料表與欄位設計

### company
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| name         | text   | not null |

### company_position
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| department  | text   | not null |
| position    | text   | not null |
| company_id  | text   | FK, not null |

### employee
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id            | text   | PK     |
| name          | text   | not null |
| image_url     | text   | nullable |


### employee_assignment
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| employee_id  | text   | FK, not null |
| position_id  | text   | FK, not null |

### login_account
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text     | PK     |
| employee_id  | text     | FK, not null |
| provider     | text     | not null, enum |
| account      | text     | not null |
| password     | text     | nullable |
| external_id  | text     | nullable |

#### provider ENUM
- local
- google
- facebook
- sso

### doc_spec
| 欄位         | 型別      | 屬性   |
|--------------|-----------|--------|
| id           | text      | PK     |
| name         | text      | not null |
| is_active    | boolean   | not null |

### doc_form
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| doc_spec_id  | text   | FK, not null, UNIQUE |
| items        | jsonb  | not null |  // FormItem[] 結構，見下方展開 |

### doc_flow
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| doc_spec_id  | text   | FK, not null, UNIQUE |
| stages       | jsonb  | not null |  // FlowItem[] 結構，見下方展開 |

### doc
| 欄位                 | 型別   | 屬性   |
|----------------------|--------|--------|
| id                   | text   | PK     |
| doc_spec_id          | text   | FK, not null |
| form_data            | jsonb  | not null |
| author_id            | text   | 弱關聯, not null |
| status               | text   | not null, enum |

#### status ENUM
- draft
- in_progress
- approved
- rejected
- canceled

### file_attachment
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| doc_id       | text   | FK, not null |
| task_id      | text   | FK, not null |
| file_name    | text   | not null |
| file_type    | text   | not null |
| file_size    | bigint | not null |
| url          | text   | not null |
| uploaded_by  | text   | 弱關聯, not null |  // 為反正規化
| created_at   | timestamp| not null |
| updated_at   | timestamp| not null |
| deleted_at   | timestamp| nullable |

### task_sign
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| task_id      | text   | PK, FK (task.id) |
| doc_id       | text   | FK, not null |
| operator_id  | text   | 弱關聯, not null |
| action       | text   | not null, enum |
| is_child     | boolean| not null |
| parent_id    | text   | FK, nullable (task_sign.task_id) |
| is_done      | boolean| not null |
| created_at   | timestamp| not null |
| updated_at   | timestamp| not null |

#### action ENUM
- pending
- signing
- agree
- disagree
- disable

### task
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| doc_id       | text   | FK, not null |
| operator_id  | text   | 弱關聯, not null |  // 為反正規劃
| type         | text   | not null, enum |
| created_at   | timestamp| not null |
| updated_at   | timestamp| not null |

#### type ENUM
- sign
- flow
- comment

### task_sign
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| task_id      | text   | PK, FK (task.id) |
| doc_id       | text   | FK, not null |
| operator_id  | text   | 弱關聯, not null |
| action       | text   | not null, enum |
| is_child     | boolean| not null |
| parent_id    | text   | FK, nullable (task_sign.task_id) |
| is_done      | boolean| not null |
| created_at   | timestamp| not null |
| updated_at   | timestamp| not null |

#### action ENUM
- pending
- signing
- agree
- disagree
- disable

### task_flow
| 欄位             | 型別   | 屬性   |
|------------------|--------|--------|
| task_id          | text   | PK, FK (task.id) |
| doc_id           | text   | FK, not null |
| operator_id      | text   | 弱關聯, not null |  // 為反正規劃
| action           | text   | not null, enum |
| target_employee_id| text  | 弱關聯, nullable |
| reason           | text   | nullable |
| created_at       | timestamp| not null |
| updated_at       | timestamp| not null |

#### action ENUM
- submit
- add
- back
- cancel

### task_comment
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| task_id      | text   | PK, FK (task.id) |
| doc_id       | text   | FK, not null |
| parent_task_id| text  | FK, not null (對應評論所屬的任務) |
| operator_id  | text   | 弱關聯, not null |
| comment      | text   | not null |
| created_at   | timestamp| not null |
| updated_at   | timestamp| not null |

### history_record
| 欄位         | 型別   | 屬性   |
|--------------|--------|--------|
| id           | text   | PK     |
| doc_id       | text   | FK, not null |
| task_id      | text   | FK, not null |
| operator_id  | text   | 弱關聯, not null |
| message      | text   | not null |
| func         | text   | not null, enum |
| timestamp    | timestamp| not null |
| is_success   | boolean| not null |

#### func ENUM
- sign_parent
- sign_child
- undo
- disable
- submit
- cancel
- add
- back
- say

---

## 三、FormItem/FlowItem 結構展開

### FormItem（doc_form.items, jsonb 陣列）
| 欄位      | 型別    | 屬性      | 說明                |
|-----------|---------|-----------|---------------------|
| key       | text    | not null  | 欄位識別碼（如 start_date, reason）|
| type      | text    | not null  | 欄位型別（如 text, number, date, ...）|
| label     | text    | not null  | 顯示標籤           |
| required  | boolean | not null  | 是否必填           |

#### 範例
```json
[
  {
    "key": "start_date",
    "type": "date",
    "label": "開始日期",
    "required": true
  }
]
```

### FlowItem（doc_flow.stages, jsonb 陣列）
| 欄位             | 型別    | 屬性      | 說明                        |
|------------------|---------|-----------|-----------------------------|
| stage_id         | text    | not null  | 關卡識別碼                  |
| position_id      | text    | not null  | 負責職位 id                 |
| default_signer_id| text    | not null  | 預設簽核人員 id             |
| pre_stage        | text    | not null  | 前一關 stage_id             |

#### 範例
```json
[
  {
    "stage_id": "manager",
    "position_id": "pos_123",
    "default_signer_id": "emp_456",
    "pre_stage": "applicant"
  }
]
```

---

## 四、設計重點說明

- 所有資料表 id 欄位統一為 `id`，task_sign、task_flow、task_comment 以 `task_id` 為 PK 並為 FK 指向 task。
- doc_form.items、doc_flow.stages 結構已完整展開。
- 「弱關聯」指僅存 id 欄位，無 FK constraint，或業務上非強制參照。表格與 ERD已明確標註。
