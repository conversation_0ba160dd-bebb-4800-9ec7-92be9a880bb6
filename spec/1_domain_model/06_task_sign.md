# TaskSign Domain Model

## 1. Domain 概述

TaskSign 代表文件簽核流程中的「簽核任務」節點，負責記錄每一階段的簽核人、狀態、簽核結果與父子任務關係。  
TaskSign 分為主線任務（Main Task）與加簽子任務（Child Task），支援多層加簽、退回、取消等動作。

## 2. 實體與值物件

- **TaskSign（實體）**
  - 欄位：id, docId, operatorId, isChild, parentId, isDone, state, stageId, createdAt, updatedAt
  - 行為：do, done, disable, undo

- **SignAction（值物件）**
  - 定義：Pending, Agree, Disagree, Disable
  - 用於 TaskSign.state，確保簽核狀態型別安全

## 3. 狀態圖

### 主線任務（Main Task）

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Pending
    Pending --> Signing
    Signing --> Agree: do(agree)
    Signing --> Disagree: do(disagree)

    Pending --> Disable: disable()
    Signing --> Disable: disable()

    Agree --> Pending: undo()
    Disagree --> Pending: undo()
    Agree --> [*]: 終止（流程結束且所有子任務完成）
    Disagree --> [*]: 終止（流程結束且所有子任務完成）
    Disable --> [*]
```

### 子任務（Child Task）

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Pending
    Pending --> Signing
    Signing --> Agree: do(agree) + done()
    Signing --> Disagree: do(disagree) + done()

    Pending --> Disable: disable()
    Signing --> Disable: disable()

    Agree --> Pending: undo()
    Disagree --> Pending: undo()
    Agree --> [*]: 終止（簽完即結束）
    Disagree --> [*]: 終止（簽完即結束）
    Disable --> [*]
```

## 4. 主要行為流程圖（前後對比）

以下針對主線任務（Main Task）與子任務（Child Task），分別補上 do(agree)、do(disagree)、undo、disable 的前後流程圖，並標註 currentTaskSignId。

### 4.1 主線任務（Main Task）

#### do(agree) 前後

**do(agree) 前**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign: Signing]
  end
```

**do(agree) 後**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign: Agree]
  end
```

#### do(disagree) 前後

**do(disagree) 前**

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  T2[TaskSign 2: Agree]
  subgraph currentTaskSignId_Progressing
    T3[TaskSign 3: Signing]
  end
  T4[TaskSign 4: Pending]
  T1 --> T2 --> T3 --> T4
```

**do(disagree) 後**

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  T2[TaskSign 2: Agree]
  subgraph currentTaskSignId_Rejected
    T3[TaskSign 3: Disagree]
  end
  T4[TaskSign 4: Disable]
  T1 --> T2 --> T3 --> T4
```

#### undo 前後

**undo 前**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign: Agree]
  end
```

**undo 後**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign: Pending/Signing]
  end
```

#### disable 前後

**disable 前**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign: Pending/Signing]
  end
```

**disable 後**

```mermaid
flowchart LR
  subgraph currentTaskSignId_Canceled
    T1[TaskSign: Disable]
  end
```

---

## 5. 主要行為（方法）

- `do(action)`：執行簽核動作（同意/不同意），狀態由 Pending 轉為 Agree/Disagree
- `done()`：標記任務完成（主線需所有子任務完成，子任務簽完即結束）
- `disable()`：標記任務無效（如取消、退回後的任務）
- `undo()`：還原狀態（如退回時還原簽核狀態）

## 5. 關聯圖

```mermaid
classDiagram
  class Task {
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
    // 任務型別相關行為
  }
  class TaskSign {
    +isChild
    +parentId
    +isDone
    +state
    +stageId
    +do()
    +done()
    +disable()
    +undo()
  }
  class TaskFlow
  class TaskComment
  class Doc

  Task "1"<|--"1" TaskSign
  Task "1"<|--"1" TaskFlow
  Task "1"<|--"1" TaskComment
  Doc "1" --o "*" Task
  TaskSign "0..1" --> "1" TaskSign : parent
  TaskSign "1" --> "0..*" TaskSign : children
```

## 6. 領域事件

- **TaskSigned**：TaskSign 完成簽核（同意/不同意）
- **TaskDisabled**：任務被取消或標記為無效
- **TaskUndone**：任務狀態被還原

## 7. 設計說明

- TaskSign 為 Doc 聚合下的核心實體，負責記錄每一簽核節點的狀態與結果
- 支援多層加簽（parent/children 結構），可靈活應對複雜簽核流程
- 狀態轉換明確，便於流程控制與歷程追蹤
- 所有狀態變更皆可觸發對應領域事件，支援通知、審計等擴充需求
- 型別安全：SignAction 為值物件，避免狀態錯誤

### 依賴反轉與 Task Interface

- **Task（介面/抽象類）**
  - TaskSign 不直接依賴其他具體任務型別，而是實作 Task interface，與 TaskFlow、TaskComment 等共同遵循同一抽象。
  - 這樣設計可讓流程操作（如 TaskFlow）針對 Task interface 操作，提升彈性與可測試性。
  - 依賴反轉原則（DIP）讓任務聚合之間僅透過 interface 互動，降低耦合度。

#### Task Interface 範例

```typescript
interface Task {
  id: string;
  docId: string;
  operatorId: string;
  createdAt: Date;
  updatedAt: Date;
  // 任務型別相關行為
}
```

- TaskSign、TaskFlow、TaskComment 等皆實作 Task interface。
- 有助於單元測試（可注入 mock 任務）、未來擴充（如新增任務型別）。

### RecordFunction 行為語意補充

- **SignParent**：主 TaskSign（主線簽核節點）進行簽署（agree/disagree/disable）時的歷程記錄。為 TaskFlow 主要的操作點，記錄主節點的簽核決策。
- **SignChild**：子 TaskSign（加簽子節點）進行簽署（agree/disagree/disable）時的歷程記錄。當父節點（主 TaskSign）執行 undo，所有該父節點的子節點都需 undo 變回 Pending。當父節點 reject，所有該父節點的子節點都需變成 Disable，且後續主節點也需 Disable。
- **Undo**：TaskSign 還原簽署動作，將該節點狀態還原為 Pending。典型觸發時機：TaskFlow.Back。
- **Disable**：TaskSign 禁止簽署動作，將狀態設為 Disable。典型觸發時機：TaskFlow.Cancel、TaskSign.do(disagree)。

