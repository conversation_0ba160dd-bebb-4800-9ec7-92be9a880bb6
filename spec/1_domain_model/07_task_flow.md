# TaskFlow Domain Model

## 1. Domain 概述

TaskFlow 代表文件簽核流程中的「流程操作」行為，負責處理加簽（Add）、退回（Back）、送出（Submit）、取消（Cancel）等動作。  
每個 TaskFlow 實例對應一次具體的流程操作，會驅動 TaskSign 狀態變更、產生子任務、或推進/回退主流程。

## 2. 實體與值物件

- **TaskFlow（實體）**
  - 欄位：id, docId, operatorId, action, targetEmployeeId, reason, createdAt, updatedAt
  - 行為：add, back, submit, cancel

- **FlowAction（值物件）**
  - 定義：Submit, Add, Back, Cancel
  - 用於 TaskFlow.action，確保流程操作型別安全

## 3. 主要行為（方法）

- `add(mainTaskSignId, assigneeId)`：於主線 TaskSign 下建立加簽子任務（isChild=true, parentId=mainTaskSignId）
- `back(fromTaskId, toTaskId)`：將流程回退至指定主線 TaskSign，並依序還原主線任務狀態
- `submit()`：送出表單，產生主線 TaskSign
- `cancel()`：將後續尚未簽署的 TaskSign 標記為 Disable

## 4. 狀態與流程圖

### 刪除（Delete）流程（前後對比）

> 僅適用於 Doc.status = Draft，TaskSign 1 為作者簽署

#### 刪除前

```mermaid
flowchart LR
  subgraph currentTaskSignId_Draft
    T1[TaskSign 1: Pending]
  end
```

#### 刪除後

```mermaid
flowchart LR
  subgraph currentTaskSignId_Canceled
    T1[TaskSign 1: Disable]
  end
```

**說明**：
- 僅當 Doc.status 為 Draft 時可執行刪除，且僅有一個 TaskSign 1（作者）。
- 刪除前，TaskSign 1 狀態為 Pending。
- 刪除後，TaskSign 1 狀態變為 Disable，代表草稿任務已被刪除。

### 送出（Submit）流程（前後對比）

#### 送出前

```mermaid
flowchart LR
  subgraph currentTaskSignId_Draft
    T1[TaskSign 1: Signing]
  end
  style T2 stroke-dasharray: 5 5
  style T3 stroke-dasharray: 5 5
  T2[TaskSign 2: Pending 尚未產生]
  T3[TaskSign 3: Pending 尚未產生]
  T1 --> T2 --> T3
```

#### 送出後

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  subgraph currentTaskSignId_Progressing
    T2[TaskSign 2: Signing]
  end
  T3[TaskSign 3: Pending]
  T1 --> T2 --> T3
```

**說明**：
- 送出前，currentTaskSignId_Draft 框包住 TaskSign 1（Pending），Doc.status: Draft，尚未啟動簽核流程。
- 送出後，TaskSign 1 狀態為 Agree（作者已送出），currentTaskSignId_Progressing 框包住 TaskSign 2（Pending），Doc.status: Progressing，後續節點狀態為 Pending。

### 加簽流程（前後對比）

#### 加簽前

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  subgraph currentTaskSignId_Progressing
    T2[TaskSign 2: Signing]
  end
  T3[TaskSign 3: Pending]
  T1 --> T2 --> T3
```

#### 加簽後

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  subgraph currentTaskSignId_Progressing
    T2[TaskSign 2: Agree]
    T2A[子TaskSign2-1: Signing]
    T2B[子TaskSign2-2: Signing]
    T2 --> T2A
    T2 --> T2B
  end
  T3[TaskSign 3: Pending]
  T1 --> T2 --> T3
```

**說明**：
- 「currentTaskSignId」群組內包含主線 TaskSign 2 及其所有加簽子任務（子TaskSign2-1、子TaskSign2-2），每個節點皆標註 state。
- 流程需所有子 TaskSign 完成（狀態由 Pending 變為 Agree）後，才會推進到 TaskSign 3。

### 退回流程（前後對比）

#### 退回前

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  T2[TaskSign 2: Agree]
  subgraph currentTaskSignId_Progressing
    T3[TaskSign 3: Signing]
  end
  T4[TaskSign 4: Pending]
  T1 --> T2 --> T3 --> T4
```

#### 退回後

```mermaid
flowchart LR
  subgraph currentTaskSignId_Progressing
    T1[TaskSign 1: Signing]
  end
  T2[TaskSign 2: Pending]
  T3[TaskSign 3: Pending]
  T4[TaskSign 4: Pending]
  T1 --> T2 --> T3 --> T4
```

**說明**：
- 退回前，流程推進到 TaskSign 3，currentTaskSignId 為 TaskSign 3，前兩節點狀態為 Agree。
- 退回後，流程回退到 TaskSign 1，currentTaskSignId 為 TaskSign 1，後續節點皆 Pending，需重新簽署。
- 若有子 TaskSign（如加簽子任務），這些子 TaskSign 也必須一併變回 Pending 狀態，需重新簽署。

### 取消流程（前後對比）

#### 取消前

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  subgraph currentTaskSignId_Progressing
    T2[TaskSign 2: Signing]
  end
  T3[TaskSign 3: Pending]
  T4[TaskSign 4: Pending]
  T1 --> T2 --> T3 --> T4
```

#### 取消後

```mermaid
flowchart LR
  T1[TaskSign 1: Agree]
  subgraph currentTaskSignId_Canceled
    T2[TaskSign 2: Disable]
  end
  T3[TaskSign 3: Disable]
  T4[TaskSign 4: Disable]
  T1 --> T2 --> T3 --> T4
```

**說明**：
- 取消前，currentTaskSignId 為 TaskSign2，狀態為 Pending，後續節點也為 Pending。
- 取消後，currentTaskSignId 仍為 TaskSign2，但 TaskSign2、3、4 狀態皆為 Disable，流程終止。

## 5. 關聯圖

```mermaid
classDiagram
  class TaskFlow {
    +action
    +targetEmployeeId?
    +reason
    +add()
    +back()
    +submit()
    +cancel()
  }
  class TaskSign
  class TaskComment
  class Task {
    +id
    +docId
    +operatorId
    +taskType
  }
  class Doc
  Doc "1" --o "*" Task
  Task "1"<|--"1" TaskComment
  Task "1"<|--"1" TaskFlow
  Task "1"<|--"1" TaskSign
  TaskFlow "1" -- "*" TaskSign

```

## 6. 領域事件

- **TaskAdded**：加簽成功建立子 TaskSign
- **TaskBacked**：退回流程
- **TaskSubmitted**：送出表單
- **TaskCanceled**：取消流程

## 7. 依賴反轉與 Task Interface

- **Task（介面/抽象類）**
  - TaskFlow 不直接依賴 TaskSign、TaskComment、TaskFlow 等具體實作，而是依賴 Task interface。
  - 這樣設計可讓 TaskFlow 處理多型的任務類型（如簽核、加簽、評論等），並便於擴充新的任務型別。
  - 依賴反轉原則（DIP）讓流程操作與任務行為解耦，提升測試與維護彈性。

### Task Interface 範例

```typescript
interface Task {
  id: string;
  docId: string;
  operatorId: string;
  createdAt: Date;
  updatedAt: Date;
  // 任務型別相關行為
}
```

- TaskSign、TaskFlow、TaskComment 等皆實作 Task interface。
- TaskFlow 的流程操作可針對 Task interface 操作，而不需關心具體任務型別。

## 8. 設計說明

- TaskFlow 封裝所有流程操作，確保流程規則一致性
- 每次操作皆產生對應歷程（HistoryRecord），便於審計與追蹤
- 加簽、退回、取消等動作皆以 TaskFlow 實例觸發，並驅動 TaskSign 狀態變更
- FlowAction 為值物件，確保流程操作型別安全
- 依賴反轉讓 TaskFlow 聚合與任務聚合之間僅透過 interface 互動，降低耦合度
- 有助於單元測試（可注入 mock 任務）、未來擴充（如新增任務型別）
- 可擴充事件機制，支援通知、外部整合等需求

### RecordFunction 行為語意補充

- **Submit**：TaskFlow 送出表單，根據 docSpec.DocFlow 建立後續 TaskSign。
- **Cancel**：TaskFlow 取消表單，若文件尚未簽署完成，則 Disable 後續所有 TaskSign，並將 doc.status 設為 Canceled。
- **Add**：TaskFlow 加簽，於主 TaskSign 節點下建立多個子 TaskSign。所有子 TaskSign 簽核完畢後，流程才可推進至下一主 TaskSign。
- **Back**：TaskFlow 退回，將流程退回至指定主 TaskSign，該主 TaskSign 及其子 TaskSign 皆需重新簽署。
