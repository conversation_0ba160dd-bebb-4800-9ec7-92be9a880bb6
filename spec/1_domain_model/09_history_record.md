# DocHistory Domain Model（RecordFunction 補充說明）

## 1. RecordFunction 行為定義與對應

RecordFunction 用於記錄 Task 之間彼此的行為，明確標示每一筆歷程的來源與意義。  
其設計涵蓋 TaskSign（主/子）、TaskFlow、TaskComment 的所有核心操作：

### TaskSign 相關

- **SignParent**  
  - 主 TaskSign（主線簽核節點）進行簽署（agree/disagree/disable）時的歷程記錄。
  - 為 TaskFlow 主要的操作點，記錄主節點的簽核決策。

- **SignChild**  
  - 子 TaskSign（加簽子節點）進行簽署（agree/disagree/disable）時的歷程記錄。
  - 當父節點（主 TaskSign）執行 undo，所有該父節點的子節點都需 undo 變回 Pending。
  - 當父節點 reject，所有該父節點的子節點都需變成 Disable，且後續主節點也需 Disable。

- **Undo**  
  - TaskSign 還原簽署動作，將該節點狀態還原為 Pending。
  - 典型觸發時機：TaskFlow.Back。

- **Disable**  
  - TaskSign 禁止簽署動作，將狀態設為 Disable。
  - 典型觸發時機：TaskFlow.Cancel、TaskSign.do(disagree)。

### TaskFlow 相關

- **Submit**  
  - TaskFlow 送出表單，根據 docSpec.DocFlow 建立後續 TaskSign。

- **Cancel**  
  - TaskFlow 取消表單，若文件尚未簽署完成，則 Disable 後續所有 TaskSign，並將 doc.status 設為 Canceled。

- **Add**  
  - TaskFlow 加簽，於主 TaskSign 節點下建立多個子 TaskSign。
  - 所有子 TaskSign 簽核完畢後，流程才可推進至下一主 TaskSign。

- **Back**  
  - TaskFlow 退回，將流程退回至指定主 TaskSign，該主 TaskSign 及其子 TaskSign 皆需重新簽署。

### TaskComment 相關

- **Say**  
  - TaskComment 創建留言，留言可夾帶檔案，留言後不可撤回，也不會有 FileMoveFile 行為。

## 2. RecordFunction 與歷程記錄的關聯圖

```mermaid
classDiagram
  class DocHistory {
    +id
    +docId
    +taskId
    +operatorId
    +message
    +func: RecordFunction
    +timestamp
    +isSuccess
    +log()
    +success()
  }
  class Doc
  class Task {
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
    // 任務型別相關行為
  }
  class TaskSign
  class TaskFlow
  class TaskComment

  Doc "1" --o "*" DocHistory
  Task "1" --o "*" DocHistory
  Task <|-- TaskSign
  Task <|-- TaskFlow
  Task <|-- TaskComment
```

## 3. 設計說明補充

- RecordFunction 以 enum 或嚴格型別定義，確保歷程記錄的語意明確且可追蹤。
- 每一筆歷程都能精確對應到 TaskSign（主/子）、TaskFlow、TaskComment 的具體行為與狀態轉換。
- Undo/Disable 等複合行為，會 cascade 影響相關子節點或後續節點，歷程記錄需完整反映這些連鎖操作。
- Say 行為僅記錄留言與附件，不支援撤回或檔案移動。
