# Domain Model Overview

本文件說明本系統的核心領域模型設計，涵蓋文件（Doc）簽核流程的主要聚合、實體、值物件、服務與事件。  
設計目標為以 DDD（Domain-Driven Design）與 OOP 為基礎，確保業務規則、流程彈性與可維護性。

## 1. 領域核心

- **Doc（文件）**：聚合根，代表一份具體的業務文件（如請假單、請購單），管理其生命週期、簽核流程與歷程。
- **DocSpec（文件類型規格）**：定義文件的表單結構與簽核流程規則。
- **TaskSign（簽核任務）**：代表每一簽核節點，支援主線與加簽子任務。
- **TaskFlow（流程操作）**：封裝加簽、退回、取消等流程動作。
- **HistoryRecord（歷程）**：記錄所有操作與狀態變更。

## 2. 設計重點

- **聚合根與邊界**：Doc 為聚合根，負責協調所有相關實體與流程。
- **狀態管理**：明確定義 Doc、TaskSign 的狀態轉換與流程推進。
- **彈性流程**：支援加簽、退回、取消等動態流程調整。
- **完整歷程**：所有操作皆有歷程記錄，便於審計與追蹤。
- **值物件/服務/事件**：以值物件強化型別安全，服務封裝複雜邏輯，事件驅動狀態變更與外部整合。
- **行為語意標準化（RecordFunction）**：所有 TaskSign、TaskFlow、TaskComment 的核心行為（如簽署、加簽、退回、留言等）皆以 RecordFunction 統一標記，並記錄於 HistoryRecord。RecordFunction 作為全域行為語意的標準，確保歷程記錄的語意一致、可追蹤，並支援複雜流程的審計與還原。

---

## 3. 全域類別圖

```mermaid
classDiagram
  %% 組織結構
  class Company {
    +id
    +name
    +departments
  }
  class Department {
    +id
    +name
    +positions
  }
  class Position {
    +id
    +title
  }
  class EmployeeAssignment {
    +id
    +department
    +position
    +employee
  }
  class Employee {
    +id
    +name
    +assignments
    +addAssignment()
    +removeAssignment()
    +getDepartments()
    +getPositions()
  }
  class LoginAccount {
    +id
    +employeeId
    +provider
    +account
    +password
    +externalId
    +verifyPassword()
    +bindToEmployee()
    +unbind()
  }

  %% 文件與流程
  class DocSpec {
    +id
    +name
    +DocForm
    +DocFlow
    +isActive
  }
  class DocForm {
    +docSpecId
    +items
  }
  class FormItem {
    +key
    +type
    +label
    +required
  }
  class DocFlow {
    +docSpecId
    +stages
  }
  class FlowItem {
    +stageId
    +positionId
    +defaultSignerId
    +preStage
  }
  class Doc {
    +id
    +docSpecId
    +formData
    +authorId
    +status
    +currentSignTaskId
    +firstSignTaskId
    +files
    +$submit()
    +$sign()
    +$add()
    +$back()
    +$cancel()
    +$delete()
    +$comment()
    +addFile()
    +removeFile()
    +log()
    +history()
  }
  class FileAttachment {
    +id
    +docId
    +taskId
    +fileName
    +fileType
    +fileSize
    +url
    +uploadedBy
    +createdAt
    +updatedAt
    +deletedAt
    +softDelete()
    +isValid()
  }
  class Task {
    <<interface>>
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
  }
  class TaskSign {
    +isChild
    +parentId
    +isDone
    +state
    +stageId
    +do()
    +done()
    +disable()
    +undo()
  }
  class TaskFlow {
    +action
    +targetEmployeeId
    +reason
    +add()
    +back()
    +submit()
    +cancel()
  }
  class TaskComment {
    +id
    +docId
    +taskId
    +operatorId
    +comment
    +files
    +write()
    +addFile()
    +removeFile()
    +createdAt
    +updatedAt
  }
  class HistoryRecord {
    +id
    +docId
    +taskId
    +operatorId
    +message
    +func
    +timestamp
    +isSuccess
    +log()
    +success()
  }

  %% 關聯
  Company "1" o-- "*" Department : departments
  Department "1" o-- "*" Position : positions
  EmployeeAssignment "*" o-- "1" Department : department
  EmployeeAssignment "*" o-- "1" Position : position
  EmployeeAssignment "*" o-- "1" Employee : employee
  Employee "1" o-- "*" EmployeeAssignment : assignments
  Employee "1" o-- "*" LoginAccount : accounts
  LoginAccount "*" o-- "1" Employee : employee

  DocSpec "1" o-- "1" DocForm : 組成
  DocSpec "1" o-- "1" DocFlow : 組成
  DocForm "1" o-- "*" FormItem : 擁有
  DocFlow "1" o-- "*" FlowItem : 擁有
  DocSpec "1" --o "*" Doc
  Doc "1" --o "*" Task
  Doc "1" --o "*" FileAttachment : files
  Doc "1" --o "*" HistoryRecord
  Task <|-- TaskSign
  Task <|-- TaskFlow
  Task <|-- TaskComment
  TaskComment "1" --o "*" FileAttachment : files
  Task "1" --o "*" HistoryRecord
  TaskSign "0..1" --> "1" TaskSign : parent
  TaskSign "1" --> "0..*" TaskSign : children

```

---

## 4. 文件結構對應

- [auth.md](./auth.md)：帳號與員工綁定、登入方式
- [employee.md](./employee.md)：組織結構、員工多部門多職位
- [doc_type.md](./doc_type.md)：文件類型規格、表單與流程定義
- [doc.md](./doc.md)：文件聚合根、核心行為
- [file_attachment.md](./file_attachment.md)：檔案附件、歸屬與軟刪除
- [task_sign.md](./task_sign.md)：簽核任務、狀態管理
- [task_flow.md](./task_flow.md)：流程操作、加簽/退回/取消
- [task_comment.md](./task_comment.md)：評論任務、留言與附件
- [history_record.md](./history_record.md)：歷程記錄、行為語意標準化
