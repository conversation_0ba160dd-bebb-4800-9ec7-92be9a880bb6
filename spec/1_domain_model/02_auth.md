# Auth Domain Model

## 1. Domain 概述

Auth（身份驗證）領域負責管理系統使用者的帳號、登入方式與員工（Employee）之間的關聯。支援多種登入方式（本地帳號、第三方 OAuth，如 Google、Facebook），並確保帳號與員工資料的正確綁定與驗證。

---

## 2. 主要實體與值物件

### 2.1 LoginAccount（登入帳號）

- **id**: string — 帳號唯一識別碼
- **employeeId**: string — 對應員工（Employee）id
- **provider**: string — 登入方式（local, google, facebook, ...）
- **account**: string — 帳號名稱（如 username、email、第三方 id）
- **password**: string — 密碼雜湊（僅 local provider 使用）
- **externalId**: string — 第三方唯一識別（如 Google/Facebook id，僅第三方 provider 使用）

#### 行為
- 驗證密碼（local）
- 綁定/解除第三方帳號
- 查詢帳號資訊

### 2.2 Employee（員工）

- **id**: string — 員工唯一識別碼
- **name**: string — 員工姓名

#### 關聯
- 一個 Employee 可綁定多個 LoginAccount（多種登入方式）
- 一個 LoginAccount 僅對應一個 Employee

---

## 3. 聚合根與不變性

- **Employee** 作為聚合根，管理其所有 LoginAccount。
- 不變性：
  - LoginAccount 必須綁定有效的 Employee。
  - 同一 provider 下，account/ externalId 必須全域唯一。
  - 一個 Employee 可同時綁定多個不同 provider 的帳號，但同 provider 僅能有一組帳號。

---

## 4. 典型行為與流程

### 4.1 登入驗證

- local provider：以 account + password 進行驗證。
- 第三方 provider：以 externalId 驗證，若首次登入則可自動建立 LoginAccount 並綁定 Employee。

### 4.2 綁定/解除第三方帳號

- 員工可於個人設定頁綁定/解除第三方登入方式。
- 綁定時需驗證第三方帳號的唯一性與有效性。

### 4.3 查詢帳號

- 依 employeeId 查詢所有綁定的 LoginAccount。
- 依 provider + account/externalId 查詢對應 Employee。

---

## 5. 類別圖

```mermaid
classDiagram
  class Employee {
    +string id
    +string name
    +addLoginAccount()
    +removeLoginAccount()
    +getLoginAccounts()
  }
  class LoginAccount {
    +string id
    +string employeeId
    +string provider
    +string account
    +string password
    +string externalId
    +verifyPassword()
    +bindToEmployee()
    +unbind()
  }
  Employee "1" o-- "*" LoginAccount
  LoginAccount "*" o-- "1" Employee
```

---

## 6. 設計說明

- 採用聚合根（Employee）管理帳號，確保帳號與員工資料一致性。
- 支援多種登入方式，便於擴充（如 SSO、企業認證）。
- 密碼僅於 local provider 下儲存，第三方 provider 以 externalId 驗證。
- 可依需求擴充登入日誌、帳號狀態（啟用/停用）、多因素驗證等功能。
