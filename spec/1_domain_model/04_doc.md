# Doc Domain Model

## 1. Domain 概述

Doc（文件）為本系統的核心聚合根，代表一份具體的業務文件（如請假單、請購單），管理其生命週期、簽核流程、加簽/退回/取消等動作，以及完整的歷程記錄。  
每份 Doc 皆為 DocSpec 的實體化，DocSpec 決定表單結構（doc_form）與簽核流程（doc_flow）。

## 2. 聚合根與核心實體

- **Doc（聚合根）**
  - 欄位：id, docSpecId, formData, authorId, status, currentSignTaskId, firstSignTaskId, files: FileAttachment[]
  - 行為：$submit, $sign, $add, $back, $cancel, $delete, $comment, addFile, removeFile, log, history
  - 管理所有與該文件相關的 TaskSign、TaskFlow、HistoryRecord、FileAttachment

- **DocSpec（實體）**
  - 欄位：id, name, DocForm, DocFlow, isActive
  - 行為：定義表單結構與簽核流程

## 3. 主要值物件

- **FormItem**：表單欄位定義（key, type, label, required）
- **FlowItem**：簽核流程節點定義（stageId, positionId, defaultSignerId, preStage）
- **DocFormData**：表單資料的不可變結構，key/value 對應 DocSpec.DocForm
- **DocumentStatus**：Draft, InProgress, Approved, Rejected, Canceled

## 4. 狀態圖

### Doc 狀態圖

```mermaid
stateDiagram-v2
    direction LR
    [*] --> Draft
    Draft --> InProgress: $submit
    Draft --> Canceled: $delete / $cancel
    InProgress --> Approved: 所有簽核同意
    InProgress --> Rejected: 任一簽核不同意
    InProgress --> Canceled: $cancel
    Approved --> [*]
    Rejected --> [*]
    Canceled --> [*]
```

## 5. 主要行為（方法）

- `$submit(operatorId)`：送出表單，依 DocSpec.DocFlow 產生主線 TaskSign，狀態轉為 InProgress。
- `$sign(operatorId, action)`：執行簽核（同意/不同意），推進流程或結束文件。
- `$add(assignerId, assigneeId)`：加簽，於當前主線 TaskSign 下建立子 TaskSign。
- `$back(operatorId, toOperatorId)`：退回，將流程回退至指定主線 TaskSign。
- `$cancel(operatorId)`：取消，將後續未完成 TaskSign 標記為 Disable，文件狀態轉為 Canceled。
- `$delete(operatorId)`：刪除草稿。
- `$comment(operatorId, comment)`：新增評論。
- `log()/history()`：查詢歷程。

## 6. 關聯圖（Domain Object Diagram）

```mermaid
classDiagram
  class DocSpec {
    +id
    +name
    +DocForm
    +DocFlow
  }
  class Doc {
    +id
    +docSpecId
    +formData
    +authorId
    +status
    +currentSignTaskId
    +firstSignTaskId
    +files
    +$submit()
    +$sign()
    +$add()
    +$back()
    +$cancel()
    +$delete()
    +$comment()
    +addFile()
    +removeFile()
    +log()
    +history()
  }
  class Task {
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
    // 任務型別相關行為
  }
  class FileAttachment {
    +id
    +docId
    +taskId
    +fileName
    +fileType
    +fileSize
    +url
    +uploadedBy
    +createdAt
    +updatedAt
    +deletedAt
    +softDelete()
    +isValid()
  }
  class TaskSign
  class TaskFlow
  class TaskComment

  DocSpec "1" --o "*" Doc
  Doc "1" --o "*" Task
  Doc "1" --o "*" FileAttachment : files
  Task <|-- TaskSign
  Task <|-- TaskFlow
  Task <|-- TaskComment
```

## 7. 領域服務（Domain Service）

- **DocFlowService**
  - 根據 DocSpec.DocFlow 產生主線 TaskSign
  - 提供流程推進、查找下一/前一簽核節點、驗證流程合法性

- **HistoryService**
  - 統一記錄所有歷程點，提供查詢與審計功能

- **NotificationService（可選）**
  - 當有簽核、加簽、退回等事件時，發送通知給相關人員

## 8. 領域事件（Domain Event）

- **DocSubmitted**：Doc 由 Draft 轉為 InProgress
- **TaskSigned**：TaskSign 完成簽核（同意/不同意）
- **TaskAdded**：加簽成功建立子 TaskSign
- **TaskBacked**：退回流程
- **DocApproved / DocRejected / DocCanceled**：Doc 狀態最終轉為 Approved/Rejected/Canceled
- **CommentAdded**：新增評論

## 9. 設計說明

- **聚合邊界**：Doc 為聚合根，管理所有 TaskSign、TaskFlow、HistoryRecord、FileAttachment，確保流程一致性與完整性
- **DocType 與 Doc**：DocType 為靜態定義，Doc 為其動態實體化。DocType 決定 Doc 的表單結構與簽核流程
- **檔案附件**：Doc 可聚合多個 FileAttachment，欄位包含 id、docId、taskId、fileName、fileType、fileSize、url、uploadedBy、createdAt、updatedAt、deletedAt，並以 addFile、removeFile 操作，附件一旦文件送出（非 Draft）僅允許軟刪除
- **狀態管理**：Doc 狀態由 TaskSign 流程推進，所有主線與加簽任務完成後自動轉為 Approved，任一不同意則轉為 Rejected
- **歷程追蹤**：所有操作皆記錄於 HistoryRecord，便於審計與查詢
- **事件驅動**：所有重要狀態變更皆以事件釋出，便於後續擴充（如通知、審計、外部整合）
