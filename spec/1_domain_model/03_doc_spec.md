# DocSpec（文件類型規格）Domain Model

## 1. Domain 概念

DocSpec（文件類型規格）負責定義一類文件的表單結構（DocForm）與簽核流程（DocFlow），是所有文件（Doc）建立與簽核規則的依據。每一份 DocSpec 會對應一組 DocForm 與 DocFlow，並可設定啟用狀態。

## 2. 結構說明

- **DocSpec**：文件類型規格，聚合 DocForm 與 DocFlow。
- **DocForm**：表單欄位集合，描述該類型文件需填寫的欄位。
- **DocFlow**：簽核流程集合，描述該類型文件的簽核關卡與規則。
- **FormItem**：單一表單欄位定義。
- **FlowItem**：單一簽核流程節點定義。

> 補充：FormItem[] (items) 與 FlowItem[] (stages) 在資料庫中分別作為 DocForm、DocFlow 的 JSON 欄位儲存。

## 3. TypeScript 型別定義

```typescript
// 單一表單欄位
export type FormItem = {
  key: string;        // 欄位識別碼（如 startDate, reason）
  type: string;       // 欄位型別（如 text, number, date）
  label: string;      // 顯示標籤
  required: boolean;  // 是否必填
}

// 單一簽核流程節點
export type FlowItem = {
  stageId: string;        // 關卡識別碼
  positionId: string;     // 負責職位 id
  defaultSignerId: string;// 預設簽核人員 id
  preStage: string;       // 前一關 stageId
}

// 表單欄位集合
export class DocForm {
  docSpecId: string;   // 所屬 DocSpec 識別碼
  items: FormItem[];   // 表單欄位陣列

  constructor(docSpecId: string, items: FormItem[]) {
    this.docSpecId = docSpecId;
    this.items = items;
  }
  // 可擴充驗證、查詢等方法
}

// 簽核流程集合
export class DocFlow {
  docSpecId: string;   // 所屬 DocSpec 識別碼
  stages: FlowItem[];  // 簽核流程節點陣列

  constructor(docSpecId: string, stages: FlowItem[]) {
    this.docSpecId = docSpecId;
    this.stages = stages;
  }
  // 可擴充取得第一關、下一關等方法
}

// 文件類型規格
export class DocSpec {
  id: string;         // DocSpec 識別碼
  name: string;       // 類型名稱
  DocForm: DocForm;   // 表單欄位集合
  DocFlow: DocFlow;   // 簽核流程集合
  isActive: boolean;  // 是否啟用

  constructor(id: string, name: string, DocForm: DocForm, DocFlow: DocFlow, isActive: boolean = true) {
    this.id = id;
    this.name = name;
    this.DocForm = DocForm;
    this.DocFlow = DocFlow;
    this.isActive = isActive;
  }
  // 可擴充驗證、查詢等方法
}
```

## 4. 關聯圖

```mermaid
classDiagram
  class DocSpec {
    +string id            // DocSpec 識別碼
    +string name          // 類型名稱
    +DocForm DocForm      // 表單欄位集合
    +DocFlow DocFlow      // 簽核流程集合
    +bool isActive        // 是否啟用
  }
  class DocForm {
    +string docSpecId     // 所屬 DocSpec 識別碼
    +FormItem[] items     // 表單欄位陣列
  }
  class DocFlow {
    +string docSpecId     // 所屬 DocSpec 識別碼
    +FlowItem[] stages    // 簽核流程節點陣列
  }
  class FormItem {
    +string key           // 欄位識別碼
    +string type          // 欄位型別
    +string label         // 顯示標籤
    +bool required        // 是否必填
  }
  class FlowItem {
    +string stageId        // 關卡識別碼
    +string positionId     // 負責職位 id
    +string defaultSignerId// 預設簽核人員 id
    +string preStage       // 前一關 stageId
  }

  DocSpec "1" o-- "1" DocForm : 組成
  DocSpec "1" o-- "1" DocFlow : 組成
  DocForm "1" o-- "*" FormItem : 擁有
  DocFlow "1" o-- "*" FlowItem : 擁有
```

## 5. 設計重點

- DocSpec 聚合 DocForm、DocFlow，確保一份規格對應一組表單與流程。
- DocForm、DocFlow 皆有 docSpecId，明確標示歸屬。
- FormItem、FlowItem 為 Value Object，僅作為集合元素存在。
- 可於 DocSpec、DocForm、DocFlow 擴充驗證、查詢、流程操作等 domain 行為。
- 結構設計利於未來動態擴充、版本控管與多型態文件支援。
