# TaskComment Domain Model

## 1. Domain 概述

TaskComment 代表文件簽核流程中的「評論任務」，用於記錄審核過程中的留言、意見或補充說明。  
每一則 TaskComment 皆關聯至特定任務（如 TaskSign），並記錄評論內容、作者、時間等資訊。  
TaskComment 支援夾帶多個檔案附件，便於補充證明、參考資料等。

## 2. 實體與值物件

- **TaskComment（實體）**
  - 欄位：id, docId, taskId, operatorId, comment, files: DocFile[], createdAt, updatedAt
  - 行為：write, addFile, removeFile

- **CommentContent（值物件）**
  - 定義：評論內容（string），可加上長度、格式等驗證

- **DocFile（實體）**
  - 欄位：
    - id: string（檔案唯一識別碼）
    - docId: string（所屬文件 id）
    - taskId: string（所屬任務 id，評論時為該 TaskComment 的 taskId）
    - fileName: string（檔案名稱）
    - fileType: string（MIME type）
    - fileSize: number（檔案大小，bytes）
    - url: string（存取路徑）
    - uploadedBy: string（上傳者 id）
    - createdAt: Date
    - updatedAt: Date
    - deletedAt?: Date（軟刪除時間，null 代表未刪除）
  - 行為：
    - softDelete()：軟刪除（標記 deletedAt）
    - isValid()：檢查檔案型態、大小等規則

## 3. 主要行為（方法）

- `write(comment: string)`：新增評論內容，並記錄於歷程
- `addFile(file: DocFile)`：為評論新增附件（taskId 為該 TaskComment 的 taskId）
- `removeFile(fileId: string)`：軟刪除指定附件（標記 deletedAt，不物理刪除）

## 4. 關聯圖

```mermaid
classDiagram
  class Task {
    +id
    +docId
    +operatorId
    +crem ''
    +updatedAt
    // 任務型別相關行為
  }
  class TaskComment {
    +id
    +docId
    +taskId
    +operatorId
    +comment
    +files
    +write()
    +addFile()
    +removeFile()
    +createdAt
    +updatedAt
  }
  class DocFile {
    +id
    +docId
    +taskId
    +fileName
    +fileType
    +fileSize
    +url
    +uploadedBy
    +createdAt
    +updatedAt
    +deletedAt
    +softDelete()
    +isValid()
  }
  class TaskFlow
  class TaskSign
  class Doc

  Doc "1" --o "*" Task
  Task <|-- TaskComment
  Task <|-- TaskFlow
  Task <|-- TaskSign
  TaskComment "1" --o "*" DocFile : files
```

## 5. 領域事件

- **CommentAdded**：新增評論
- **FileAttached**：評論新增附件
- **FileRemoved**：評論移除附件

## 6. 依賴反轉與 Task Interface

- **Task（介面/抽象類）**
  - TaskComment 不直接依賴其他具體任務型別，而是實作 Task interface，與 TaskSign、TaskFlow 等共同遵循同一抽象。
  - 這樣設計可讓流程操作（如 TaskFlow）針對 Task interface 操作，提升彈性與可測試性。
  - 依賴反轉原則（DIP）讓任務聚合之間僅透過 interface 互動，降低耦合度。

### Task Interface 範例

```typescript
interface Task {
  id: string;
  docId: string;
  operatorId: string;
  createdAt: Date;
  updatedAt: Date;
  // 任務型別相關行為
}
```

- TaskSign、TaskFlow、TaskComment 等皆實作 Task interface。
- 有助於單元測試（可注入 mock 任務）、未來擴充（如新增任務型別）。

## 7. 設計說明

- TaskComment 為 Doc 聚合下的評論實體，支援審核過程中多次留言與多個附件
- 評論內容可驗證格式、長度，確保資料品質
- 附件資訊以 DocFile 實體封裝，欄位包含 id、docId、taskId、fileName、fileType、fileSize、url、uploadedBy、createdAt、updatedAt、deletedAt，確保型別安全與資料一致性
- 所有評論與附件操作皆可觸發對應領域事件，便於通知、審計等擴充需求
- 依賴反轉設計讓 TaskComment 可與其他任務型別共用流程操作邏輯，提升彈性與可維護性
- 一旦 TaskComment 建立後，附件僅允許軟刪除（deletedAt 標記），不可物理刪除或修改，確保歷程可追溯

### RecordFunction 行為語意補充

- **Say**：TaskComment 創建留言，留言可夾帶檔案，留言後不可撤回，也不會有 FileMoveFile 行為。每次留言都會產生一筆 RecordFunction=Say 的歷程記錄。
