# Employee Domain Model

## 1. Domain 概述

Employee（員工）為組織結構的核心實體，代表公司內的每一位成員。  
本模型支援員工同時於多個部門、擔任多個職位，並以 EmployeeAssignment 作為三元關聯，完整描述員工的任職情境。  
Employee 聚合其所有 EmployeeAssignment，並可查詢所有任職部門與職位。

---

## 2. 實體與值物件

- **Employee（實體）**
  - 欄位：id, name, assignments
  - 行為：addAssignment, removeAssignment, getDepartments, getPositions

- **EmployeeAssignment（實體）**
  - 欄位：id, employee, department, position

- **Department（實體，外部關聯）**
  - 欄位：id, name

- **Position（實體，外部關聯）**
  - 欄位：id, title

---

## 3. 主要行為（方法）

- `addAssignment(assignment: EmployeeAssignment)`：新增任職關聯
- `removeAssignment(assignmentId: string)`：移除任職關聯
- `getDepartments(): Department[]`：查詢所有任職部門
- `getPositions(): Position[]`：查詢所有任職職位

---

## 4. 類別圖

```mermaid
---
title: Employee example
---
classDiagram
  class Employee {
      +string id
      +string name
      +EmployeeAssignment[] assignments
      +addAssignment()
      +removeAssignment()
      +getDepartments()
      +getPositions()
  }
  class EmployeeAssignment {
      +string id
      +Employee employee
      +Position position
  }
  class Position {
      +string id
      +string department
      +string position
  }
  Employee "1" o-- "*" EmployeeAssignment : assignments
  EmployeeAssignment "*" o-- "1" Employee : employee
  EmployeeAssignment "*" o-- "1" Position : position
```

---

## 5. 設計說明

- **Employee** 為聚合根，聚合所有 EmployeeAssignment。
- **EmployeeAssignment** 為三元關聯，連結 Employee、Department、Position，支援多部門多職位。
- 所有查詢與管理行為皆封裝於 Employee 實體，符合 DDD 聚合原則。
- 可依據實際需求擴充 Employee 欄位（如 email、phone 等）。
- 不涉及登入帳號（LoginAccount），其 domain 於 auth.md 處理。

---
