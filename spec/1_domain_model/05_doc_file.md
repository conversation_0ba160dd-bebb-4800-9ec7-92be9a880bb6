# DocFile Domain Model

## 1. Domain 概述

DocFile 代表系統中「文件附件」的唯一領域模型，封裝單一檔案的屬性與存取資訊。  
Doc（文件）與 TaskComment（評論任務）皆可夾帶多個 DocFile，並以 docId、taskId 明確標記歸屬。  
一旦文件送出（非 Draft）或 TaskComment 建立後，檔案僅允許軟刪除（soft delete），不可物理刪除或修改，確保歷程可追溯。

## 2. 實體設計

- **DocFile（實體）**
  - 欄位：
    - id: string（檔案唯一識別碼）
    - docId: string（所屬文件 id）
    - taskId: string（所屬任務 id，Draft 時為作者的 TaskSign，評論時為該 TaskComment 的 taskId）
    - fileName: string（檔案名稱）
    - fileType: string（MIME type）
    - fileSize: number（檔案大小，bytes）
    - url: string（存取路徑）
    - uploadedBy: string（上傳者 id）
    - createdAt: Date
    - updatedAt: Date
    - deletedAt?: Date（軟刪除時間，null 代表未刪除）
  - 行為：
    - softDelete()：軟刪除（標記 deletedAt）
    - isValid()：檢查檔案型態、大小等規則

## 3. 聚合根與關聯

- **Doc（聚合根）**
  - 欄位：id, ... , files: DocFile[]
  - 行為：addFile(file: DocFile), removeFile(fileId: string)

- **Task（介面/抽象類）**
  - 欄位：id, docId, operatorId, createdAt, updatedAt
  - TaskSign、TaskComment 皆實作 Task

- **TaskComment（實體）**
  - 欄位：id, docId, operatorId, createdAt, updatedAt, files: DocFile[]
  - 行為：addFile(file: DocFile), removeFile(fileId: string)

- **TaskSign（實體）**
  - 欄位：id, docId, operatorId, createdAt, updatedAt
  - 無 addFile/removeFile 行為

## 4. 關聯圖（Domain Object Diagram）

```mermaid
classDiagram
  class Doc {
    +id
    +files
    +addFile()
    +removeFile()
  }
  class Task {
    <<interface>>
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
  }
  class TaskSign {
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
    // ...簽核任務專屬欄位與行為
  }
  class TaskComment {
    +id
    +docId
    +operatorId
    +createdAt
    +updatedAt
    +addFile()
    +removeFile()
    // ...評論任務專屬欄位與行為
  }
  class DocFile {
    +id
    +docId
    +taskId
    +fileName
    +fileType
    +fileSize
    +url
    +uploadedBy
    +createdAt
    +updatedAt
    +deletedAt
    +softDelete()
    +isValid()
  }

  Doc "1" --o "*" DocFile : files
  Doc "1" --o "*" Task
  Task <|-- TaskSign
  Task <|-- TaskComment
  TaskComment "1" --o "*" DocFile : files
```

## 5. 主要行為（方法）

- `Doc.addFile(file: FileAttachment)`：為文件新增附件（Draft 時 taskId 為作者的 TaskSign）
- `Doc.removeFile(fileId: string)`：軟刪除文件附件
- `TaskComment.addFile(file: FileAttachment)`：評論新增附件（taskId 為該 TaskComment 的 taskId）
- `TaskComment.removeFile(fileId: string)`：軟刪除評論附件
- `FileAttachment.softDelete()`：標記 deletedAt，不物理刪除
- `FileAttachment.isValid()`：檢查檔案規則

## 6. 領域事件

- **FileAttached**：檔案被夾帶至 Doc 或 TaskComment
- **FileSoftDeleted**：檔案被軟刪除

## 7. 設計說明

- **歸屬明確**：每個 FileAttachment 必須標記 docId 與 taskId，便於追蹤來源與歸屬。
- **Draft 階段**：文件建立（Draft）時，附件的 taskId 設為作者的 TaskSign。
- **評論附件**：TaskComment 建立時，附件的 taskId 設為該 TaskComment 的 taskId。
- **軟刪除策略**：一旦文件送出或評論建立，附件僅允許軟刪除（deletedAt 標記），不可物理刪除或修改，確保歷程可追溯。
- **依賴反轉**：FileAttachment 只依賴 Task（以 taskId 標記歸屬），不直接依賴 TaskSign、TaskComment 等具體型別，維持低耦合與彈性。
- **操作權限**：僅 Doc、TaskComment 可主動操作附件（addFile、removeFile），TaskSign 無此行為。
- **資料驗證**：FileAttachment 內建型態、大小等驗證邏輯，確保資料品質。
- **一致性與擴充性**：所有檔案操作皆以領域事件釋出，便於通知、審計、外部整合等擴充需求。
