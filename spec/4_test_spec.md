# 測試設計文件（Test Specification）

## 目錄

- [1. 邊界情境（Edge Cases & Exception Handling）](#1-邊界情境edge-cases--exception-handling)
- [2. 測試案例（Test Cases）](#2-測試案例test-cases)
- [3. 驗收標準（Acceptance Criteria）](#3-驗收標準acceptance-criteria)

---

## 1. 邊界情境（Edge Cases & Exception Handling）

### 認證/授權
- 登入時帳號不存在、密碼錯誤、帳號被停用
- JWT 過期、JWT 無效、未帶 JWT
- 註冊時帳號重複、密碼過短

### 文件建立/簽核
- 建立文件時缺少必填欄位
- 上傳檔案格式不符（如 exe）、超過數量/大小限制
- 非簽核人嘗試簽核、重複簽核同一文件
- 加簽人不存在、加簽自己
- 退回到不存在的節點、已結案文件再簽核/加簽/退回
- 刪除已送出/已結案文件
- 編輯他人文件

### 檔案上傳/下載
- 未授權上傳/下載
- 檔案不存在、檔案型別不符
- 下載他人文件的附件

### 查詢/分頁
- 查詢無資料
- 分頁超出範圍
- 條件查詢無結果

---

## 2. 測試案例（Test Cases）

### 2.1 認證/授權

| 編號 | 測試項目 | 步驟 | 預期結果 |
|------|----------|------|----------|
| TC-101 | 正確註冊 | 輸入新帳號密碼 | 註冊成功，回傳 userId |
| TC-102 | 重複註冊 | 輸入已存在帳號 | 回傳 USER_EXISTS 錯誤 |
| TC-103 | 正確登入 | 輸入正確帳密 | 回傳 JWT |
| TC-104 | 錯誤密碼 | 輸入錯誤密碼 | 回傳 INVALID_CREDENTIALS |
| TC-105 | JWT 過期 | 用過期 JWT 呼叫 API | 回傳 401 Unauthorized |
| TC-106 | 未帶 JWT | 呼叫需驗證 API | 回傳 401 Unauthorized |

### 2.2 文件建立/簽核

| 編號 | 測試項目 | 步驟 | 預期結果 |
|------|----------|------|----------|
| TC-201 | 建立請假單 | 填寫正確欄位送出 | 文件建立成功，狀態 draft |
| TC-202 | 缺少必填欄位 | 缺 reason | 回傳 400，提示缺欄位 |
| TC-203 | 上傳多檔案 | 上傳 2 個 pdf | 回傳 2 個 url |
| TC-204 | 上傳不支援格式 | 上傳 exe | 回傳 400，格式錯誤 |
| TC-205 | 非簽核人簽核 | 其他人呼叫 sign | 回傳 403 Forbidden |
| TC-206 | 重複簽核 | 已簽核再簽 | 回傳 400，狀態錯誤 |
| TC-207 | 加簽不存在員工 | assigneeId 不存在 | 回傳 404 Not Found |
| TC-208 | 退回不存在節點 | toOperatorId 不存在 | 回傳 404 Not Found |
| TC-209 | 已結案再簽核 | 文件已 approved | 回傳 400，狀態錯誤 |
| TC-210 | 編輯他人文件 | 非作者編輯 | 回傳 403 Forbidden |

### 2.3 檔案上傳/下載

| 編號 | 測試項目 | 步驟 | 預期結果 |
|------|----------|------|----------|
| TC-301 | 上傳合法檔案 | 上傳 pdf | 回傳 url |
| TC-302 | 未授權上傳 | 不帶 JWT | 回傳 401 Unauthorized |
| TC-303 | 下載合法檔案 | 下載自己文件附件 | 成功下載 |
| TC-304 | 下載他人檔案 | 下載他人文件附件 | 回傳 403 Forbidden |
| TC-305 | 檔案不存在 | 下載不存在 fileId | 回傳 404 Not Found |

### 2.4 查詢/分頁

| 編號 | 測試項目 | 步驟 | 預期結果 |
|------|----------|------|----------|
| TC-401 | 查詢有資料 | 查詢我的待簽 | 回傳文件清單 |
| TC-402 | 查詢無資料 | 查詢條件無結果 | 回傳空陣列 |
| TC-403 | 分頁超出範圍 | page > total | 回傳空陣列 |

### 2.5 整合/驗收測試

| 編號 | 測試項目 | 步驟 | 預期結果 |
|------|----------|------|----------|
| TC-501 | 完整簽核流程 | 建立→送出→多層簽核→加簽→退回→完成 | 文件狀態正確，歷程完整 |
| TC-502 | 多人加簽 | 主線+多加簽 | 所有人簽完主線才前進 |
| TC-503 | 文件刪除 | 刪除 draft 文件 | 文件刪除成功 |
| TC-504 | 刪除已送出文件 | 刪除 in_progress 文件 | 回傳 400，狀態錯誤 |

---

## 3. 驗收標準（Acceptance Criteria）

### 認證/授權
- 註冊、登入、JWT 驗證皆可正常運作，異常有明確錯誤訊息
- 未授權操作一律回傳 401/403

### 文件/簽核
- 文件可建立、送出、簽核、加簽、退回，狀態與歷程正確
- 非簽核人、非作者無法操作
- 已結案文件不可再簽核、加簽、退回、刪除

### 檔案
- 可上傳/下載多檔案，未授權或下載他人檔案會被拒絕
- 不支援格式、檔案不存在時有明確錯誤

### 查詢
- 查詢 API 支援分頁、條件查詢，無資料時回傳空陣列

### 異常處理
- 所有異常皆有明確錯誤訊息、狀態碼、錯誤碼
