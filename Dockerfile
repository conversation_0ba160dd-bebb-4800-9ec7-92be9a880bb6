# === Build stage ===
FROM node:20-alpine AS builder

WORKDIR /build

COPY package.json package-lock.json ./
COPY tsconfig.json tsconfig.build.json ./
COPY src/ ./src
COPY prisma/ ./prisma

RUN npm install
RUN npx prisma generate
RUN npm run build

# === Runtime stage ===
FROM node:20-alpine AS runner

WORKDIR /app
ENV NODE_ENV=production

COPY package.json package-lock.json ./
RUN npm install --production

# 只複製必要執行檔
COPY --from=builder /build/dist ./dist
# 複製 prisma 相關檔案
COPY --from=builder /build/node_modules/@prisma/client ./node_modules/@prisma/client
COPY --from=builder /build/node_modules/.prisma ./node_modules/.prisma
# 啟動 server
CMD ["npm", "run", "start"]
