const fs = require('fs');
const path = require('path');

const baseDir = process.cwd();
const migrationsDir = path.join(baseDir, 'prisma', 'migrations');

fs.readdirSync(migrationsDir).forEach((folder) => {
  const folderPath = path.join(migrationsDir, folder);
  const sqlPath = path.join(folderPath, 'migration.sql');

  if (!fs.existsSync(sqlPath)) return;

  let sql = fs.readFileSync(sqlPath, 'utf8');

  // 改掉所有 timestamp(3) 為 timestamptz(3)
  const updatedSql = sql.replace(/\bTIMESTAMP\((\d+)\)/g, 'TIMESTAMPTZ($1)');

  if (updatedSql !== sql) {
    fs.writeFileSync(sqlPath, updatedSql, 'utf8');
    console.log(`✅ Patched: ${sqlPath}`);
  } else {
    console.log(`ℹ️  Skipped (no TIMESTAMP found): ${sqlPath}`);
  }
});
