
const { execSync } = require("child_process");
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});
function run(command) {
    console.log(`\n> ${command}`);
    execSync(command, { stdio: "inherit", shell: true });
}

const showMenu = () => {
    console.log('\n=== 主選單 ===');
    console.log('1. 產生一個新migration');
    console.log('2. 先build prisma typing');
    console.log('3. migration up');
    console.log('4. migration down');
    console.log('5. 重設db')
    console.log('6. seed')
};
function mgGen() {
    run("npx prisma migrate dev")

    // run("npx prisma migrate dev --schema=prisma/bpm/schema.prisma")
    // run("npx prisma migrate dev --schema=prisma/wrs/schema.prisma")
}
function mgBuild() {
    run("npx prisma generate")

    // run("npx prisma generate --schema=prisma/bpm/schema.prisma")
    // run("npx prisma generate --schema=prisma/wrs/schema.prisma")
}
function mgUp() {
    run("npx prisma migrate deploy")
}

function mgDown() {
    run("npx prisma migrate diff \
        --from-schema-datasource prisma/schema.prisma \
        --to-schema-datamodel prisma/schema.prisma \
        --script > down.sql")
    run("npx prisma db execute --file=./down.sql --schema=prisma/schema.prisma")
    run(`npx prisma migrate resolve --rolled-back "<TIMESTAMP_migration-name>"`)
}

function mgReset() {
    run("rimraf prisma/migrations prisma/prisma") // 清除migrate資料
    run("npx prisma migrate reset --force") // 清除db資料
    run("npx prisma migrate dev --name init") // 重新migrate
    run("node scripts/toTz.js")
}
function mgSeed(){
    run("npx ts-node prisma/seed.ts")
}

const handleSelection = (choice) => {
    switch (choice.trim()) {
        case '1':
            mgGen();
            break;
        case '2':
            mgBuild();
            break;
        case '3':
            mgUp();
            break;
        case '4':
            mgDown();
            break;
        case '5':
            mgReset();
            break;
        case '6':
            mgSeed();
            break;
        default:
            console.log('\n❗ 無效選項，請重新輸入');
    }
    rl.close();  
    console.log('done') 
}
const ask = () => {
    showMenu();
    rl.question('\n請輸入選項編號：', handleSelection);
};

ask()