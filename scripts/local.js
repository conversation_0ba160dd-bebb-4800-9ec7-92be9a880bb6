const { execSync } = require("child_process");

function run(command) {
    console.log(`\n> ${command}`);
    execSync(command, { stdio: "inherit", shell: true });
}

console.log('請先確定Docker已經開啟...')
try {
    console.log('1. 啟動Postgres')
    run("docker-compose down db && docker-compose up --build db -d");

    console.log('2. 安裝依賴')
    run("npm install");

    console.log('3. migrate資料庫')
    process.env.DATABASE_URL = "postgres://user:ps@localhost:2345/dev"

    run("npx prisma migrate deploy") // migration

    console.log('4. 啟動伺服器(第一次建立資料庫請加跑一次seed)')
    run("npm run dev");

} catch (err) {
    console.log(err)
}

