# #!/bin/bash

# # 專案名稱（Docker image 名稱）
APP_NAME="bpm-backend"
ECR_URL="802133076138.dkr.ecr.us-east-1.amazonaws.com"
# # 嘗試取得 Git tag，沒有的話用 fallback
GIT_TAG=$(git describe --tags --abbrev=0 2>/dev/null)
GIT_COMMIT=$(git rev-parse --short HEAD)

# # 如果沒有 tag，就用 default
if [ -z "$GIT_TAG" ]; then
  GIT_TAG="dev"
fi

# ⚠️ 如果在 M 系列 Mac 上 build，請使用：
IMAGE_TAG=$APP_NAME:${GIT_TAG}-${GIT_COMMIT}
# docker buildx create --use --name mybuilder
docker buildx build --platform linux/amd64  -t ${IMAGE_TAG} . --load

echo ""
echo "✅ Built image with tags:"
echo "   $IMAGE_TAG"

echo "Add Aws Tag"
docker tag ${IMAGE_TAG} ${ECR_URL}/${IMAGE_TAG}

echo "Push image"
docker push ${ECR_URL}/${IMAGE_TAG}