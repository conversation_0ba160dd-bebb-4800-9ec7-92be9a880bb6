{"name": "bpm", "version": "1.0.0", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev -r tsconfig-paths/register  --respawn src/app.ts", "build": "rimraf ./build && tsc --project tsconfig.build.json && tsc-alias", "fe": "ts-node -r tsconfig-paths/register src/frontend/shell/server.ts", "mg": "node scripts/mg.js", "dev:compose": "docker-compose down && docker-compose up --build", "dev:local": "node scripts/local.js", "docker:push": "sh scripts/build.sh", "docker:login": " aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 802133076138.dkr.ecr.us-east-1.amazonaws.com", "mail": "npx ts-node  -r tsconfig-paths/register src/module/notify/playground.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/client-ses": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@google/genai": "^1.4.0", "@hono/node-server": "^1.14.1", "@hono/swagger-ui": "^0.5.1", "@hono/zod-openapi": "^0.19.4", "@hono/zod-validator": "^0.4.3", "@prisma/client": "^6.6.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "fs-extra": "^11.3.0", "hono": "^4.7.6", "hono-openapi": "^0.4.6", "hono-sessions": "^0.7.2", "jest": "^29.7.0", "multer": "^1.4.5-lts.2", "nanoid": "^5.1.5", "prisma": "^6.6.0", "qs": "^6.14.0", "zod-openapi": "^4.2.4"}, "devDependencies": {"@types/aws-sdk": "^0.0.42", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.15.3", "rimraf": "^6.0.1", "ts-jest": "^29.3.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}