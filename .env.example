# Exampleenvironmentvariables
PORT=3000
NODE_ENV=development
JWT_SECRET=your_jwt_secret
API_KEY=aaa
VERSION=v0
HOST_URL=http://localhost:3000
FRONTEND_URL=http://localhost:5175

ENABLE_MAIL=false
# Database
# DATABASE_PROVIDER="sqlite"
DATABASE_URL="**************************/dev" # "file:./prisma/dev.db"

# azure
TENANT_ID='aaa'
CLIENT_ID='bbb'
CLIENT_SECRET='ccc'
CALLBACK_URL='http://localhost:3000/auth/callback'
SENDER_EMAIL='<EMAIL>'

# aws s3
AWS_REGION=ap-northeast-1
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret
S3_BUCKET_NAME=your_bucket

# llm
LLM_API_KEY=your_llm_api_key
LLM_MODEL==gemini-2.0-flash

AWS_SES_ACCESS_KEY=your_ses_access_key
AWS_SES_SECRET_KEY=your_ses_secret_key