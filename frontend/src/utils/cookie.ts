import { deleteCookie, getCookie, setCookie } from "hono/cookie";

export function _getCookie(c: any, key: string) {
    return getCookie(c, key);
}
export function _setCookie(c: any, key: string, value: string) {
    return setCookie(c, key, value, {
        path: '/',
        httpOnly: false, // 設為 true 可防止 XSS 窺探 cookie, 但false才可以由前端(html檔)吃到cookie, 建議由server傳給前端cookie
        maxAge: 60 * 60, // 1hr
    })
}
export function _delCookie(c: any, key: string) {
    return deleteCookie(c, key);
}