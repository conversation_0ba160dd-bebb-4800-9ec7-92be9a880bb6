import { <PERSON>o } from "hono";
import axios from "axios";
import { _delCookie, _getCookie, _setCookie } from "../utils/cookie";
import { env } from "../env";

export function loginRouter() {
    const router = new Hono();

    router.get('/login', (c) => {
        const login = `https://login.microsoftonline.com/${env.TENANT_ID}/oauth2/v2.0/authorize?response_type=code&client_id=${env.CLIENT_ID}&redirect_uri=${env.CALLBACK_URL}&scope=user.read`;
        return c.redirect(login);
    });


    router.get('/auth/callback', async (c) => {
        // return c.json({ success: true });
        console.log(c.req.url)
        const { searchParams } = new URL(c.req.url);
        const code = searchParams.get('code');

        // to our token
        const result = await axios(
            {
                method: 'POST',
                url: `${env.API_URL}/v0/auth/azure`,
                data: { callbackCode: code }
            }
        )

        _setCookie(c, 'authToken', result.data.token )
        return c.redirect('/me')
    })

    router.get('/logout',async(c)=>{
        _delCookie(c, 'authToken')
        _delCookie(c, 'info')
        return c.redirect('/')
    })


    router.get('/me', async (c) => {
        const token = _getCookie(c, 'authToken');
        const res = await fetch(`${env.API_URL}/v0/auth/me`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                'Custom-Header': 'custom value'
            }
        })
        const result = await res.json()
        _setCookie(c, 'name', result.nick_name)
        return c.html(
            `<div>
              <h1>${JSON.stringify(result, null, 2)}</h1>
              <p> <a href="/">回首頁</a></p>
            </div>`
        );
    })
    return router
}