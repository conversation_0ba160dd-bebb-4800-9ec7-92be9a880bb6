import { Hono } from "hono";
import { readFileSync } from 'fs'
import { _getCookie } from "../utils/cookie";

export function uploadRouter() {
    const router = new Hono();
    
    router.get('/cookie', async (c) => {
        const name=_getCookie(c, 'name') as string
        const token = _getCookie(c, 'authToken');
        const html = readFileSync('./src/frontend/shell/cookie.html', 'utf-8')
        return c.html(html)
    });

    router.get('/upload', async (c) => {
        const html = readFileSync('./src/frontend/shell/upload.html', 'utf-8')
        return c.html(html)
    });
    return router
}