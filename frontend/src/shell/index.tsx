import { Hono } from "hono";
import { _getCookie } from "../utils/cookie";
import { env } from '../env'
import axios from "axios";
export function indexRouter() {
    const router = new Hono();
    router.get('/', async (c) => {
        const token = _getCookie(c, 'authToken');
        if (token) {
            const name = _getCookie!(c, 'name') as string
            return c.html(
                <div>
                    <h1>你好，{name || token}</h1>
                    <h1><a href="/logout">登出</a></h1>
                    <h1><a href="/my-apply">我的申請</a></h1>
                    <h1><a href="/upload">Upload</a></h1>
                    <h1><a href="/cookie">cookie</a></h1>
                    <h1><a href="/api">api</a></h1>
                    <iframe src="/my-apply" width="800" height="600" frameborder="0"></iframe>
                </div>
            );
        } else {
            return c.html(
                <>
                    <div>尚未登入, 請先登入</div>
                    <a href="/login">Login</a>
                </>
            )
        }
    });

    router.get('/api', async (c) => {
        const {data}=await axios({
            url:`${env.API_URL}/v0/health`,
            method:'GET'
        })
        console.log(data)
        return c.html(
            <>
                <div>{JSON.stringify(data)}</div>
                <p> <a href="/">回首頁</a></p>
            </>
        )
    })
    return router
}