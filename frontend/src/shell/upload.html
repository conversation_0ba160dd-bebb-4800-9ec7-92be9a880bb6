<input type="file" id="fileInput" />
<button id="uploadBtn">上傳</button>
<progress id="progressBar" value="0" max="100" style="width: 100%;"></progress>
<p id="user">載入中...</p>
<p> <a href="/">回首頁</a></p>
<script>
  function getCookie(name) {
    const value = `; ${document.cookie}`;
    console.log(value)
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      const result = parts.pop().split(';').shift();
      console.log({result})
      return result
    }
  }

  document.addEventListener("DOMContentLoaded", () => {
    const username = getCookie("name");
    const el = document.getElementById("user");
    el.textContent = username ? `你好，${username}` : "尚未登入";
  });

  const uploadBtn = document.getElementById('uploadBtn');
  const fileInput = document.getElementById('fileInput');
  const progressBar = document.getElementById('progressBar');

  uploadBtn.addEventListener('click', async () => {

    const file = fileInput.files[0];
    if (!file) return alert('請選擇檔案');

    try {
      // 1. 取得預簽 URL
      const opt = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getCookie('authToken')}`,
        },
        body: JSON.stringify({ fileName: file.name, fileType: file.type, docId: "d_001", taskId: null }),

      }

      const res = await fetch('http://localhost:3000/v0/files/generate-presigned-url', opt);

      if (!res.ok) throw new Error('取得預簽 URL 失敗: ' + await res.text());

      const { data: { url, key } }= await res.json();
      console.log('取得預簽 URL：', url);

      // 2. 使用 XMLHttpRequest 上傳
      const xhr = new XMLHttpRequest();
      xhr.open('PUT', url, true);
      xhr.setRequestHeader('Content-Type', file.type);

      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded / e.total) * 100);
          progressBar.value = percent;
        }
      });

      xhr.onload = async () => {
        if (!file) return alert('❗ 無法讀取原始檔案資訊');
        if (xhr.status >= 200 && xhr.status < 300) {
          const body = JSON.stringify({
            id: key,
            docId: 'd_001',
            taskId: null,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,//byte(default is byte)
          })
          await fetch('http://localhost:3000/v0/files/notify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${getCookie('authToken')}`,
            },
            body: body,
          });
          alert('✅ 上傳成功！\nS3 key: ' + key);

        } else {
          alert(`❌ 上傳失敗 (${xhr.status}): ${xhr.responseText}`);
        }
      };

      xhr.onerror = () => {
        alert('❌ 上傳錯誤');
      };

      xhr.send(file);
    } catch (err) {
      console.error(err);
      alert('❌ 錯誤：' + err.message);
    }
  });
</script>