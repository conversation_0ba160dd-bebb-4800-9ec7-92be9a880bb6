import { Hono } from "hono";
import { _getCookie } from "../utils/cookie";
import { env } from "../env";

export function myApplyRouter() {
    const router = new Hono();
    router.get('/my-apply', async (c) => {
        console.log(c.req.url)
        const token = _getCookie(c, 'authToken');
        const url = `${env.API_URL}/v0/docs/user-doc/FC0065/my-apply`
        const opt = {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            }
            // const  all = await fetch(`${url}?isNotDraft=true`, opt)
        }
        const all = await fetch(`${url}`, opt)

        const all_data = await all.json()
        return c.html(
            <>
                {JSON.stringify(all_data.data)} || []
                <p> <a href="..">回首頁</a></p>
            </>
        )
    })
    return router
}