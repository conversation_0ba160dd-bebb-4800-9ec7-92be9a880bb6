<p id="user">載入中...</p>
<p> <a href="/">回首頁</a></p>


<script>
    function getCookie(name) {
        console.log("All cookies", document.cookie)
        const value = `; ${document.cookie}`;
        console.log(value)
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            const result = parts.pop().split(';').shift();
            console.log({ result })
            return result
        }
    }

    document.addEventListener("DOMContentLoaded", () => {
        const username = getCookie("name");
        const el = document.getElementById("user");
        el.textContent = username ? `你好，${username}` : "尚未登入";
    });
</script>