
import { Hono } from "hono";
import { serve } from '@hono/node-server';
import { indexRouter } from "./shell";
import { loginRouter } from "./shell/login";
import { uploadRouter } from "./shell/upload";
import { myApplyRouter } from "./shell/myApply";

export function MainSSR() {
    const router = new Hono();
    router.route('/', indexRouter())
    router.route('/', loginRouter())
    router.route('/', uploadRouter())
    router.route('/', myApplyRouter())
    
    serve(
        { fetch: router.fetch, port: 5175, },
        () => {
            console.log('frontend running at http://localhost:5175')
        }
    )
}

MainSSR()
