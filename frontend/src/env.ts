import dotenv from 'dotenv';

dotenv.config();

function requireEnv(key: string): string {
    const value = process.env[key];
    if (!value) {
        console.error(`Missing required environment variable: ${key}`);
    }
    return value || '';
}

export const env = {
    TENANT_ID: requireEnv('TENANT_ID'),
    CLIENT_ID: requireEnv('CLIENT_ID'),
    CALLBACK_URL: requireEnv('CALLBACK_URL'),
    API_URL: requireEnv('API_URL')
}

console.log(env)